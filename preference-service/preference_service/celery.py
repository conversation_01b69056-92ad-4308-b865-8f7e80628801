"""
Celery configuration for preference_service.
"""
import os
from celery import Celery
from django.conf import settings

# Set the default Django settings module for the 'celery' program.
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'preference_service.settings')

app = Celery('preference_service')

# Using a string here means the worker doesn't have to serialize
# the configuration object to child processes.
app.config_from_object('django.conf:settings', namespace='CELERY')

# Load task modules from all registered Django apps.
app.autodiscover_tasks()

# Celery beat schedule for periodic tasks
app.conf.beat_schedule = {
    'validate-preferences': {
        'task': 'preferences.tasks.validate_all_preferences',
        'schedule': 3600.0,  # Run every hour
    },
    'sync-user-data': {
        'task': 'preferences.tasks.sync_user_data',
        'schedule': 300.0,  # Run every 5 minutes
    },
    'cleanup-old-preferences': {
        'task': 'preferences.tasks.cleanup_old_preference_history',
        'schedule': 86400.0,  # Run daily
    },
    'send-preference-reminders': {
        'task': 'preferences.tasks.send_preference_reminders',
        'schedule': 21600.0,  # Run every 6 hours
    },
}

app.conf.timezone = 'UTC'


@app.task(bind=True)
def debug_task(self):
    print(f'Request: {self.request!r}')
