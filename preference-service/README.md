# Preference Collection Service

The Preference Collection Service is a comprehensive microservice for collecting, managing, and validating lecturer and student preferences in the Timetable Management System. It provides real-time updates via WebSockets, advanced validation, and seamless integration with other services.

## Features

### 🎯 **Preference Collection**
- **Lecturer Preferences**: Available time slots, preferred courses, teaching constraints, workload preferences
- **Student Preferences**: Course selections with priorities, time constraints, study preferences
- **Flexible Configuration**: Customizable preference fields and validation rules
- **Academic Period Support**: Multi-semester and multi-year preference management

### ⚡ **Real-time Updates**
- **WebSocket Support**: Live preference updates and notifications
- **Auto-save**: Automatic saving of preference changes
- **Conflict Detection**: Real-time conflict detection and alerts
- **Progress Tracking**: Live progress updates for bulk operations

### 🔍 **Advanced Validation**
- **Configurable Rules**: Flexible validation rule engine
- **Conflict Detection**: Automatic detection of scheduling conflicts
- **Constraint Validation**: Workload, credit, and time constraint validation
- **Custom Logic**: Support for custom validation logic

### 📊 **Analytics & Reporting**
- **Preference Analytics**: Comprehensive preference statistics and trends
- **Completion Tracking**: Real-time completion rate monitoring
- **Popular Choices**: Course and time slot popularity analysis
- **Export Capabilities**: Data export for external analysis

### 🔄 **Integration**
- **User Service Integration**: Seamless user data synchronization
- **Timetable Service**: Direct preference submission to timetable generation
- **Authentication Service**: JWT-based authentication and authorization
- **Audit Logging**: Comprehensive audit trails for all operations

## Architecture

### Technology Stack
- **Framework**: Django REST Framework
- **Database**: MongoDB (shared with other services)
- **Real-time**: Django Channels with Redis
- **Task Queue**: Celery with Redis
- **Authentication**: JWT tokens from Authentication Service
- **API Documentation**: Swagger/OpenAPI

### Key Components

#### Models
- **LecturerPreference**: Teaching preferences with courses and time slots
- **StudentPreference**: Course selections with priorities and constraints
- **ValidationRule**: Configurable validation rules
- **ConflictDetection**: Automatic conflict detection and resolution
- **PreferenceSubmission**: Batch submissions to timetable generation

#### Services
- **Preference Management**: CRUD operations with role-based permissions
- **Real-time Updates**: WebSocket-based live updates
- **Validation Engine**: Comprehensive preference validation
- **Conflict Detection**: Automatic scheduling conflict detection

#### Security
- **JWT Authentication**: Token-based authentication from auth service
- **Role-based Permissions**: Granular permission system
- **Audit Logging**: Complete audit trails
- **Input Validation**: Strict validation of all inputs

## API Endpoints

### Lecturer Preferences
```
GET    /api/preferences/lecturer-preferences/           # List lecturer preferences
POST   /api/preferences/lecturer-preferences/           # Create lecturer preference
GET    /api/preferences/lecturer-preferences/{id}/      # Get lecturer preference
PATCH  /api/preferences/lecturer-preferences/{id}/      # Update lecturer preference
DELETE /api/preferences/lecturer-preferences/{id}/      # Delete lecturer preference
POST   /api/preferences/lecturer-preferences/{id}/submit/    # Submit for approval
POST   /api/preferences/lecturer-preferences/{id}/approve/   # Approve (admin only)
POST   /api/preferences/lecturer-preferences/{id}/reject/    # Reject (admin only)
GET    /api/preferences/lecturer-preferences/{id}/history/   # Get change history
GET    /api/preferences/lecturer-preferences/my-preferences/ # Get current user's preferences
GET    /api/preferences/lecturer-preferences/statistics/     # Get statistics (admin only)
```

### Student Preferences
```
GET    /api/preferences/student-preferences/            # List student preferences
POST   /api/preferences/student-preferences/            # Create student preference
GET    /api/preferences/student-preferences/{id}/       # Get student preference
PATCH  /api/preferences/student-preferences/{id}/       # Update student preference
DELETE /api/preferences/student-preferences/{id}/       # Delete student preference
POST   /api/preferences/student-preferences/{id}/submit/     # Submit for approval
GET    /api/preferences/student-preferences/my-preferences/  # Get current user's preferences
```

### Preference Submissions
```
GET    /api/preferences/submissions/                    # List submissions (admin only)
POST   /api/preferences/submissions/                    # Create submission (admin only)
GET    /api/preferences/submissions/{id}/               # Get submission details
POST   /api/preferences/submissions/{id}/reprocess/     # Reprocess submission
GET    /api/preferences/submissions/latest/             # Get latest submissions
```

### Analytics & Summary
```
GET    /api/preferences/summary/                        # Get preference summary
GET    /api/preferences/analytics/                      # Get preference analytics (admin only)
```

### System Endpoints
```
GET    /api/health/                                     # Health check
GET    /api/info/                                       # Service information
GET    /api/statistics/                                 # System statistics
GET    /api/time-slots/                                 # Available time slots
```

## WebSocket Endpoints

### User Preferences
```
ws://localhost:8003/ws/preferences/
```
**Events:**
- `preference_update`: Real-time preference updates
- `validation_result`: Validation results and errors
- `conflict_detected`: Scheduling conflicts
- `submission_status`: Submission progress updates

### Admin Dashboard
```
ws://localhost:8003/ws/admin/
```
**Events:**
- `system_alert`: System-wide alerts
- `bulk_operation_update`: Bulk operation progress
- `validation_summary`: System validation status

## Role-Based Access Control

### Superadmin
- ✅ Full access to all preferences and operations
- ✅ System configuration and rule management
- ✅ Advanced analytics and reporting

### Admin
- ✅ View and manage all preferences
- ✅ Approve/reject submitted preferences
- ✅ Access to analytics and bulk operations
- ❌ Cannot modify system configuration

### Lecturer
- ✅ Create and manage own preferences
- ✅ Submit preferences for approval
- ✅ View own preference history
- ❌ Cannot access other users' preferences

### Student
- ✅ Create and manage own preferences
- ✅ Submit preferences for approval
- ✅ View own preference history
- ❌ Cannot access other users' preferences

## Development

### Setup
```bash
# Install dependencies
pip install -r requirements.txt

# Run migrations
python manage.py migrate

# Create time slots
python manage.py shell
>>> from core.models import TimeSlot
>>> # Create default time slots

# Run development server
python manage.py runserver 0.0.0.0:8003
```

### Testing
```bash
# Run all tests
python manage.py test

# Run specific test modules
python manage.py test preferences.tests
python manage.py test validation.tests

# Run with coverage
coverage run --source='.' manage.py test
coverage report
```

### Celery Workers
```bash
# Start Celery worker
celery -A preference_service worker --loglevel=info

# Start Celery beat scheduler
celery -A preference_service beat --loglevel=info

# Monitor Celery tasks
celery -A preference_service flower
```

### WebSocket Development
```bash
# Start with Daphne for WebSocket support
daphne -b 0.0.0.0 -p 8003 preference_service.asgi:application
```

## Configuration

### Environment Variables
- `MONGODB_URI`: MongoDB connection string
- `REDIS_URL`: Redis connection string
- `USER_SERVICE_URL`: User Management Service URL
- `AUTH_SERVICE_URL`: Authentication Service URL
- `TIMETABLE_SERVICE_URL`: Timetable Generation Service URL
- `INTERNAL_SERVICE_TOKEN`: Internal service authentication token
- `JWT_SECRET_KEY`: JWT signing key
- `SECRET_KEY`: Django secret key

### Preference Settings
- `PREFERENCE_VALIDATION_ENABLED`: Enable/disable validation
- `PREFERENCE_CONFLICT_DETECTION`: Enable/disable conflict detection
- `PREFERENCE_AUTO_SAVE_INTERVAL`: Auto-save interval in seconds
- `PREFERENCE_HISTORY_RETENTION_DAYS`: History retention period

### Time Configuration
- `DEFAULT_TIME_SLOT_DURATION`: Default time slot duration in minutes
- `WORKING_HOURS_START`: Working hours start time
- `WORKING_HOURS_END`: Working hours end time
- `WORKING_DAYS`: List of working days

## Integration

### User Management Service
- **User Synchronization**: Automatic user data sync
- **Profile Integration**: Extended profile information
- **Role Management**: Consistent role-based permissions

### Authentication Service
- **JWT Validation**: Token validation and user authentication
- **Session Management**: Secure session handling
- **Permission Sync**: Role and permission synchronization

### Timetable Generation Service
- **Preference Submission**: Automated preference data submission
- **Status Updates**: Real-time submission status updates
- **Result Integration**: Timetable generation result handling

## Monitoring & Logging

### Health Checks
- `/api/health/`: Service health status
- Database connectivity checks
- Redis connectivity checks
- WebSocket functionality checks

### Metrics
- Preference submission rates
- Validation success rates
- Conflict detection rates
- WebSocket connection counts
- API response times

### Logging
- All preference operations are logged
- Validation results and conflicts
- WebSocket connection events
- Error tracking and alerting
- Performance monitoring

## Security Considerations

1. **Authentication**: All endpoints require valid JWT tokens
2. **Authorization**: Role-based access control for all operations
3. **Input Validation**: Strict validation of all inputs
4. **Audit Logging**: Comprehensive audit trails
5. **Data Protection**: Sensitive data handling and encryption
6. **Rate Limiting**: API rate limiting to prevent abuse
7. **WebSocket Security**: Secure WebSocket connections with authentication

## Future Enhancements

1. **Advanced Templates**: Preference templates for common patterns
2. **Bulk Operations**: Enhanced bulk import/export capabilities
3. **Machine Learning**: Preference prediction and recommendations
4. **Mobile API**: Optimized mobile API endpoints
5. **Advanced Analytics**: Predictive analytics and insights
6. **Integration APIs**: Enhanced third-party integration capabilities
