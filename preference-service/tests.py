"""
Comprehensive tests for the preference collection service.
"""
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
from unittest.mock import patch, Mock
from django.utils import timezone
from datetime import time, timedelta

from core.models import User, Course, TimeSlot, AuditLog
from preferences.models import (
    LecturerPreference, StudentPreference, StudentCoursePreference,
    PreferenceHistory, PreferenceSubmission
)
from validation.models import ValidationResult, ValidationRule, ConflictDetection
from templates.models import PreferenceTemplate, BulkPreferenceOperation


class PreferenceServiceTestCase(APITestCase):
    """Base test case with common setup."""

    def setUp(self):
        """Set up test data."""
        # Create users
        self.admin_user = User.objects.create(
            email='<EMAIL>',
            username='admin',
            first_name='Admin',
            last_name='User',
            role_name='admin',
            is_verified=True
        )

        self.lecturer_user = User.objects.create(
            email='<EMAIL>',
            username='lecturer',
            first_name='Test',
            last_name='Lecturer',
            role_name='lecturer',
            department='Computer Science',
            employee_id='EMP001',
            is_verified=True
        )

        self.student_user = User.objects.create(
            email='<EMAIL>',
            username='student',
            first_name='Test',
            last_name='Student',
            role_name='student',
            department='Computer Science',
            student_id='STU001',
            year_of_study=2,
            is_verified=True
        )

        # Create courses
        self.course1 = Course.objects.create(
            code='CS101',
            name='Introduction to Programming',
            department='Computer Science',
            credits=3,
            semester='Fall',
            year_level=1
        )

        self.course2 = Course.objects.create(
            code='CS201',
            name='Data Structures',
            department='Computer Science',
            credits=4,
            semester='Fall',
            year_level=2
        )

        # Create time slots
        self.time_slot1 = TimeSlot.objects.create(
            day_of_week='monday',
            start_time=time(9, 0),
            end_time=time(10, 30),
            duration_minutes=90
        )

        self.time_slot2 = TimeSlot.objects.create(
            day_of_week='wednesday',
            start_time=time(14, 0),
            end_time=time(15, 30),
            duration_minutes=90
        )


class CoreViewsTestCase(PreferenceServiceTestCase):
    """Test cases for core views."""

    def test_health_check(self):
        """Test health check endpoint."""
        url = reverse('core:health-check')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['status'], 'healthy')
        self.assertEqual(response.data['service'], 'preference-collection-service')

    def test_service_info(self):
        """Test service info endpoint."""
        url = reverse('core:service-info')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('service', response.data)
        self.assertIn('features', response.data)

    def test_statistics_view_authenticated(self):
        """Test statistics view with authentication."""
        self.client.force_authenticate(user=self.admin_user)
        url = reverse('core:statistics')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('users', response.data)
        self.assertIn('preferences', response.data)

    def test_statistics_view_unauthenticated(self):
        """Test statistics view without authentication."""
        url = reverse('core:statistics')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_time_slots_view(self):
        """Test time slots endpoint."""
        self.client.force_authenticate(user=self.lecturer_user)
        url = reverse('core:time-slots')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('time_slots', response.data)


class LecturerPreferenceTestCase(PreferenceServiceTestCase):
    """Test cases for lecturer preference management."""

    def test_create_lecturer_preference(self):
        """Test creating lecturer preference."""
        self.client.force_authenticate(user=self.lecturer_user)
        url = reverse('preferences:lecturer-preference-list')
        data = {
            'academic_year': '2023-2024',
            'semester': 'Fall',
            'preferred_workload': 'normal',
            'max_courses_per_semester': 3,
            'max_hours_per_week': 18,
            'preferred_course_ids': [str(self.course1.id), str(self.course2.id)],
            'available_time_slot_ids': [str(self.time_slot1.id), str(self.time_slot2.id)],
            'comments': 'Test preference'
        }
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['academic_year'], '2023-2024')
        self.assertEqual(response.data['semester'], 'Fall')

    def test_lecturer_preference_list_access(self):
        """Test lecturer preference list access permissions."""
        # Admin can access all
        self.client.force_authenticate(user=self.admin_user)
        url = reverse('preferences:lecturer-preference-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Lecturer can access own
        self.client.force_authenticate(user=self.lecturer_user)
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Student cannot access
        self.client.force_authenticate(user=self.student_user)
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 0)

    def test_submit_lecturer_preference(self):
        """Test submitting lecturer preference."""
        # Create preference
        preference = LecturerPreference.objects.create(
            user=self.lecturer_user,
            academic_year='2023-2024',
            semester='Fall',
            max_courses_per_semester=3,
            max_hours_per_week=18
        )
        preference.preferred_courses.add(self.course1)
        preference.available_time_slots.add(self.time_slot1)

        self.client.force_authenticate(user=self.lecturer_user)
        url = reverse('preferences:lecturer-preference-submit', kwargs={'pk': preference.id})
        response = self.client.post(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        preference.refresh_from_db()
        self.assertEqual(preference.status, 'submitted')
        self.assertIsNotNone(preference.submitted_at)

    def test_approve_lecturer_preference(self):
        """Test approving lecturer preference (admin only)."""
        # Create submitted preference
        preference = LecturerPreference.objects.create(
            user=self.lecturer_user,
            academic_year='2023-2024',
            semester='Fall',
            status='submitted',
            max_courses_per_semester=3,
            max_hours_per_week=18,
            submitted_at=timezone.now()
        )

        self.client.force_authenticate(user=self.admin_user)
        url = reverse('preferences:lecturer-preference-approve', kwargs={'pk': preference.id})
        response = self.client.post(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        preference.refresh_from_db()
        self.assertEqual(preference.status, 'approved')
        self.assertIsNotNone(preference.approved_at)
        self.assertEqual(preference.approved_by, self.admin_user)

    def test_lecturer_preference_history(self):
        """Test preference history tracking."""
        preference = LecturerPreference.objects.create(
            user=self.lecturer_user,
            academic_year='2023-2024',
            semester='Fall',
            max_courses_per_semester=3,
            max_hours_per_week=18
        )

        self.client.force_authenticate(user=self.lecturer_user)
        url = reverse('preferences:lecturer-preference-history', kwargs={'pk': preference.id})
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)


class StudentPreferenceTestCase(PreferenceServiceTestCase):
    """Test cases for student preference management."""

    def test_create_student_preference(self):
        """Test creating student preference."""
        self.client.force_authenticate(user=self.student_user)
        url = reverse('preferences:student-preference-list')
        data = {
            'academic_year': '2023-2024',
            'semester': 'Fall',
            'max_courses_per_semester': 5,
            'max_credits_per_semester': 18,
            'course_preferences_data': [
                {
                    'course_id': str(self.course1.id),
                    'priority': 1,
                    'is_required': True,
                    'notes': 'Required course'
                },
                {
                    'course_id': str(self.course2.id),
                    'priority': 2,
                    'is_required': False,
                    'notes': 'Elective'
                }
            ],
            'preferred_time_slot_ids': [str(self.time_slot1.id)],
            'prefer_morning_classes': True,
            'comments': 'Test student preference'
        }
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['academic_year'], '2023-2024')

    def test_student_preference_validation(self):
        """Test student preference validation."""
        self.client.force_authenticate(user=self.student_user)
        url = reverse('preferences:student-preference-list')
        data = {
            'academic_year': '2023-2024',
            'semester': 'Fall',
            'max_courses_per_semester': 5,
            'max_credits_per_semester': 5,  # Too low for the courses
            'course_preferences_data': [
                {
                    'course_id': str(self.course1.id),
                    'priority': 1,
                    'is_required': True
                },
                {
                    'course_id': str(self.course2.id),
                    'priority': 2,
                    'is_required': False
                }
            ]
        }
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('Total credits', str(response.data))

    def test_submit_student_preference(self):
        """Test submitting student preference."""
        # Create preference
        preference = StudentPreference.objects.create(
            user=self.student_user,
            academic_year='2023-2024',
            semester='Fall',
            max_courses_per_semester=5,
            max_credits_per_semester=18
        )
        StudentCoursePreference.objects.create(
            student_preference=preference,
            course=self.course1,
            priority=1,
            is_required=True
        )

        self.client.force_authenticate(user=self.student_user)
        url = reverse('preferences:student-preference-submit', kwargs={'pk': preference.id})
        response = self.client.post(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        preference.refresh_from_db()
        self.assertEqual(preference.status, 'submitted')


class PreferenceSubmissionTestCase(PreferenceServiceTestCase):
    """Test cases for preference submissions."""

    def test_create_preference_submission(self):
        """Test creating preference submission (admin only)."""
        self.client.force_authenticate(user=self.admin_user)
        url = reverse('preferences:preference-submission-list')
        data = {
            'academic_year': '2023-2024',
            'semester': 'Fall'
        }
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['academic_year'], '2023-2024')
        self.assertEqual(response.data['status'], 'pending')

    def test_preference_submission_access_control(self):
        """Test preference submission access control."""
        # Only admin can create submissions
        self.client.force_authenticate(user=self.lecturer_user)
        url = reverse('preferences:preference-submission-list')
        data = {
            'academic_year': '2023-2024',
            'semester': 'Fall'
        }
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    @patch('preferences.tasks.process_preference_submission.delay')
    def test_reprocess_submission(self, mock_task):
        """Test reprocessing failed submission."""
        submission = PreferenceSubmission.objects.create(
            academic_year='2023-2024',
            semester='Fall',
            status='failed',
            submitted_by=self.admin_user
        )

        self.client.force_authenticate(user=self.admin_user)
        url = reverse('preferences:preference-submission-reprocess', kwargs={'pk': submission.id})
        response = self.client.post(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        submission.refresh_from_db()
        self.assertEqual(submission.status, 'pending')
        mock_task.assert_called_once()


class PreferenceSummaryTestCase(PreferenceServiceTestCase):
    """Test cases for preference summary views."""

    def test_preference_summary_lecturer(self):
        """Test preference summary for lecturer."""
        # Create lecturer preference
        LecturerPreference.objects.create(
            user=self.lecturer_user,
            academic_year='2023-2024',
            semester='Fall',
            status='submitted',
            max_courses_per_semester=3,
            max_hours_per_week=18
        )

        self.client.force_authenticate(user=self.lecturer_user)
        url = reverse('preferences:preference-summary')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('preferences', response.data)
        self.assertIn('lecturer', response.data['preferences'])

    def test_preference_summary_student(self):
        """Test preference summary for student."""
        # Create student preference
        preference = StudentPreference.objects.create(
            user=self.student_user,
            academic_year='2023-2024',
            semester='Fall',
            status='submitted',
            max_courses_per_semester=5,
            max_credits_per_semester=18
        )
        StudentCoursePreference.objects.create(
            student_preference=preference,
            course=self.course1,
            priority=1
        )

        self.client.force_authenticate(user=self.student_user)
        url = reverse('preferences:preference-summary')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('preferences', response.data)
        self.assertIn('student', response.data['preferences'])

    def test_preference_analytics_admin(self):
        """Test preference analytics (admin only)."""
        self.client.force_authenticate(user=self.admin_user)
        url = reverse('preferences:preference-analytics')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('lecturer_analytics', response.data)
        self.assertIn('student_analytics', response.data)


class ModelTestCase(PreferenceServiceTestCase):
    """Test cases for model functionality."""

    def test_lecturer_preference_model(self):
        """Test lecturer preference model methods."""
        preference = LecturerPreference.objects.create(
            user=self.lecturer_user,
            academic_year='2023-2024',
            semester='Fall',
            max_courses_per_semester=3,
            max_hours_per_week=18
        )
        preference.preferred_courses.add(self.course1, self.course2)
        preference.available_time_slots.add(self.time_slot1)

        # Test string representation
        self.assertIn(self.lecturer_user.get_full_name(), str(preference))

        # Test course codes
        course_codes = preference.get_preferred_course_codes()
        self.assertIn('CS101', course_codes)
        self.assertIn('CS201', course_codes)

        # Test time slots by day
        slots_by_day = preference.get_available_time_slots_by_day()
        self.assertIn('monday', slots_by_day)

    def test_student_preference_model(self):
        """Test student preference model methods."""
        preference = StudentPreference.objects.create(
            user=self.student_user,
            academic_year='2023-2024',
            semester='Fall',
            max_courses_per_semester=5,
            max_credits_per_semester=18
        )
        
        StudentCoursePreference.objects.create(
            student_preference=preference,
            course=self.course1,
            priority=1,
            is_required=True
        )
        StudentCoursePreference.objects.create(
            student_preference=preference,
            course=self.course2,
            priority=2,
            is_required=False
        )

        # Test total credits calculation
        total_credits = preference.get_total_preferred_credits()
        self.assertEqual(total_credits, 7)  # 3 + 4 credits

        # Test courses by priority
        courses_by_priority = preference.get_preferred_courses_by_priority()
        self.assertIn(1, courses_by_priority)
        self.assertIn(2, courses_by_priority)

    def test_time_slot_overlap(self):
        """Test time slot overlap detection."""
        slot1 = TimeSlot.objects.create(
            day_of_week='monday',
            start_time=time(9, 0),
            end_time=time(10, 30),
            duration_minutes=90
        )
        
        slot2 = TimeSlot.objects.create(
            day_of_week='monday',
            start_time=time(10, 0),
            end_time=time(11, 30),
            duration_minutes=90
        )
        
        slot3 = TimeSlot.objects.create(
            day_of_week='tuesday',
            start_time=time(9, 0),
            end_time=time(10, 30),
            duration_minutes=90
        )

        # Test overlap detection
        self.assertTrue(slot1.overlaps_with(slot2))  # Same day, overlapping times
        self.assertFalse(slot1.overlaps_with(slot3))  # Different days

    def test_user_role_methods(self):
        """Test user role checking methods."""
        self.assertTrue(self.admin_user.is_admin())
        self.assertFalse(self.admin_user.is_lecturer())
        self.assertFalse(self.admin_user.is_student())

        self.assertFalse(self.lecturer_user.is_admin())
        self.assertTrue(self.lecturer_user.is_lecturer())
        self.assertFalse(self.lecturer_user.is_student())

        self.assertFalse(self.student_user.is_admin())
        self.assertFalse(self.student_user.is_lecturer())
        self.assertTrue(self.student_user.is_student())


class ValidationTestCase(PreferenceServiceTestCase):
    """Test cases for preference validation."""

    def test_validation_rule_creation(self):
        """Test creating validation rules."""
        rule = ValidationRule.objects.create(
            name='minimum_courses',
            description='Minimum number of courses required',
            rule_type='lecturer',
            severity='error',
            rule_config={'min_courses': 2},
            validation_logic='# Custom validation logic',
            error_message_template='At least {min_courses} courses required',
            created_by=self.admin_user
        )
        
        self.assertEqual(rule.name, 'minimum_courses')
        self.assertEqual(rule.rule_type, 'lecturer')
        self.assertTrue(rule.is_enabled)

    def test_validation_result_creation(self):
        """Test creating validation results."""
        preference = LecturerPreference.objects.create(
            user=self.lecturer_user,
            academic_year='2023-2024',
            semester='Fall',
            max_courses_per_semester=3,
            max_hours_per_week=18
        )

        result = ValidationResult.objects.create(
            preference_type='lecturer',
            preference_id=preference.id,
            user=self.lecturer_user,
            status='invalid',
            message='Validation failed',
            details={'error': 'Test error'}
        )
        
        self.assertEqual(result.preference_type, 'lecturer')
        self.assertEqual(result.status, 'invalid')
        self.assertFalse(result.is_resolved)

    def test_conflict_detection_creation(self):
        """Test creating conflict detections."""
        conflict = ConflictDetection.objects.create(
            conflict_type='time_overlap',
            severity='high',
            description='Time slot overlap detected',
            affected_preferences=[
                {'type': 'lecturer', 'id': 'test-id-1'},
                {'type': 'lecturer', 'id': 'test-id-2'}
            ],
            conflict_data={'details': 'Test conflict'},
            suggested_resolutions=['Resolution 1', 'Resolution 2']
        )
        
        self.assertEqual(conflict.conflict_type, 'time_overlap')
        self.assertEqual(conflict.severity, 'high')
        self.assertFalse(conflict.is_resolved)


class WebSocketTestCase(PreferenceServiceTestCase):
    """Test cases for WebSocket functionality."""

    def test_websocket_consumer_import(self):
        """Test that WebSocket consumers can be imported."""
        from websockets.consumers import PreferenceConsumer, AdminConsumer
        self.assertTrue(PreferenceConsumer)
        self.assertTrue(AdminConsumer)

    def test_websocket_routing_import(self):
        """Test that WebSocket routing can be imported."""
        from websockets.routing import websocket_urlpatterns
        self.assertTrue(websocket_urlpatterns)
        self.assertEqual(len(websocket_urlpatterns), 2)
