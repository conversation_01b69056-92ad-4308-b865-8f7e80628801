"""
Custom permissions for the preference collection service.
"""
from rest_framework import permissions


class IsOwnerOrAdmin(permissions.BasePermission):
    """
    Permission that allows owners of an object or admins to access it.
    """

    def has_object_permission(self, request, view, obj):
        # Admin users have full access
        if hasattr(request.user, 'is_admin') and request.user.is_admin():
            return True
        
        # Check if the object has a user field and if it matches the request user
        if hasattr(obj, 'user'):
            return obj.user == request.user
        
        # For objects that don't have a user field, check if they are the user themselves
        if hasattr(obj, 'id') and hasattr(request.user, 'id'):
            return obj.id == request.user.id
        
        return False


class IsAdminUser(permissions.BasePermission):
    """
    Permission that only allows admin users.
    """

    def has_permission(self, request, view):
        return (
            request.user and 
            request.user.is_authenticated and 
            hasattr(request.user, 'is_admin') and 
            request.user.is_admin()
        )


class IsLecturerUser(permissions.BasePermission):
    """
    Permission that only allows lecturer users.
    """

    def has_permission(self, request, view):
        return (
            request.user and 
            request.user.is_authenticated and 
            hasattr(request.user, 'is_lecturer') and 
            request.user.is_lecturer()
        )


class IsStudentUser(permissions.BasePermission):
    """
    Permission that only allows student users.
    """

    def has_permission(self, request, view):
        return (
            request.user and 
            request.user.is_authenticated and 
            hasattr(request.user, 'is_student') and 
            request.user.is_student()
        )


class IsLecturerOrAdmin(permissions.BasePermission):
    """
    Permission that allows lecturers or admin users.
    """

    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated):
            return False
        
        return (
            (hasattr(request.user, 'is_lecturer') and request.user.is_lecturer()) or
            (hasattr(request.user, 'is_admin') and request.user.is_admin())
        )


class IsStudentOrAdmin(permissions.BasePermission):
    """
    Permission that allows students or admin users.
    """

    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated):
            return False
        
        return (
            (hasattr(request.user, 'is_student') and request.user.is_student()) or
            (hasattr(request.user, 'is_admin') and request.user.is_admin())
        )


class CanSubmitPreferences(permissions.BasePermission):
    """
    Permission that allows users to submit preferences based on their role.
    """

    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated):
            return False
        
        # Admins can always submit preferences
        if hasattr(request.user, 'is_admin') and request.user.is_admin():
            return True
        
        # Check if user can submit preferences based on the view
        if 'lecturer' in view.__class__.__name__.lower():
            return hasattr(request.user, 'is_lecturer') and request.user.is_lecturer()
        elif 'student' in view.__class__.__name__.lower():
            return hasattr(request.user, 'is_student') and request.user.is_student()
        
        return False

    def has_object_permission(self, request, view, obj):
        # Admin users have full access
        if hasattr(request.user, 'is_admin') and request.user.is_admin():
            return True
        
        # Users can only submit their own preferences
        if hasattr(obj, 'user'):
            return obj.user == request.user
        
        return False


class CanApprovePreferences(permissions.BasePermission):
    """
    Permission that only allows admin users to approve preferences.
    """

    def has_permission(self, request, view):
        return (
            request.user and 
            request.user.is_authenticated and 
            hasattr(request.user, 'is_admin') and 
            request.user.is_admin()
        )

    def has_object_permission(self, request, view, obj):
        # Only admin users can approve preferences
        return (
            hasattr(request.user, 'is_admin') and 
            request.user.is_admin()
        )


class ReadOnlyOrOwnerWrite(permissions.BasePermission):
    """
    Permission that allows read access to everyone but write access only to owners or admins.
    """

    def has_permission(self, request, view):
        # Read permissions for any authenticated user
        if request.method in permissions.SAFE_METHODS:
            return request.user and request.user.is_authenticated
        
        # Write permissions only for authenticated users
        return request.user and request.user.is_authenticated

    def has_object_permission(self, request, view, obj):
        # Read permissions for any authenticated user
        if request.method in permissions.SAFE_METHODS:
            return True
        
        # Write permissions only for the owner or admin
        if hasattr(request.user, 'is_admin') and request.user.is_admin():
            return True
        
        if hasattr(obj, 'user'):
            return obj.user == request.user
        
        return False
