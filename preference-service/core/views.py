"""
Core views for shared functionality.
"""
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from django.http import JsonResponse
from django.conf import settings
from core.models import AuditLog, User, Course, TimeSlot
from django.db.models import Count, Q
from django.utils import timezone
from datetime import timedelta
import logging

logger = logging.getLogger(__name__)


@api_view(['GET'])
@permission_classes([AllowAny])
def health_check(request):
    """
    Health check endpoint for monitoring and load balancers.
    """
    try:
        # Check database connectivity
        user_count = User.objects.count()
        course_count = Course.objects.count()
        
        # Check Redis connectivity (for WebSockets)
        from django.core.cache import cache
        cache.set('health_check', 'ok', 30)
        cache_status = cache.get('health_check') == 'ok'
        
        return Response({
            'status': 'healthy',
            'service': 'preference-collection-service',
            'version': '1.0.0',
            'debug': settings.DEBUG,
            'database': 'connected',
            'cache': 'connected' if cache_status else 'disconnected',
            'user_count': user_count,
            'course_count': course_count,
            'timestamp': timezone.now().isoformat(),
        }, status=status.HTTP_200_OK)
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return Response({
            'status': 'unhealthy',
            'service': 'preference-collection-service',
            'error': str(e),
            'timestamp': timezone.now().isoformat(),
        }, status=status.HTTP_503_SERVICE_UNAVAILABLE)


@api_view(['GET'])
@permission_classes([AllowAny])
def service_info(request):
    """
    Service information endpoint.
    """
    return Response({
        'service': 'Preference Collection Service',
        'description': 'Preference collection and management service for the Timetable Management System',
        'version': '1.0.0',
        'features': [
            'Lecturer preference collection (time slots, courses, constraints)',
            'Student preference collection (course selections, time constraints)',
            'Real-time preference updates via WebSockets',
            'Preference validation and conflict detection',
            'Preference templates and bulk operations',
            'Integration with timetable generation service',
            'Comprehensive audit logging',
        ],
        'endpoints': {
            'lecturer_preferences': '/api/preferences/lecturer-preferences/',
            'student_preferences': '/api/preferences/student-preferences/',
            'preference_templates': '/api/templates/',
            'validation': '/api/validation/',
            'websocket': '/ws/preferences/',
            'docs': '/api/docs/',
            'health': '/api/health/',
        },
        'authentication': 'JWT tokens from auth service',
        'real_time': 'WebSocket support for live updates',
    }, status=status.HTTP_200_OK)


@api_view(['GET'])
def statistics_view(request):
    """
    Get preference collection statistics.
    """
    try:
        # Basic statistics
        total_users = User.objects.filter(is_active=True).count()
        lecturers = User.objects.filter(role_name='lecturer', is_active=True).count()
        students = User.objects.filter(role_name='student', is_active=True).count()
        
        # Preference statistics
        from preferences.models import LecturerPreference, StudentPreference
        
        lecturer_preferences = LecturerPreference.objects.filter(is_active=True).count()
        student_preferences = StudentPreference.objects.filter(is_active=True).count()
        
        # Completion rates
        lecturers_with_preferences = LecturerPreference.objects.filter(
            is_active=True
        ).values('user').distinct().count()
        
        students_with_preferences = StudentPreference.objects.filter(
            is_active=True
        ).values('user').distinct().count()
        
        lecturer_completion_rate = (lecturers_with_preferences / lecturers * 100) if lecturers > 0 else 0
        student_completion_rate = (students_with_preferences / students * 100) if students > 0 else 0
        
        # Recent activity (last 7 days)
        seven_days_ago = timezone.now() - timedelta(days=7)
        recent_updates = AuditLog.objects.filter(
            created_at__gte=seven_days_ago,
            resource__in=['lecturer_preference', 'student_preference']
        ).count()
        
        # Validation statistics
        from validation.models import ValidationResult
        
        validation_results = ValidationResult.objects.filter(
            created_at__gte=seven_days_ago
        ).values('status').annotate(count=Count('id'))
        
        return Response({
            'users': {
                'total': total_users,
                'lecturers': lecturers,
                'students': students,
            },
            'preferences': {
                'lecturer_preferences': lecturer_preferences,
                'student_preferences': student_preferences,
                'total': lecturer_preferences + student_preferences,
            },
            'completion_rates': {
                'lecturers': round(lecturer_completion_rate, 2),
                'students': round(student_completion_rate, 2),
                'overall': round((lecturer_completion_rate + student_completion_rate) / 2, 2),
            },
            'recent_activity': {
                'updates_last_7_days': recent_updates,
                'validation_results': list(validation_results),
            },
            'generated_at': timezone.now().isoformat(),
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Error generating statistics: {str(e)}")
        return Response({
            'error': 'Failed to generate statistics',
            'message': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def audit_logs_view(request):
    """
    Get audit logs (admin only).
    """
    # Check permissions
    if not request.user.is_authenticated or not request.user.is_admin():
        return Response({
            'error': 'Permission denied'
        }, status=status.HTTP_403_FORBIDDEN)
    
    try:
        # Get query parameters
        user_id = request.GET.get('user_id')
        action = request.GET.get('action')
        resource = request.GET.get('resource')
        days = int(request.GET.get('days', 30))
        
        # Build query
        queryset = AuditLog.objects.all()
        
        if user_id:
            queryset = queryset.filter(user_id=user_id)
        
        if action:
            queryset = queryset.filter(action=action)
            
        if resource:
            queryset = queryset.filter(resource=resource)
        
        # Filter by date range
        start_date = timezone.now() - timedelta(days=days)
        queryset = queryset.filter(created_at__gte=start_date)
        
        # Order by most recent
        queryset = queryset.order_by('-created_at')
        
        # Paginate
        page_size = int(request.GET.get('page_size', 50))
        page = int(request.GET.get('page', 1))
        start = (page - 1) * page_size
        end = start + page_size
        
        logs = queryset[start:end]
        total_count = queryset.count()
        
        # Serialize logs
        logs_data = []
        for log in logs:
            logs_data.append({
                'id': str(log.id),
                'user_id': str(log.user_id),
                'target_user_id': str(log.target_user_id) if log.target_user_id else None,
                'action': log.action,
                'resource': log.resource,
                'resource_id': str(log.resource_id) if log.resource_id else None,
                'details': log.details,
                'ip_address': log.ip_address,
                'created_at': log.created_at.isoformat(),
            })
        
        return Response({
            'logs': logs_data,
            'total_count': total_count,
            'page': page,
            'page_size': page_size,
            'has_next': end < total_count,
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Error fetching audit logs: {str(e)}")
        return Response({
            'error': 'Failed to fetch audit logs',
            'message': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def time_slots_view(request):
    """
    Get available time slots.
    """
    try:
        time_slots = TimeSlot.objects.filter(is_active=True).order_by('day_of_week', 'start_time')
        
        # Group by day of week
        slots_by_day = {}
        for slot in time_slots:
            day = slot.day_of_week
            if day not in slots_by_day:
                slots_by_day[day] = []
            
            slots_by_day[day].append({
                'id': str(slot.id),
                'start_time': slot.start_time.strftime('%H:%M'),
                'end_time': slot.end_time.strftime('%H:%M'),
                'duration_minutes': slot.duration_minutes,
            })
        
        return Response({
            'time_slots': slots_by_day,
            'working_hours': {
                'start': settings.WORKING_HOURS_START,
                'end': settings.WORKING_HOURS_END,
            },
            'working_days': settings.WORKING_DAYS,
            'default_duration': settings.DEFAULT_TIME_SLOT_DURATION,
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Error fetching time slots: {str(e)}")
        return Response({
            'error': 'Failed to fetch time slots',
            'message': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
