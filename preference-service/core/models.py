"""
Core models for shared functionality across the preference collection service.
"""
from django.contrib.auth.models import AbstractBaseUser, PermissionsMixin
from django.db import models
from django.utils import timezone
from django.core.validators import EmailValidator
import uuid


class BaseModel(models.Model):
    """
    Abstract base model with common fields for all models.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        abstract = True
        ordering = ['-created_at']

    def soft_delete(self):
        """Soft delete by setting is_active to False"""
        self.is_active = False
        self.save()

    def restore(self):
        """Restore soft deleted object"""
        self.is_active = True
        self.save()


class User(AbstractBaseUser, PermissionsMixin, BaseModel):
    """
    User model proxy for preference service (synced from user management service).
    """
    email = models.EmailField(
        unique=True,
        validators=[EmailValidator()],
        help_text="User's email address"
    )
    username = models.CharField(
        max_length=150,
        unique=True,
        help_text="Unique username for the user"
    )
    first_name = models.CharField(max_length=150, blank=True)
    last_name = models.CharField(max_length=150, blank=True)
    
    # Role information (synced from user service)
    role_name = models.CharField(max_length=50, blank=True)
    role_display_name = models.CharField(max_length=100, blank=True)
    
    # Status fields
    is_staff = models.BooleanField(default=False)
    is_superuser = models.BooleanField(default=False)
    is_verified = models.BooleanField(default=False)
    is_blocked = models.BooleanField(default=False)
    
    # Timestamps
    date_joined = models.DateTimeField(default=timezone.now)
    last_login = models.DateTimeField(null=True, blank=True)
    
    # Additional fields
    phone_number = models.CharField(max_length=20, blank=True)
    
    # Profile information (synced from user service)
    department = models.CharField(max_length=100, blank=True)
    faculty = models.CharField(max_length=100, blank=True)
    employee_id = models.CharField(max_length=50, blank=True)
    student_id = models.CharField(max_length=50, blank=True)
    year_of_study = models.IntegerField(null=True, blank=True)
    
    # Sync tracking
    last_synced_at = models.DateTimeField(auto_now=True)

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username', 'first_name', 'last_name']

    class Meta:
        db_table = 'users'
        verbose_name = 'User'
        verbose_name_plural = 'Users'
        indexes = [
            models.Index(fields=['email']),
            models.Index(fields=['username']),
            models.Index(fields=['role_name']),
            models.Index(fields=['department']),
            models.Index(fields=['is_active']),
        ]

    def __str__(self):
        return f"{self.get_full_name()} ({self.email})"

    def get_full_name(self):
        """Return the user's full name."""
        return f"{self.first_name} {self.last_name}".strip()

    def get_short_name(self):
        """Return the user's short name."""
        return self.first_name

    def is_lecturer(self):
        """Check if user is a lecturer."""
        return self.role_name == 'lecturer'

    def is_student(self):
        """Check if user is a student."""
        return self.role_name == 'student'

    def is_admin(self):
        """Check if user is an admin."""
        return self.role_name in ['admin', 'superadmin']


class Course(BaseModel):
    """
    Course model (synced from user management service).
    """
    code = models.CharField(max_length=20, unique=True)
    name = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    department = models.CharField(max_length=100)
    faculty = models.CharField(max_length=100, blank=True)
    
    # Course details
    credits = models.IntegerField(default=3)
    semester = models.CharField(max_length=20)
    year_level = models.IntegerField(default=1)
    
    # Sync tracking
    last_synced_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'courses'
        verbose_name = 'Course'
        verbose_name_plural = 'Courses'
        indexes = [
            models.Index(fields=['code']),
            models.Index(fields=['department']),
            models.Index(fields=['semester']),
            models.Index(fields=['year_level']),
        ]

    def __str__(self):
        return f"{self.code} - {self.name}"


class TimeSlot(BaseModel):
    """
    Time slot model for scheduling.
    """
    DAY_CHOICES = [
        ('monday', 'Monday'),
        ('tuesday', 'Tuesday'),
        ('wednesday', 'Wednesday'),
        ('thursday', 'Thursday'),
        ('friday', 'Friday'),
        ('saturday', 'Saturday'),
        ('sunday', 'Sunday'),
    ]

    day_of_week = models.CharField(max_length=10, choices=DAY_CHOICES)
    start_time = models.TimeField()
    end_time = models.TimeField()
    duration_minutes = models.IntegerField(help_text="Duration in minutes")

    class Meta:
        db_table = 'time_slots'
        verbose_name = 'Time Slot'
        verbose_name_plural = 'Time Slots'
        unique_together = ['day_of_week', 'start_time', 'end_time']
        indexes = [
            models.Index(fields=['day_of_week', 'start_time']),
            models.Index(fields=['duration_minutes']),
        ]

    def __str__(self):
        return f"{self.get_day_of_week_display()} {self.start_time}-{self.end_time}"

    def overlaps_with(self, other_slot):
        """Check if this time slot overlaps with another."""
        if self.day_of_week != other_slot.day_of_week:
            return False
        
        return (self.start_time < other_slot.end_time and 
                self.end_time > other_slot.start_time)

    def is_within_working_hours(self, start_hour=8, end_hour=18):
        """Check if time slot is within working hours."""
        return (self.start_time.hour >= start_hour and 
                self.end_time.hour <= end_hour)


class AuditLog(BaseModel):
    """
    Audit log for tracking preference-related operations.
    """
    ACTION_CHOICES = [
        ('create', 'Create'),
        ('update', 'Update'),
        ('delete', 'Delete'),
        ('validate', 'Validate'),
        ('submit', 'Submit'),
        ('approve', 'Approve'),
        ('reject', 'Reject'),
    ]

    user_id = models.UUIDField(help_text="ID of the user performing the action")
    target_user_id = models.UUIDField(help_text="ID of the user being acted upon", null=True, blank=True)
    action = models.CharField(max_length=20, choices=ACTION_CHOICES)
    resource = models.CharField(max_length=50, help_text="Resource being acted upon")
    resource_id = models.UUIDField(help_text="ID of the resource", null=True, blank=True)
    details = models.JSONField(default=dict, help_text="Additional details about the action")
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)

    class Meta:
        db_table = 'audit_logs'
        verbose_name = 'Audit Log'
        verbose_name_plural = 'Audit Logs'
        indexes = [
            models.Index(fields=['user_id']),
            models.Index(fields=['target_user_id']),
            models.Index(fields=['action']),
            models.Index(fields=['resource']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"{self.action} on {self.resource} by {self.user_id}"
