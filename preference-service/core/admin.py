"""
Django admin configuration for core models.
"""
from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from core.models import User, Course, TimeSlot, AuditLog


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    """
    Admin interface for User model.
    """
    list_display = [
        'email', 'username', 'first_name', 'last_name', 'role_name',
        'department', 'is_verified', 'is_blocked', 'is_active'
    ]
    list_filter = [
        'role_name', 'department', 'faculty', 'is_verified', 
        'is_blocked', 'is_active', 'date_joined'
    ]
    search_fields = [
        'email', 'username', 'first_name', 'last_name',
        'employee_id', 'student_id', 'department'
    ]
    readonly_fields = ['id', 'date_joined', 'last_login', 'last_synced_at']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('id', 'email', 'username', 'first_name', 'last_name', 'phone_number')
        }),
        ('Role and Permissions', {
            'fields': ('role_name', 'role_display_name', 'is_staff', 'is_superuser')
        }),
        ('Academic Information', {
            'fields': (
                'department', 'faculty', 'employee_id', 'student_id', 'year_of_study'
            )
        }),
        ('Status', {
            'fields': ('is_verified', 'is_blocked', 'is_active')
        }),
        ('Timestamps', {
            'fields': ('date_joined', 'last_login', 'last_synced_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).filter(is_active=True)


@admin.register(Course)
class CourseAdmin(admin.ModelAdmin):
    """
    Admin interface for Course model.
    """
    list_display = [
        'code', 'name', 'department', 'faculty', 'credits',
        'semester', 'year_level', 'is_active'
    ]
    list_filter = [
        'department', 'faculty', 'semester', 'year_level', 
        'credits', 'is_active'
    ]
    search_fields = ['code', 'name', 'description', 'department']
    readonly_fields = ['id', 'created_at', 'updated_at', 'last_synced_at']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('id', 'code', 'name', 'description')
        }),
        ('Academic Details', {
            'fields': ('department', 'faculty', 'credits', 'semester', 'year_level')
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at', 'last_synced_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(TimeSlot)
class TimeSlotAdmin(admin.ModelAdmin):
    """
    Admin interface for TimeSlot model.
    """
    list_display = [
        'day_of_week', 'start_time', 'end_time', 'duration_minutes', 'is_active'
    ]
    list_filter = ['day_of_week', 'duration_minutes', 'is_active']
    search_fields = ['day_of_week']
    readonly_fields = ['id', 'created_at', 'updated_at']
    
    fieldsets = (
        ('Time Information', {
            'fields': ('day_of_week', 'start_time', 'end_time', 'duration_minutes')
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
        ('Timestamps', {
            'fields': ('id', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).order_by('day_of_week', 'start_time')


@admin.register(AuditLog)
class AuditLogAdmin(admin.ModelAdmin):
    """
    Admin interface for AuditLog model.
    """
    list_display = [
        'user_id', 'action', 'resource', 'target_user_id', 
        'ip_address', 'created_at'
    ]
    list_filter = ['action', 'resource', 'created_at']
    search_fields = ['user_id', 'target_user_id', 'resource', 'ip_address']
    readonly_fields = ['id', 'created_at']
    
    fieldsets = (
        ('Action Information', {
            'fields': ('user_id', 'target_user_id', 'action', 'resource', 'resource_id')
        }),
        ('Details', {
            'fields': ('details', 'ip_address', 'user_agent')
        }),
        ('Timestamp', {
            'fields': ('id', 'created_at'),
            'classes': ('collapse',)
        }),
    )

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        return False
