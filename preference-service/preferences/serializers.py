"""
Serializers for preference management endpoints.
"""
from rest_framework import serializers
from django.utils import timezone
from preferences.models import (
    LecturerPreference, StudentPreference, StudentCoursePreference,
    PreferenceHistory, PreferenceSubmission
)
from core.models import User, Course, TimeSlot


class TimeSlotSerializer(serializers.ModelSerializer):
    """
    Serializer for time slot information.
    """
    day_display = serializers.CharField(source='get_day_of_week_display', read_only=True)
    
    class Meta:
        model = TimeSlot
        fields = [
            'id', 'day_of_week', 'day_display', 'start_time', 'end_time', 
            'duration_minutes'
        ]
        read_only_fields = ['id']


class CourseSerializer(serializers.ModelSerializer):
    """
    Serializer for course information.
    """
    class Meta:
        model = Course
        fields = [
            'id', 'code', 'name', 'description', 'department', 'faculty',
            'credits', 'semester', 'year_level'
        ]
        read_only_fields = ['id']


class LecturerPreferenceSerializer(serializers.ModelSerializer):
    """
    Serializer for lecturer preferences.
    """
    user_name = serializers.CharField(source='user.get_full_name', read_only=True)
    user_email = serializers.CharField(source='user.email', read_only=True)
    user_department = serializers.CharField(source='user.department', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    workload_display = serializers.CharField(source='get_preferred_workload_display', read_only=True)
    approved_by_name = serializers.CharField(source='approved_by.get_full_name', read_only=True)
    
    # Nested serializers for related objects
    preferred_courses = CourseSerializer(many=True, read_only=True)
    available_time_slots = TimeSlotSerializer(many=True, read_only=True)
    
    # Write-only fields for updates
    preferred_course_ids = serializers.ListField(
        child=serializers.UUIDField(),
        write_only=True,
        required=False
    )
    available_time_slot_ids = serializers.ListField(
        child=serializers.UUIDField(),
        write_only=True,
        required=False
    )

    class Meta:
        model = LecturerPreference
        fields = [
            'id', 'user', 'user_name', 'user_email', 'user_department',
            'academic_year', 'semester', 'status', 'status_display',
            'submitted_at', 'approved_at', 'approved_by', 'approved_by_name',
            'preferred_workload', 'workload_display', 'max_courses_per_semester',
            'max_hours_per_week', 'preferred_courses', 'preferred_course_ids',
            'available_time_slots', 'available_time_slot_ids',
            'constraints', 'comments', 'admin_notes',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'submitted_at', 'approved_at', 'approved_by',
            'created_at', 'updated_at'
        ]

    def validate(self, attrs):
        """Validate lecturer preference data."""
        # Validate workload constraints
        max_courses = attrs.get('max_courses_per_semester', 0)
        max_hours = attrs.get('max_hours_per_week', 0)
        
        if max_courses > 10:
            raise serializers.ValidationError("Maximum courses per semester cannot exceed 10")
        
        if max_hours > 40:
            raise serializers.ValidationError("Maximum hours per week cannot exceed 40")
        
        # Validate time slot availability
        time_slot_ids = attrs.get('available_time_slot_ids', [])
        if time_slot_ids:
            # Check if all time slots exist
            existing_slots = TimeSlot.objects.filter(id__in=time_slot_ids, is_active=True)
            if len(existing_slots) != len(time_slot_ids):
                raise serializers.ValidationError("Some time slots are invalid or inactive")
        
        return attrs

    def create(self, validated_data):
        """Create lecturer preference with related objects."""
        preferred_course_ids = validated_data.pop('preferred_course_ids', [])
        available_time_slot_ids = validated_data.pop('available_time_slot_ids', [])
        
        preference = LecturerPreference.objects.create(**validated_data)
        
        # Set related objects
        if preferred_course_ids:
            courses = Course.objects.filter(id__in=preferred_course_ids, is_active=True)
            preference.preferred_courses.set(courses)
        
        if available_time_slot_ids:
            time_slots = TimeSlot.objects.filter(id__in=available_time_slot_ids, is_active=True)
            preference.available_time_slots.set(time_slots)
        
        return preference

    def update(self, instance, validated_data):
        """Update lecturer preference with related objects."""
        preferred_course_ids = validated_data.pop('preferred_course_ids', None)
        available_time_slot_ids = validated_data.pop('available_time_slot_ids', None)
        
        # Update basic fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        
        # Update related objects
        if preferred_course_ids is not None:
            courses = Course.objects.filter(id__in=preferred_course_ids, is_active=True)
            instance.preferred_courses.set(courses)
        
        if available_time_slot_ids is not None:
            time_slots = TimeSlot.objects.filter(id__in=available_time_slot_ids, is_active=True)
            instance.available_time_slots.set(time_slots)
        
        return instance


class StudentCoursePreferenceSerializer(serializers.ModelSerializer):
    """
    Serializer for student course preferences.
    """
    course = CourseSerializer(read_only=True)
    course_id = serializers.UUIDField(write_only=True)
    priority_display = serializers.CharField(source='get_priority_display', read_only=True)

    class Meta:
        model = StudentCoursePreference
        fields = [
            'id', 'course', 'course_id', 'priority', 'priority_display',
            'is_required', 'notes'
        ]
        read_only_fields = ['id']


class StudentPreferenceSerializer(serializers.ModelSerializer):
    """
    Serializer for student preferences.
    """
    user_name = serializers.CharField(source='user.get_full_name', read_only=True)
    user_email = serializers.CharField(source='user.email', read_only=True)
    user_department = serializers.CharField(source='user.department', read_only=True)
    user_year_of_study = serializers.IntegerField(source='user.year_of_study', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    approved_by_name = serializers.CharField(source='approved_by.get_full_name', read_only=True)
    
    # Nested serializers for related objects
    course_preferences = StudentCoursePreferenceSerializer(
        source='studentcoursepreference_set',
        many=True,
        read_only=True
    )
    preferred_time_slots = TimeSlotSerializer(many=True, read_only=True)
    unavailable_time_slots = TimeSlotSerializer(many=True, read_only=True)
    
    # Write-only fields for updates
    course_preferences_data = serializers.ListField(
        child=serializers.DictField(),
        write_only=True,
        required=False
    )
    preferred_time_slot_ids = serializers.ListField(
        child=serializers.UUIDField(),
        write_only=True,
        required=False
    )
    unavailable_time_slot_ids = serializers.ListField(
        child=serializers.UUIDField(),
        write_only=True,
        required=False
    )

    class Meta:
        model = StudentPreference
        fields = [
            'id', 'user', 'user_name', 'user_email', 'user_department', 'user_year_of_study',
            'academic_year', 'semester', 'status', 'status_display',
            'submitted_at', 'approved_at', 'approved_by', 'approved_by_name',
            'course_preferences', 'course_preferences_data',
            'preferred_time_slots', 'preferred_time_slot_ids',
            'unavailable_time_slots', 'unavailable_time_slot_ids',
            'max_courses_per_semester', 'max_credits_per_semester',
            'prefer_morning_classes', 'prefer_afternoon_classes',
            'prefer_consecutive_classes', 'avoid_friday_classes',
            'constraints', 'comments', 'admin_notes',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'submitted_at', 'approved_at', 'approved_by',
            'created_at', 'updated_at'
        ]

    def validate(self, attrs):
        """Validate student preference data."""
        # Validate course and credit limits
        max_courses = attrs.get('max_courses_per_semester', 0)
        max_credits = attrs.get('max_credits_per_semester', 0)
        
        if max_courses > 10:
            raise serializers.ValidationError("Maximum courses per semester cannot exceed 10")
        
        if max_credits > 30:
            raise serializers.ValidationError("Maximum credits per semester cannot exceed 30")
        
        # Validate course preferences data
        course_prefs_data = attrs.get('course_preferences_data', [])
        if course_prefs_data:
            total_credits = 0
            course_ids = []
            
            for pref_data in course_prefs_data:
                course_id = pref_data.get('course_id')
                if not course_id:
                    raise serializers.ValidationError("Course ID is required for each course preference")
                
                if course_id in course_ids:
                    raise serializers.ValidationError("Duplicate course preferences are not allowed")
                
                course_ids.append(course_id)
                
                # Validate course exists
                try:
                    course = Course.objects.get(id=course_id, is_active=True)
                    total_credits += course.credits
                except Course.DoesNotExist:
                    raise serializers.ValidationError(f"Course with ID {course_id} does not exist")
            
            # Check if total credits exceed limit
            if total_credits > max_credits:
                raise serializers.ValidationError(
                    f"Total credits ({total_credits}) exceed maximum allowed ({max_credits})"
                )
        
        return attrs

    def create(self, validated_data):
        """Create student preference with related objects."""
        course_prefs_data = validated_data.pop('course_preferences_data', [])
        preferred_time_slot_ids = validated_data.pop('preferred_time_slot_ids', [])
        unavailable_time_slot_ids = validated_data.pop('unavailable_time_slot_ids', [])
        
        preference = StudentPreference.objects.create(**validated_data)
        
        # Create course preferences
        for pref_data in course_prefs_data:
            course = Course.objects.get(id=pref_data['course_id'])
            StudentCoursePreference.objects.create(
                student_preference=preference,
                course=course,
                priority=pref_data.get('priority', 2),
                is_required=pref_data.get('is_required', False),
                notes=pref_data.get('notes', '')
            )
        
        # Set time slot preferences
        if preferred_time_slot_ids:
            time_slots = TimeSlot.objects.filter(id__in=preferred_time_slot_ids, is_active=True)
            preference.preferred_time_slots.set(time_slots)
        
        if unavailable_time_slot_ids:
            time_slots = TimeSlot.objects.filter(id__in=unavailable_time_slot_ids, is_active=True)
            preference.unavailable_time_slots.set(time_slots)
        
        return preference

    def update(self, instance, validated_data):
        """Update student preference with related objects."""
        course_prefs_data = validated_data.pop('course_preferences_data', None)
        preferred_time_slot_ids = validated_data.pop('preferred_time_slot_ids', None)
        unavailable_time_slot_ids = validated_data.pop('unavailable_time_slot_ids', None)
        
        # Update basic fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        
        # Update course preferences
        if course_prefs_data is not None:
            # Clear existing preferences
            instance.studentcoursepreference_set.all().delete()
            
            # Create new preferences
            for pref_data in course_prefs_data:
                course = Course.objects.get(id=pref_data['course_id'])
                StudentCoursePreference.objects.create(
                    student_preference=instance,
                    course=course,
                    priority=pref_data.get('priority', 2),
                    is_required=pref_data.get('is_required', False),
                    notes=pref_data.get('notes', '')
                )
        
        # Update time slot preferences
        if preferred_time_slot_ids is not None:
            time_slots = TimeSlot.objects.filter(id__in=preferred_time_slot_ids, is_active=True)
            instance.preferred_time_slots.set(time_slots)
        
        if unavailable_time_slot_ids is not None:
            time_slots = TimeSlot.objects.filter(id__in=unavailable_time_slot_ids, is_active=True)
            instance.unavailable_time_slots.set(time_slots)
        
        return instance


class PreferenceHistorySerializer(serializers.ModelSerializer):
    """
    Serializer for preference history tracking.
    """
    user_name = serializers.CharField(source='user.get_full_name', read_only=True)
    changed_by_name = serializers.CharField(source='changed_by.get_full_name', read_only=True)
    preference_type_display = serializers.CharField(source='get_preference_type_display', read_only=True)

    class Meta:
        model = PreferenceHistory
        fields = [
            'id', 'preference_type', 'preference_type_display', 'preference_id',
            'user', 'user_name', 'changed_by', 'changed_by_name',
            'field_name', 'old_value', 'new_value', 'change_reason',
            'created_at'
        ]
        read_only_fields = ['id', 'created_at']


class PreferenceSubmissionSerializer(serializers.ModelSerializer):
    """
    Serializer for preference submissions to timetable generation.
    """
    submitted_by_name = serializers.CharField(source='submitted_by.get_full_name', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    duration = serializers.SerializerMethodField()

    class Meta:
        model = PreferenceSubmission
        fields = [
            'id', 'academic_year', 'semester', 'status', 'status_display',
            'submitted_by', 'submitted_by_name', 'submitted_at', 'processed_at',
            'processing_time_seconds', 'duration', 'total_lecturer_preferences',
            'total_student_preferences', 'validation_errors',
            'submission_data', 'response_data', 'error_details'
        ]
        read_only_fields = [
            'id', 'submitted_at', 'processed_at', 'processing_time_seconds',
            'total_lecturer_preferences', 'total_student_preferences',
            'validation_errors', 'response_data', 'error_details'
        ]

    def get_duration(self, obj):
        """Calculate submission processing duration."""
        if obj.submitted_at and obj.processed_at:
            duration = obj.processed_at - obj.submitted_at
            return duration.total_seconds()
        return None


class PreferenceSubmissionCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating preference submissions.
    """
    class Meta:
        model = PreferenceSubmission
        fields = ['academic_year', 'semester', 'submitted_by']

    def validate(self, attrs):
        """Validate submission data."""
        academic_year = attrs['academic_year']
        semester = attrs['semester']

        # Check if submission already exists
        if PreferenceSubmission.objects.filter(
            academic_year=academic_year,
            semester=semester
        ).exists():
            raise serializers.ValidationError(
                f"Submission for {academic_year} {semester} already exists"
            )

        return attrs
