"""
Views for preference collection and management.
"""
from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import <PERSON>Filter, OrderingFilter
from django.db.models import Q, Count, Prefetch
from django.utils import timezone
from preferences.models import (
    LecturerPreference, StudentPreference, StudentCoursePreference,
    PreferenceHistory, PreferenceSubmission
)
from preferences.serializers import (
    LecturerPreferenceSerializer, StudentPreferenceSerializer,
    PreferenceHistorySerializer, PreferenceSubmissionSerializer,
    PreferenceSubmissionCreateSerializer
)
from core.models import User, Course, TimeSlot
from core.permissions import IsOwnerOrAdmin, IsAdminUser
import logging

logger = logging.getLogger(__name__)


class LecturerPreferenceViewSet(viewsets.ModelViewSet):
    """
    ViewSet for lecturer preference management.
    """
    serializer_class = LecturerPreferenceSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['status', 'academic_year', 'semester', 'preferred_workload']
    search_fields = ['user__first_name', 'user__last_name', 'user__email', 'comments']
    ordering_fields = ['created_at', 'updated_at', 'submitted_at', 'approved_at']
    ordering = ['-created_at']

    def get_queryset(self):
        """
        Get queryset based on user permissions.
        """
        user = self.request.user
        queryset = LecturerPreference.objects.select_related(
            'user', 'approved_by'
        ).prefetch_related(
            'preferred_courses', 'available_time_slots'
        ).filter(is_active=True)

        if user.is_admin():
            # Admins can see all preferences
            return queryset
        elif user.is_lecturer():
            # Lecturers can only see their own preferences
            return queryset.filter(user=user)
        else:
            # Students and others cannot access lecturer preferences
            return queryset.none()

    def perform_create(self, serializer):
        """
        Create lecturer preference with current user.
        """
        # Only lecturers can create their own preferences
        if not self.request.user.is_lecturer():
            raise permissions.PermissionDenied("Only lecturers can create preferences")
        
        serializer.save(user=self.request.user)

    def perform_update(self, serializer):
        """
        Update lecturer preference with validation.
        """
        instance = self.get_object()
        user = self.request.user
        
        # Check permissions
        if not (user.is_admin() or instance.user == user):
            raise permissions.PermissionDenied("You can only update your own preferences")
        
        # Prevent updates to submitted/approved preferences unless admin
        if instance.status in ['submitted', 'approved'] and not user.is_admin():
            raise permissions.PermissionDenied("Cannot update submitted or approved preferences")
        
        serializer.save()

    @action(detail=True, methods=['post'])
    def submit(self, request, pk=None):
        """
        Submit preference for approval.
        """
        preference = self.get_object()
        user = request.user
        
        # Check permissions
        if not (user.is_admin() or preference.user == user):
            return Response({
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)
        
        # Check if already submitted
        if preference.status != 'draft':
            return Response({
                'error': 'Preference is not in draft status'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Validate preference completeness
        validation_errors = self.validate_preference_completeness(preference)
        if validation_errors:
            return Response({
                'error': 'Preference is incomplete',
                'validation_errors': validation_errors
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Submit preference
        preference.submit()
        
        # Log the action
        self.log_preference_action(preference, 'submit', user)
        
        return Response({
            'message': 'Preference submitted successfully',
            'status': preference.status
        }, status=status.HTTP_200_OK)

    @action(detail=True, methods=['post'], permission_classes=[IsAdminUser])
    def approve(self, request, pk=None):
        """
        Approve lecturer preference (admin only).
        """
        preference = self.get_object()
        
        if preference.status != 'submitted':
            return Response({
                'error': 'Preference is not submitted for approval'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        preference.approve(request.user)
        self.log_preference_action(preference, 'approve', request.user)
        
        return Response({
            'message': 'Preference approved successfully',
            'status': preference.status
        }, status=status.HTTP_200_OK)

    @action(detail=True, methods=['post'], permission_classes=[IsAdminUser])
    def reject(self, request, pk=None):
        """
        Reject lecturer preference (admin only).
        """
        preference = self.get_object()
        
        if preference.status != 'submitted':
            return Response({
                'error': 'Preference is not submitted for approval'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Add rejection reason to admin notes
        rejection_reason = request.data.get('reason', '')
        if rejection_reason:
            preference.admin_notes = f"Rejected: {rejection_reason}\n{preference.admin_notes}"
        
        preference.reject()
        self.log_preference_action(preference, 'reject', request.user)
        
        return Response({
            'message': 'Preference rejected successfully',
            'status': preference.status
        }, status=status.HTTP_200_OK)

    @action(detail=True, methods=['get'])
    def history(self, request, pk=None):
        """
        Get preference change history.
        """
        preference = self.get_object()
        
        # Check permissions
        user = request.user
        if not (user.is_admin() or preference.user == user):
            return Response({
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)
        
        history = PreferenceHistory.objects.filter(
            preference_type='lecturer',
            preference_id=preference.id
        ).select_related('user', 'changed_by').order_by('-created_at')
        
        serializer = PreferenceHistorySerializer(history, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    @action(detail=False, methods=['get'])
    def my_preferences(self, request):
        """
        Get current user's preferences.
        """
        if not request.user.is_lecturer():
            return Response({
                'error': 'Only lecturers can access this endpoint'
            }, status=status.HTTP_403_FORBIDDEN)
        
        preferences = self.get_queryset().filter(user=request.user)
        serializer = self.get_serializer(preferences, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    @action(detail=False, methods=['get'], permission_classes=[IsAdminUser])
    def statistics(self, request):
        """
        Get lecturer preference statistics (admin only).
        """
        queryset = self.get_queryset()
        
        # Basic statistics
        total_preferences = queryset.count()
        status_counts = queryset.values('status').annotate(count=Count('id'))
        
        # Workload distribution
        workload_counts = queryset.values('preferred_workload').annotate(count=Count('id'))
        
        # Recent activity
        recent_submissions = queryset.filter(
            submitted_at__gte=timezone.now() - timezone.timedelta(days=7)
        ).count()
        
        return Response({
            'total_preferences': total_preferences,
            'status_distribution': list(status_counts),
            'workload_distribution': list(workload_counts),
            'recent_submissions': recent_submissions,
        }, status=status.HTTP_200_OK)

    def validate_preference_completeness(self, preference):
        """
        Validate if preference is complete enough for submission.
        """
        errors = []
        
        if not preference.preferred_courses.exists():
            errors.append("At least one preferred course must be selected")
        
        if not preference.available_time_slots.exists():
            errors.append("At least one available time slot must be selected")
        
        if not preference.max_courses_per_semester:
            errors.append("Maximum courses per semester must be specified")
        
        if not preference.max_hours_per_week:
            errors.append("Maximum hours per week must be specified")
        
        return errors

    def log_preference_action(self, preference, action, user):
        """
        Log preference action for audit purposes.
        """
        try:
            from core.models import AuditLog
            AuditLog.objects.create(
                user_id=user.id,
                target_user_id=preference.user.id,
                action=action,
                resource='lecturer_preference',
                resource_id=preference.id,
                details={
                    'academic_year': preference.academic_year,
                    'semester': preference.semester,
                    'status': preference.status,
                }
            )
        except Exception as e:
            logger.error(f"Failed to log preference action: {e}")


class StudentPreferenceViewSet(viewsets.ModelViewSet):
    """
    ViewSet for student preference management.
    """
    serializer_class = StudentPreferenceSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['status', 'academic_year', 'semester']
    search_fields = ['user__first_name', 'user__last_name', 'user__email', 'comments']
    ordering_fields = ['created_at', 'updated_at', 'submitted_at', 'approved_at']
    ordering = ['-created_at']

    def get_queryset(self):
        """
        Get queryset based on user permissions.
        """
        user = self.request.user
        queryset = StudentPreference.objects.select_related(
            'user', 'approved_by'
        ).prefetch_related(
            Prefetch(
                'studentcoursepreference_set',
                queryset=StudentCoursePreference.objects.select_related('course')
            ),
            'preferred_time_slots',
            'unavailable_time_slots'
        ).filter(is_active=True)

        if user.is_admin():
            # Admins can see all preferences
            return queryset
        elif user.is_student():
            # Students can only see their own preferences
            return queryset.filter(user=user)
        else:
            # Lecturers and others cannot access student preferences
            return queryset.none()

    def perform_create(self, serializer):
        """
        Create student preference with current user.
        """
        # Only students can create their own preferences
        if not self.request.user.is_student():
            raise permissions.PermissionDenied("Only students can create preferences")
        
        serializer.save(user=self.request.user)

    def perform_update(self, serializer):
        """
        Update student preference with validation.
        """
        instance = self.get_object()
        user = self.request.user
        
        # Check permissions
        if not (user.is_admin() or instance.user == user):
            raise permissions.PermissionDenied("You can only update your own preferences")
        
        # Prevent updates to submitted/approved preferences unless admin
        if instance.status in ['submitted', 'approved'] and not user.is_admin():
            raise permissions.PermissionDenied("Cannot update submitted or approved preferences")
        
        serializer.save()

    @action(detail=True, methods=['post'])
    def submit(self, request, pk=None):
        """
        Submit preference for approval.
        """
        preference = self.get_object()
        user = request.user
        
        # Check permissions
        if not (user.is_admin() or preference.user == user):
            return Response({
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)
        
        # Check if already submitted
        if preference.status != 'draft':
            return Response({
                'error': 'Preference is not in draft status'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Validate preference completeness
        validation_errors = self.validate_preference_completeness(preference)
        if validation_errors:
            return Response({
                'error': 'Preference is incomplete',
                'validation_errors': validation_errors
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Submit preference
        preference.submit()
        
        # Log the action
        self.log_preference_action(preference, 'submit', user)
        
        return Response({
            'message': 'Preference submitted successfully',
            'status': preference.status
        }, status=status.HTTP_200_OK)

    def validate_preference_completeness(self, preference):
        """
        Validate if preference is complete enough for submission.
        """
        errors = []
        
        if not preference.studentcoursepreference_set.exists():
            errors.append("At least one course preference must be selected")
        
        if not preference.max_courses_per_semester:
            errors.append("Maximum courses per semester must be specified")
        
        if not preference.max_credits_per_semester:
            errors.append("Maximum credits per semester must be specified")
        
        # Check if total preferred credits exceed limit
        total_credits = preference.get_total_preferred_credits()
        if total_credits > preference.max_credits_per_semester:
            errors.append(f"Total preferred credits ({total_credits}) exceed maximum allowed ({preference.max_credits_per_semester})")
        
        return errors

    def log_preference_action(self, preference, action, user):
        """
        Log preference action for audit purposes.
        """
        try:
            from core.models import AuditLog
            AuditLog.objects.create(
                user_id=user.id,
                target_user_id=preference.user.id,
                action=action,
                resource='student_preference',
                resource_id=preference.id,
                details={
                    'academic_year': preference.academic_year,
                    'semester': preference.semester,
                    'status': preference.status,
                }
            )
        except Exception as e:
            logger.error(f"Failed to log preference action: {e}")


class PreferenceSubmissionViewSet(viewsets.ModelViewSet):
    """
    ViewSet for preference submissions to timetable generation service.
    """
    serializer_class = PreferenceSubmissionSerializer
    permission_classes = [IsAdminUser]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['status', 'academic_year', 'semester']
    search_fields = ['academic_year', 'semester']
    ordering_fields = ['submitted_at', 'processed_at']
    ordering = ['-submitted_at']

    def get_queryset(self):
        """
        Get all preference submissions.
        """
        return PreferenceSubmission.objects.select_related('submitted_by').filter(is_active=True)

    def get_serializer_class(self):
        """
        Return appropriate serializer based on action.
        """
        if self.action == 'create':
            return PreferenceSubmissionCreateSerializer
        return PreferenceSubmissionSerializer

    def perform_create(self, serializer):
        """
        Create preference submission and trigger processing.
        """
        submission = serializer.save(submitted_by=self.request.user)

        # Trigger async processing
        from preferences.tasks import process_preference_submission
        process_preference_submission.delay(str(submission.id))

    @action(detail=True, methods=['post'])
    def reprocess(self, request, pk=None):
        """
        Reprocess a failed submission.
        """
        submission = self.get_object()

        if submission.status not in ['failed', 'completed']:
            return Response({
                'error': 'Can only reprocess failed or completed submissions'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Reset submission status
        submission.status = 'pending'
        submission.processed_at = None
        submission.processing_time_seconds = None
        submission.error_details = []
        submission.save()

        # Trigger async processing
        from preferences.tasks import process_preference_submission
        process_preference_submission.delay(str(submission.id))

        return Response({
            'message': 'Submission queued for reprocessing',
            'status': submission.status
        }, status=status.HTTP_200_OK)

    @action(detail=False, methods=['get'])
    def latest(self, request):
        """
        Get the latest submission for each academic year/semester.
        """
        # Get query parameters
        academic_year = request.GET.get('academic_year')
        semester = request.GET.get('semester')

        queryset = self.get_queryset()

        if academic_year:
            queryset = queryset.filter(academic_year=academic_year)

        if semester:
            queryset = queryset.filter(semester=semester)

        # Get latest submission for each year/semester combination
        latest_submissions = queryset.order_by(
            'academic_year', 'semester', '-submitted_at'
        ).distinct('academic_year', 'semester')

        serializer = self.get_serializer(latest_submissions, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)


from rest_framework.decorators import api_view, permission_classes

@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def preference_summary(request):
    """
    Get preference summary for the current user.
    """
    user = request.user

    # Get query parameters
    academic_year = request.GET.get('academic_year')
    semester = request.GET.get('semester')

    summary = {
        'user': {
            'id': str(user.id),
            'name': user.get_full_name(),
            'email': user.email,
            'role': user.role_name,
            'department': user.department,
        },
        'preferences': {}
    }

    if user.is_lecturer():
        # Get lecturer preferences
        lecturer_prefs = LecturerPreference.objects.filter(
            user=user,
            is_active=True
        )

        if academic_year:
            lecturer_prefs = lecturer_prefs.filter(academic_year=academic_year)
        if semester:
            lecturer_prefs = lecturer_prefs.filter(semester=semester)

        lecturer_prefs = lecturer_prefs.order_by('-created_at')

        summary['preferences']['lecturer'] = []
        for pref in lecturer_prefs:
            summary['preferences']['lecturer'].append({
                'id': str(pref.id),
                'academic_year': pref.academic_year,
                'semester': pref.semester,
                'status': pref.status,
                'submitted_at': pref.submitted_at,
                'approved_at': pref.approved_at,
                'preferred_courses_count': pref.preferred_courses.count(),
                'available_time_slots_count': pref.available_time_slots.count(),
            })

    elif user.is_student():
        # Get student preferences
        student_prefs = StudentPreference.objects.filter(
            user=user,
            is_active=True
        )

        if academic_year:
            student_prefs = student_prefs.filter(academic_year=academic_year)
        if semester:
            student_prefs = student_prefs.filter(semester=semester)

        student_prefs = student_prefs.order_by('-created_at')

        summary['preferences']['student'] = []
        for pref in student_prefs:
            summary['preferences']['student'].append({
                'id': str(pref.id),
                'academic_year': pref.academic_year,
                'semester': pref.semester,
                'status': pref.status,
                'submitted_at': pref.submitted_at,
                'approved_at': pref.approved_at,
                'course_preferences_count': pref.studentcoursepreference_set.count(),
                'total_preferred_credits': pref.get_total_preferred_credits(),
            })

    return Response(summary, status=status.HTTP_200_OK)


@api_view(['GET'])
@permission_classes([IsAdminUser])
def preference_analytics(request):
    """
    Get preference analytics (admin only).
    """
    # Get query parameters
    academic_year = request.GET.get('academic_year')
    semester = request.GET.get('semester')

    # Base querysets
    lecturer_prefs = LecturerPreference.objects.filter(is_active=True)
    student_prefs = StudentPreference.objects.filter(is_active=True)

    if academic_year:
        lecturer_prefs = lecturer_prefs.filter(academic_year=academic_year)
        student_prefs = student_prefs.filter(academic_year=academic_year)

    if semester:
        lecturer_prefs = lecturer_prefs.filter(semester=semester)
        student_prefs = student_prefs.filter(semester=semester)

    # Lecturer analytics
    lecturer_analytics = {
        'total': lecturer_prefs.count(),
        'by_status': list(lecturer_prefs.values('status').annotate(count=Count('id'))),
        'by_department': list(lecturer_prefs.values('user__department').annotate(count=Count('id'))),
        'by_workload': list(lecturer_prefs.values('preferred_workload').annotate(count=Count('id'))),
        'completion_rate': 0,
    }

    # Calculate lecturer completion rate
    from core.models import User
    total_lecturers = User.objects.filter(role_name='lecturer', is_active=True).count()
    if total_lecturers > 0:
        lecturers_with_prefs = lecturer_prefs.values('user').distinct().count()
        lecturer_analytics['completion_rate'] = round((lecturers_with_prefs / total_lecturers) * 100, 2)

    # Student analytics
    student_analytics = {
        'total': student_prefs.count(),
        'by_status': list(student_prefs.values('status').annotate(count=Count('id'))),
        'by_department': list(student_prefs.values('user__department').annotate(count=Count('id'))),
        'by_year_of_study': list(student_prefs.values('user__year_of_study').annotate(count=Count('id'))),
        'completion_rate': 0,
    }

    # Calculate student completion rate
    total_students = User.objects.filter(role_name='student', is_active=True).count()
    if total_students > 0:
        students_with_prefs = student_prefs.values('user').distinct().count()
        student_analytics['completion_rate'] = round((students_with_prefs / total_students) * 100, 2)

    # Course popularity (from student preferences)
    course_popularity = list(
        StudentCoursePreference.objects.filter(
            student_preference__in=student_prefs
        ).values(
            'course__code', 'course__name'
        ).annotate(
            preference_count=Count('id')
        ).order_by('-preference_count')[:10]
    )

    # Time slot popularity (from lecturer preferences)
    time_slot_popularity = list(
        lecturer_prefs.values(
            'available_time_slots__day_of_week',
            'available_time_slots__start_time'
        ).annotate(
            lecturer_count=Count('id')
        ).order_by('-lecturer_count')[:10]
    )

    return Response({
        'lecturer_analytics': lecturer_analytics,
        'student_analytics': student_analytics,
        'course_popularity': course_popularity,
        'time_slot_popularity': time_slot_popularity,
        'filters': {
            'academic_year': academic_year,
            'semester': semester,
        },
        'generated_at': timezone.now().isoformat(),
    }, status=status.HTTP_200_OK)
