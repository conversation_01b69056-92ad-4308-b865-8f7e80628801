"""
Django admin configuration for preference models.
"""
from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from preferences.models import (
    LecturerPreference, StudentPreference, StudentCoursePreference,
    PreferenceHistory, PreferenceSubmission
)


@admin.register(LecturerPreference)
class LecturerPreferenceAdmin(admin.ModelAdmin):
    """
    Admin interface for lecturer preferences.
    """
    list_display = [
        'user_name', 'user_email', 'academic_year', 'semester', 
        'status', 'preferred_workload', 'course_count', 'time_slot_count',
        'submitted_at', 'approved_at'
    ]
    list_filter = [
        'status', 'academic_year', 'semester', 'preferred_workload',
        'user__department', 'submitted_at', 'approved_at'
    ]
    search_fields = [
        'user__first_name', 'user__last_name', 'user__email',
        'user__department', 'comments'
    ]
    readonly_fields = [
        'id', 'created_at', 'updated_at', 'submitted_at', 
        'approved_at', 'course_count', 'time_slot_count'
    ]
    filter_horizontal = ['preferred_courses', 'available_time_slots']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('id', 'user', 'academic_year', 'semester', 'status')
        }),
        ('Workload Preferences', {
            'fields': ('preferred_workload', 'max_courses_per_semester', 'max_hours_per_week')
        }),
        ('Course and Time Preferences', {
            'fields': ('preferred_courses', 'available_time_slots')
        }),
        ('Additional Information', {
            'fields': ('constraints', 'comments', 'admin_notes')
        }),
        ('Workflow', {
            'fields': ('submitted_at', 'approved_at', 'approved_by')
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at', 'course_count', 'time_slot_count'),
            'classes': ('collapse',)
        }),
    )

    def user_name(self, obj):
        return obj.user.get_full_name()
    user_name.short_description = 'User Name'

    def user_email(self, obj):
        return obj.user.email
    user_email.short_description = 'Email'

    def course_count(self, obj):
        return obj.preferred_courses.count()
    course_count.short_description = 'Preferred Courses'

    def time_slot_count(self, obj):
        return obj.available_time_slots.count()
    time_slot_count.short_description = 'Available Time Slots'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'user', 'approved_by'
        ).prefetch_related('preferred_courses', 'available_time_slots')

    actions = ['approve_preferences', 'reject_preferences']

    def approve_preferences(self, request, queryset):
        """Approve selected preferences."""
        count = 0
        for preference in queryset.filter(status='submitted'):
            preference.approve(request.user)
            count += 1
        
        self.message_user(request, f'{count} preferences approved successfully.')
    approve_preferences.short_description = 'Approve selected preferences'

    def reject_preferences(self, request, queryset):
        """Reject selected preferences."""
        count = 0
        for preference in queryset.filter(status='submitted'):
            preference.reject()
            count += 1
        
        self.message_user(request, f'{count} preferences rejected.')
    reject_preferences.short_description = 'Reject selected preferences'


class StudentCoursePreferenceInline(admin.TabularInline):
    """
    Inline admin for student course preferences.
    """
    model = StudentCoursePreference
    extra = 0
    fields = ['course', 'priority', 'is_required', 'notes']
    autocomplete_fields = ['course']


@admin.register(StudentPreference)
class StudentPreferenceAdmin(admin.ModelAdmin):
    """
    Admin interface for student preferences.
    """
    list_display = [
        'user_name', 'user_email', 'user_year', 'academic_year', 'semester',
        'status', 'course_count', 'total_credits', 'submitted_at', 'approved_at'
    ]
    list_filter = [
        'status', 'academic_year', 'semester', 'user__year_of_study',
        'user__department', 'submitted_at', 'approved_at'
    ]
    search_fields = [
        'user__first_name', 'user__last_name', 'user__email',
        'user__student_id', 'user__department', 'comments'
    ]
    readonly_fields = [
        'id', 'created_at', 'updated_at', 'submitted_at', 
        'approved_at', 'course_count', 'total_credits'
    ]
    filter_horizontal = ['preferred_time_slots', 'unavailable_time_slots']
    inlines = [StudentCoursePreferenceInline]
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('id', 'user', 'academic_year', 'semester', 'status')
        }),
        ('Course Limits', {
            'fields': ('max_courses_per_semester', 'max_credits_per_semester')
        }),
        ('Schedule Preferences', {
            'fields': (
                'preferred_time_slots', 'unavailable_time_slots',
                'prefer_morning_classes', 'prefer_afternoon_classes',
                'prefer_consecutive_classes', 'avoid_friday_classes'
            )
        }),
        ('Additional Information', {
            'fields': ('constraints', 'comments', 'admin_notes')
        }),
        ('Workflow', {
            'fields': ('submitted_at', 'approved_at', 'approved_by')
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at', 'course_count', 'total_credits'),
            'classes': ('collapse',)
        }),
    )

    def user_name(self, obj):
        return obj.user.get_full_name()
    user_name.short_description = 'Student Name'

    def user_email(self, obj):
        return obj.user.email
    user_email.short_description = 'Email'

    def user_year(self, obj):
        return obj.user.year_of_study or 'N/A'
    user_year.short_description = 'Year'

    def course_count(self, obj):
        return obj.studentcoursepreference_set.count()
    course_count.short_description = 'Course Preferences'

    def total_credits(self, obj):
        return obj.get_total_preferred_credits()
    total_credits.short_description = 'Total Credits'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'user', 'approved_by'
        ).prefetch_related(
            'studentcoursepreference_set__course',
            'preferred_time_slots',
            'unavailable_time_slots'
        )


@admin.register(PreferenceHistory)
class PreferenceHistoryAdmin(admin.ModelAdmin):
    """
    Admin interface for preference history.
    """
    list_display = [
        'preference_type', 'user_name', 'field_name', 'changed_by_name',
        'change_reason', 'created_at'
    ]
    list_filter = [
        'preference_type', 'field_name', 'created_at'
    ]
    search_fields = [
        'user__first_name', 'user__last_name', 'user__email',
        'changed_by__first_name', 'changed_by__last_name',
        'field_name', 'change_reason'
    ]
    readonly_fields = ['id', 'created_at']
    
    fieldsets = (
        ('Change Information', {
            'fields': ('preference_type', 'preference_id', 'user', 'changed_by')
        }),
        ('Change Details', {
            'fields': ('field_name', 'old_value', 'new_value', 'change_reason')
        }),
        ('Metadata', {
            'fields': ('id', 'created_at'),
            'classes': ('collapse',)
        }),
    )

    def user_name(self, obj):
        return obj.user.get_full_name()
    user_name.short_description = 'User'

    def changed_by_name(self, obj):
        return obj.changed_by.get_full_name()
    changed_by_name.short_description = 'Changed By'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user', 'changed_by')


@admin.register(PreferenceSubmission)
class PreferenceSubmissionAdmin(admin.ModelAdmin):
    """
    Admin interface for preference submissions.
    """
    list_display = [
        'academic_year', 'semester', 'status', 'submitted_by_name',
        'lecturer_count', 'student_count', 'submitted_at', 'processed_at'
    ]
    list_filter = [
        'status', 'academic_year', 'semester', 'submitted_at', 'processed_at'
    ]
    search_fields = [
        'academic_year', 'semester', 'submitted_by__first_name',
        'submitted_by__last_name', 'submitted_by__email'
    ]
    readonly_fields = [
        'id', 'submitted_at', 'processed_at', 'processing_time_seconds',
        'lecturer_count', 'student_count', 'validation_errors'
    ]
    
    fieldsets = (
        ('Submission Information', {
            'fields': ('id', 'academic_year', 'semester', 'status', 'submitted_by')
        }),
        ('Statistics', {
            'fields': (
                'lecturer_count', 'student_count', 'validation_errors',
                'processing_time_seconds'
            )
        }),
        ('Timing', {
            'fields': ('submitted_at', 'processed_at')
        }),
        ('Data', {
            'fields': ('submission_data', 'response_data', 'error_details'),
            'classes': ('collapse',)
        }),
    )

    def submitted_by_name(self, obj):
        return obj.submitted_by.get_full_name()
    submitted_by_name.short_description = 'Submitted By'

    def lecturer_count(self, obj):
        return obj.total_lecturer_preferences
    lecturer_count.short_description = 'Lecturer Preferences'

    def student_count(self, obj):
        return obj.total_student_preferences
    student_count.short_description = 'Student Preferences'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('submitted_by')

    actions = ['reprocess_submissions']

    def reprocess_submissions(self, request, queryset):
        """Reprocess selected submissions."""
        from preferences.tasks import process_preference_submission
        
        count = 0
        for submission in queryset.filter(status__in=['failed', 'completed']):
            submission.status = 'pending'
            submission.processed_at = None
            submission.processing_time_seconds = None
            submission.error_details = []
            submission.save()
            
            process_preference_submission.delay(str(submission.id))
            count += 1
        
        self.message_user(request, f'{count} submissions queued for reprocessing.')
    reprocess_submissions.short_description = 'Reprocess selected submissions'
