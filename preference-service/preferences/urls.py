"""
URLs for preference collection endpoints.
"""
from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON><PERSON><PERSON>
from . import views

app_name = 'preferences'

# Create router for viewsets
router = DefaultRouter()
router.register(r'lecturer-preferences', views.LecturerPreferenceViewSet, basename='lecturer-preference')
router.register(r'student-preferences', views.StudentPreferenceViewSet, basename='student-preference')
router.register(r'submissions', views.PreferenceSubmissionViewSet, basename='preference-submission')

urlpatterns = [
    # ViewSet URLs
    path('', include(router.urls)),
    
    # Function-based view URLs
    path('summary/', views.preference_summary, name='preference-summary'),
    path('analytics/', views.preference_analytics, name='preference-analytics'),
]
