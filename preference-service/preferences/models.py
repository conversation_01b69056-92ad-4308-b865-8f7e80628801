"""
Preference models for lecturer and student preference collection.
"""
from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from core.models import BaseModel, User, Course, TimeSlot
import json


class LecturerPreference(BaseModel):
    """
    Lecturer preference model for collecting teaching preferences.
    """
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('submitted', 'Submitted'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
    ]

    WORKLOAD_CHOICES = [
        ('light', 'Light (< 12 hours/week)'),
        ('normal', 'Normal (12-18 hours/week)'),
        ('heavy', 'Heavy (> 18 hours/week)'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='lecturer_preferences')
    academic_year = models.CharField(max_length=20, help_text="e.g., 2023-2024")
    semester = models.Char<PERSON>ield(max_length=20, help_text="e.g., Fall, Spring")
    
    # Status and workflow
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    submitted_at = models.DateTimeField(null=True, blank=True)
    approved_at = models.DateTimeField(null=True, blank=True)
    approved_by = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='approved_lecturer_preferences'
    )
    
    # Teaching preferences
    preferred_workload = models.CharField(max_length=20, choices=WORKLOAD_CHOICES, default='normal')
    max_courses_per_semester = models.IntegerField(
        default=3,
        validators=[MinValueValidator(1), MaxValueValidator(10)]
    )
    max_hours_per_week = models.IntegerField(
        default=18,
        validators=[MinValueValidator(1), MaxValueValidator(40)]
    )
    
    # Course preferences
    preferred_courses = models.ManyToManyField(
        Course,
        blank=True,
        related_name='preferred_by_lecturers',
        help_text="Courses the lecturer prefers to teach"
    )
    
    # Time preferences
    available_time_slots = models.ManyToManyField(
        TimeSlot,
        blank=True,
        related_name='available_lecturers',
        help_text="Time slots when the lecturer is available"
    )
    
    # Constraints and special requirements
    constraints = models.JSONField(
        default=dict,
        help_text="Additional constraints and requirements"
    )
    
    # Comments and notes
    comments = models.TextField(blank=True, help_text="Additional comments or requirements")
    admin_notes = models.TextField(blank=True, help_text="Internal notes from administrators")

    class Meta:
        db_table = 'lecturer_preferences'
        verbose_name = 'Lecturer Preference'
        verbose_name_plural = 'Lecturer Preferences'
        unique_together = ['user', 'academic_year', 'semester']
        indexes = [
            models.Index(fields=['user', 'academic_year', 'semester']),
            models.Index(fields=['status']),
            models.Index(fields=['submitted_at']),
            models.Index(fields=['approved_at']),
        ]

    def __str__(self):
        return f"{self.user.get_full_name()} - {self.academic_year} {self.semester}"

    def submit(self):
        """Submit preferences for approval."""
        self.status = 'submitted'
        self.submitted_at = timezone.now()
        self.save()

    def approve(self, approved_by_user):
        """Approve preferences."""
        self.status = 'approved'
        self.approved_at = timezone.now()
        self.approved_by = approved_by_user
        self.save()

    def reject(self):
        """Reject preferences."""
        self.status = 'rejected'
        self.save()

    def get_preferred_course_codes(self):
        """Get list of preferred course codes."""
        return list(self.preferred_courses.values_list('code', flat=True))

    def get_available_time_slots_by_day(self):
        """Get available time slots grouped by day."""
        slots_by_day = {}
        for slot in self.available_time_slots.all():
            day = slot.day_of_week
            if day not in slots_by_day:
                slots_by_day[day] = []
            slots_by_day[day].append({
                'start_time': slot.start_time.strftime('%H:%M'),
                'end_time': slot.end_time.strftime('%H:%M'),
                'duration': slot.duration_minutes
            })
        return slots_by_day

    def has_time_conflict(self, time_slot):
        """Check if there's a conflict with the given time slot."""
        for available_slot in self.available_time_slots.all():
            if available_slot.overlaps_with(time_slot):
                return False
        return True


class StudentPreference(BaseModel):
    """
    Student preference model for collecting course and schedule preferences.
    """
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('submitted', 'Submitted'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
    ]

    PRIORITY_CHOICES = [
        (1, 'High Priority'),
        (2, 'Medium Priority'),
        (3, 'Low Priority'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='student_preferences')
    academic_year = models.CharField(max_length=20, help_text="e.g., 2023-2024")
    semester = models.CharField(max_length=20, help_text="e.g., Fall, Spring")
    
    # Status and workflow
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    submitted_at = models.DateTimeField(null=True, blank=True)
    approved_at = models.DateTimeField(null=True, blank=True)
    approved_by = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='approved_student_preferences'
    )
    
    # Course preferences
    preferred_courses = models.ManyToManyField(
        Course,
        through='StudentCoursePreference',
        related_name='preferred_by_students',
        help_text="Courses the student wants to take"
    )
    
    # Schedule preferences
    preferred_time_slots = models.ManyToManyField(
        TimeSlot,
        blank=True,
        related_name='preferred_by_students',
        help_text="Preferred time slots for classes"
    )
    
    unavailable_time_slots = models.ManyToManyField(
        TimeSlot,
        blank=True,
        related_name='unavailable_for_students',
        help_text="Time slots when student is not available"
    )
    
    # Study preferences
    max_courses_per_semester = models.IntegerField(
        default=5,
        validators=[MinValueValidator(1), MaxValueValidator(10)]
    )
    max_credits_per_semester = models.IntegerField(
        default=18,
        validators=[MinValueValidator(1), MaxValueValidator(30)]
    )
    
    # Schedule constraints
    prefer_morning_classes = models.BooleanField(default=False)
    prefer_afternoon_classes = models.BooleanField(default=False)
    prefer_consecutive_classes = models.BooleanField(default=True)
    avoid_friday_classes = models.BooleanField(default=False)
    
    # Additional constraints
    constraints = models.JSONField(
        default=dict,
        help_text="Additional constraints and requirements"
    )
    
    # Comments and notes
    comments = models.TextField(blank=True, help_text="Additional comments or requirements")
    admin_notes = models.TextField(blank=True, help_text="Internal notes from administrators")

    class Meta:
        db_table = 'student_preferences'
        verbose_name = 'Student Preference'
        verbose_name_plural = 'Student Preferences'
        unique_together = ['user', 'academic_year', 'semester']
        indexes = [
            models.Index(fields=['user', 'academic_year', 'semester']),
            models.Index(fields=['status']),
            models.Index(fields=['submitted_at']),
            models.Index(fields=['approved_at']),
        ]

    def __str__(self):
        return f"{self.user.get_full_name()} - {self.academic_year} {self.semester}"

    def submit(self):
        """Submit preferences for approval."""
        self.status = 'submitted'
        self.submitted_at = timezone.now()
        self.save()

    def approve(self, approved_by_user):
        """Approve preferences."""
        self.status = 'approved'
        self.approved_at = timezone.now()
        self.approved_by = approved_by_user
        self.save()

    def reject(self):
        """Reject preferences."""
        self.status = 'rejected'
        self.save()

    def get_preferred_courses_by_priority(self):
        """Get preferred courses grouped by priority."""
        course_prefs = self.studentcoursepreference_set.all().select_related('course')
        courses_by_priority = {}
        for pref in course_prefs:
            priority = pref.priority
            if priority not in courses_by_priority:
                courses_by_priority[priority] = []
            courses_by_priority[priority].append({
                'course': pref.course,
                'is_required': pref.is_required,
                'notes': pref.notes
            })
        return courses_by_priority

    def get_total_preferred_credits(self):
        """Calculate total credits for preferred courses."""
        return sum(
            pref.course.credits 
            for pref in self.studentcoursepreference_set.all()
        )


class StudentCoursePreference(BaseModel):
    """
    Through model for student course preferences with additional details.
    """
    student_preference = models.ForeignKey(StudentPreference, on_delete=models.CASCADE)
    course = models.ForeignKey(Course, on_delete=models.CASCADE)
    priority = models.IntegerField(
        choices=StudentPreference.PRIORITY_CHOICES,
        default=2,
        help_text="Priority level for this course"
    )
    is_required = models.BooleanField(
        default=False,
        help_text="Whether this course is required for graduation"
    )
    notes = models.TextField(blank=True, help_text="Additional notes about this course preference")

    class Meta:
        db_table = 'student_course_preferences'
        verbose_name = 'Student Course Preference'
        verbose_name_plural = 'Student Course Preferences'
        unique_together = ['student_preference', 'course']
        indexes = [
            models.Index(fields=['student_preference', 'priority']),
            models.Index(fields=['course', 'priority']),
            models.Index(fields=['is_required']),
        ]

    def __str__(self):
        return f"{self.student_preference.user.get_full_name()} - {self.course.code}"


class PreferenceHistory(BaseModel):
    """
    Track changes to preferences for audit purposes.
    """
    PREFERENCE_TYPE_CHOICES = [
        ('lecturer', 'Lecturer Preference'),
        ('student', 'Student Preference'),
    ]

    preference_type = models.CharField(max_length=20, choices=PREFERENCE_TYPE_CHOICES)
    preference_id = models.UUIDField(help_text="ID of the preference object")
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='preference_history')
    changed_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='preference_changes')
    
    # Change details
    field_name = models.CharField(max_length=100)
    old_value = models.TextField(blank=True)
    new_value = models.TextField(blank=True)
    change_reason = models.CharField(max_length=200, blank=True)

    class Meta:
        db_table = 'preference_history'
        verbose_name = 'Preference History'
        verbose_name_plural = 'Preference Histories'
        indexes = [
            models.Index(fields=['preference_type', 'preference_id']),
            models.Index(fields=['user']),
            models.Index(fields=['changed_by']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"{self.preference_type} - {self.field_name} changed for {self.user.get_full_name()}"


class PreferenceSubmission(BaseModel):
    """
    Track preference submissions for timetable generation.
    """
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    ]

    academic_year = models.CharField(max_length=20)
    semester = models.CharField(max_length=20)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    
    # Submission details
    submitted_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='preference_submissions')
    submitted_at = models.DateTimeField(default=timezone.now)
    
    # Processing details
    processed_at = models.DateTimeField(null=True, blank=True)
    processing_time_seconds = models.IntegerField(null=True, blank=True)
    
    # Statistics
    total_lecturer_preferences = models.IntegerField(default=0)
    total_student_preferences = models.IntegerField(default=0)
    validation_errors = models.IntegerField(default=0)
    
    # Results
    submission_data = models.JSONField(
        default=dict,
        help_text="Compiled preference data sent to timetable generation"
    )
    response_data = models.JSONField(
        default=dict,
        help_text="Response from timetable generation service"
    )
    error_details = models.JSONField(
        default=list,
        help_text="Error details if submission failed"
    )

    class Meta:
        db_table = 'preference_submissions'
        verbose_name = 'Preference Submission'
        verbose_name_plural = 'Preference Submissions'
        unique_together = ['academic_year', 'semester']
        indexes = [
            models.Index(fields=['academic_year', 'semester']),
            models.Index(fields=['status']),
            models.Index(fields=['submitted_at']),
            models.Index(fields=['processed_at']),
        ]

    def __str__(self):
        return f"Preference Submission - {self.academic_year} {self.semester}"

    def mark_as_processing(self):
        """Mark submission as processing."""
        self.status = 'processing'
        self.save()

    def mark_as_completed(self, processing_time=None):
        """Mark submission as completed."""
        self.status = 'completed'
        self.processed_at = timezone.now()
        if processing_time:
            self.processing_time_seconds = processing_time
        self.save()

    def mark_as_failed(self, error_details=None):
        """Mark submission as failed."""
        self.status = 'failed'
        self.processed_at = timezone.now()
        if error_details:
            self.error_details = error_details
        self.save()
