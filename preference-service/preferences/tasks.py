"""
Celery tasks for preference collection service.
"""
from celery import shared_task
from django.conf import settings
from django.utils import timezone
from datetime import timedelta
import requests
import logging
import json

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3)
def process_preference_submission(self, submission_id):
    """
    Process preference submission and send to timetable generation service.
    """
    try:
        from preferences.models import PreferenceSubmission, LecturerPreference, StudentPreference
        from core.models import User, Course
        
        # Get submission
        submission = PreferenceSubmission.objects.get(id=submission_id)
        submission.mark_as_processing()
        
        start_time = timezone.now()
        
        # Collect lecturer preferences
        lecturer_preferences = LecturerPreference.objects.filter(
            academic_year=submission.academic_year,
            semester=submission.semester,
            status='approved',
            is_active=True
        ).select_related('user').prefetch_related(
            'preferred_courses', 'available_time_slots'
        )
        
        # Collect student preferences
        student_preferences = StudentPreference.objects.filter(
            academic_year=submission.academic_year,
            semester=submission.semester,
            status='approved',
            is_active=True
        ).select_related('user').prefetch_related(
            'studentcoursepreference_set__course',
            'preferred_time_slots',
            'unavailable_time_slots'
        )
        
        # Compile submission data
        submission_data = {
            'academic_year': submission.academic_year,
            'semester': submission.semester,
            'submitted_at': submission.submitted_at.isoformat(),
            'lecturer_preferences': [],
            'student_preferences': [],
            'metadata': {
                'total_lecturers': lecturer_preferences.count(),
                'total_students': student_preferences.count(),
                'submission_id': str(submission.id),
            }
        }
        
        # Process lecturer preferences
        for pref in lecturer_preferences:
            lecturer_data = {
                'user_id': str(pref.user.id),
                'user_email': pref.user.email,
                'user_name': pref.user.get_full_name(),
                'department': pref.user.department,
                'employee_id': pref.user.employee_id,
                'preferred_workload': pref.preferred_workload,
                'max_courses_per_semester': pref.max_courses_per_semester,
                'max_hours_per_week': pref.max_hours_per_week,
                'preferred_courses': [
                    {
                        'id': str(course.id),
                        'code': course.code,
                        'name': course.name,
                        'credits': course.credits,
                        'department': course.department,
                    }
                    for course in pref.preferred_courses.all()
                ],
                'available_time_slots': [
                    {
                        'id': str(slot.id),
                        'day_of_week': slot.day_of_week,
                        'start_time': slot.start_time.strftime('%H:%M'),
                        'end_time': slot.end_time.strftime('%H:%M'),
                        'duration_minutes': slot.duration_minutes,
                    }
                    for slot in pref.available_time_slots.all()
                ],
                'constraints': pref.constraints,
                'comments': pref.comments,
            }
            submission_data['lecturer_preferences'].append(lecturer_data)
        
        # Process student preferences
        for pref in student_preferences:
            student_data = {
                'user_id': str(pref.user.id),
                'user_email': pref.user.email,
                'user_name': pref.user.get_full_name(),
                'department': pref.user.department,
                'student_id': pref.user.student_id,
                'year_of_study': pref.user.year_of_study,
                'max_courses_per_semester': pref.max_courses_per_semester,
                'max_credits_per_semester': pref.max_credits_per_semester,
                'course_preferences': [
                    {
                        'course': {
                            'id': str(cp.course.id),
                            'code': cp.course.code,
                            'name': cp.course.name,
                            'credits': cp.course.credits,
                            'department': cp.course.department,
                        },
                        'priority': cp.priority,
                        'is_required': cp.is_required,
                        'notes': cp.notes,
                    }
                    for cp in pref.studentcoursepreference_set.all()
                ],
                'preferred_time_slots': [
                    {
                        'id': str(slot.id),
                        'day_of_week': slot.day_of_week,
                        'start_time': slot.start_time.strftime('%H:%M'),
                        'end_time': slot.end_time.strftime('%H:%M'),
                    }
                    for slot in pref.preferred_time_slots.all()
                ],
                'unavailable_time_slots': [
                    {
                        'id': str(slot.id),
                        'day_of_week': slot.day_of_week,
                        'start_time': slot.start_time.strftime('%H:%M'),
                        'end_time': slot.end_time.strftime('%H:%M'),
                    }
                    for slot in pref.unavailable_time_slots.all()
                ],
                'schedule_preferences': {
                    'prefer_morning_classes': pref.prefer_morning_classes,
                    'prefer_afternoon_classes': pref.prefer_afternoon_classes,
                    'prefer_consecutive_classes': pref.prefer_consecutive_classes,
                    'avoid_friday_classes': pref.avoid_friday_classes,
                },
                'constraints': pref.constraints,
                'comments': pref.comments,
            }
            submission_data['student_preferences'].append(student_data)
        
        # Update submission with compiled data
        submission.submission_data = submission_data
        submission.total_lecturer_preferences = len(submission_data['lecturer_preferences'])
        submission.total_student_preferences = len(submission_data['student_preferences'])
        submission.save()
        
        # Send to timetable generation service
        response_data = send_to_timetable_service(submission_data)
        
        # Calculate processing time
        end_time = timezone.now()
        processing_time = (end_time - start_time).total_seconds()
        
        # Update submission with response
        submission.response_data = response_data
        submission.mark_as_completed(processing_time)
        
        logger.info(f"Preference submission {submission_id} processed successfully")
        return {
            'status': 'completed',
            'processing_time': processing_time,
            'lecturer_preferences': submission.total_lecturer_preferences,
            'student_preferences': submission.total_student_preferences,
        }
        
    except Exception as exc:
        logger.error(f"Error processing preference submission {submission_id}: {str(exc)}")
        
        # Update submission with error
        try:
            submission = PreferenceSubmission.objects.get(id=submission_id)
            submission.mark_as_failed([str(exc)])
        except:
            pass
        
        # Retry the task
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60 * (2 ** self.request.retries))
        
        raise exc


def send_to_timetable_service(submission_data):
    """
    Send preference data to timetable generation service.
    """
    try:
        timetable_service_url = f"{settings.TIMETABLE_SERVICE_URL}/api/preferences/submit/"
        
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Internal {settings.INTERNAL_SERVICE_TOKEN}',
        }
        
        response = requests.post(
            timetable_service_url,
            json=submission_data,
            headers=headers,
            timeout=300  # 5 minutes timeout
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            logger.error(f"Timetable service returned {response.status_code}: {response.text}")
            return {
                'error': f"Timetable service error: {response.status_code}",
                'message': response.text
            }
            
    except requests.RequestException as e:
        logger.error(f"Error sending to timetable service: {str(e)}")
        return {
            'error': 'Connection error',
            'message': str(e)
        }


@shared_task
def validate_all_preferences():
    """
    Validate all active preferences for consistency.
    """
    try:
        from preferences.models import LecturerPreference, StudentPreference
        from validation.models import ValidationResult
        
        # Validate lecturer preferences
        lecturer_prefs = LecturerPreference.objects.filter(
            status__in=['submitted', 'approved'],
            is_active=True
        )
        
        for pref in lecturer_prefs:
            validate_lecturer_preference.delay(str(pref.id))
        
        # Validate student preferences
        student_prefs = StudentPreference.objects.filter(
            status__in=['submitted', 'approved'],
            is_active=True
        )
        
        for pref in student_prefs:
            validate_student_preference.delay(str(pref.id))
        
        logger.info(f"Triggered validation for {lecturer_prefs.count()} lecturer and {student_prefs.count()} student preferences")
        
    except Exception as e:
        logger.error(f"Error in validate_all_preferences: {str(e)}")


@shared_task
def validate_lecturer_preference(preference_id):
    """
    Validate a specific lecturer preference.
    """
    try:
        from preferences.models import LecturerPreference
        from validation.tasks import validate_preference
        
        preference = LecturerPreference.objects.get(id=preference_id)
        validate_preference.delay('lecturer', str(preference.id))
        
    except Exception as e:
        logger.error(f"Error validating lecturer preference {preference_id}: {str(e)}")


@shared_task
def validate_student_preference(preference_id):
    """
    Validate a specific student preference.
    """
    try:
        from preferences.models import StudentPreference
        from validation.tasks import validate_preference
        
        preference = StudentPreference.objects.get(id=preference_id)
        validate_preference.delay('student', str(preference.id))
        
    except Exception as e:
        logger.error(f"Error validating student preference {preference_id}: {str(e)}")


@shared_task
def sync_user_data():
    """
    Sync user data from user management service.
    """
    try:
        from core.models import User
        
        # Get all active users from user service
        user_service_url = f"{settings.USER_SERVICE_URL}/api/users/"
        headers = {
            'Authorization': f'Internal {settings.INTERNAL_SERVICE_TOKEN}',
        }
        
        response = requests.get(user_service_url, headers=headers, timeout=30)
        
        if response.status_code == 200:
            users_data = response.json()
            
            synced_count = 0
            for user_data in users_data.get('results', []):
                try:
                    # Update or create user
                    user, created = User.objects.update_or_create(
                        id=user_data['id'],
                        defaults={
                            'email': user_data['email'],
                            'username': user_data['username'],
                            'first_name': user_data.get('first_name', ''),
                            'last_name': user_data.get('last_name', ''),
                            'phone_number': user_data.get('phone_number', ''),
                            'role_name': user_data.get('role', {}).get('name', '') if user_data.get('role') else '',
                            'role_display_name': user_data.get('role', {}).get('display_name', '') if user_data.get('role') else '',
                            'is_verified': user_data.get('is_verified', False),
                            'is_blocked': user_data.get('is_blocked', False),
                            'department': user_data.get('profile', {}).get('department', ''),
                            'faculty': user_data.get('profile', {}).get('faculty', ''),
                            'employee_id': user_data.get('profile', {}).get('employee_id', ''),
                            'student_id': user_data.get('profile', {}).get('student_id', ''),
                            'year_of_study': user_data.get('profile', {}).get('year_of_study'),
                        }
                    )
                    synced_count += 1
                    
                except Exception as e:
                    logger.error(f"Error syncing user {user_data.get('id')}: {str(e)}")
            
            logger.info(f"Synced {synced_count} users from user service")
            
        else:
            logger.error(f"Failed to fetch users from user service: {response.status_code}")
            
    except Exception as e:
        logger.error(f"Error in sync_user_data: {str(e)}")


@shared_task
def cleanup_old_preference_history():
    """
    Clean up old preference history records.
    """
    try:
        from preferences.models import PreferenceHistory
        
        # Delete history older than retention period
        retention_days = settings.PREFERENCE_HISTORY_RETENTION_DAYS
        cutoff_date = timezone.now() - timedelta(days=retention_days)
        
        deleted_count = PreferenceHistory.objects.filter(
            created_at__lt=cutoff_date
        ).delete()[0]
        
        logger.info(f"Cleaned up {deleted_count} old preference history records")
        
    except Exception as e:
        logger.error(f"Error in cleanup_old_preference_history: {str(e)}")


@shared_task
def send_preference_reminders():
    """
    Send reminders to users who haven't submitted preferences.
    """
    try:
        from core.models import User
        from preferences.models import LecturerPreference, StudentPreference
        
        # Get current academic year and semester (this would be configurable)
        current_year = "2023-2024"
        current_semester = "Fall"
        
        # Find lecturers without preferences
        lecturers_with_prefs = LecturerPreference.objects.filter(
            academic_year=current_year,
            semester=current_semester,
            is_active=True
        ).values_list('user_id', flat=True)
        
        lecturers_without_prefs = User.objects.filter(
            role_name='lecturer',
            is_active=True,
            is_blocked=False
        ).exclude(id__in=lecturers_with_prefs)
        
        # Find students without preferences
        students_with_prefs = StudentPreference.objects.filter(
            academic_year=current_year,
            semester=current_semester,
            is_active=True
        ).values_list('user_id', flat=True)
        
        students_without_prefs = User.objects.filter(
            role_name='student',
            is_active=True,
            is_blocked=False
        ).exclude(id__in=students_with_prefs)
        
        # Send reminders (this would integrate with notification service)
        reminder_count = 0
        
        for lecturer in lecturers_without_prefs:
            # TODO: Send reminder notification
            reminder_count += 1
        
        for student in students_without_prefs:
            # TODO: Send reminder notification
            reminder_count += 1
        
        logger.info(f"Sent {reminder_count} preference reminders")
        
    except Exception as e:
        logger.error(f"Error in send_preference_reminders: {str(e)}")


@shared_task
def validate_user_preferences(user_id):
    """
    Validate all preferences for a specific user.
    """
    try:
        from preferences.models import LecturerPreference, StudentPreference
        from core.models import User
        
        user = User.objects.get(id=user_id)
        
        if user.is_lecturer():
            # Validate lecturer preferences
            lecturer_prefs = LecturerPreference.objects.filter(
                user=user,
                is_active=True
            )
            
            for pref in lecturer_prefs:
                validate_lecturer_preference.delay(str(pref.id))
                
        elif user.is_student():
            # Validate student preferences
            student_prefs = StudentPreference.objects.filter(
                user=user,
                is_active=True
            )
            
            for pref in student_prefs:
                validate_student_preference.delay(str(pref.id))
        
        logger.info(f"Triggered preference validation for user {user_id}")
        
    except Exception as e:
        logger.error(f"Error validating preferences for user {user_id}: {str(e)}")
