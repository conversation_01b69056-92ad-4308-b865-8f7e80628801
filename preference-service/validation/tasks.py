"""
Celery tasks for preference validation and conflict detection.
"""
from celery import shared_task
from django.conf import settings
from django.utils import timezone
import logging

logger = logging.getLogger(__name__)


@shared_task
def validate_preference(preference_type, preference_id):
    """
    Validate a specific preference.
    """
    try:
        from validation.models import ValidationResult, ValidationRule
        from preferences.models import LecturerPreference, StudentPreference
        
        # Get the preference
        if preference_type == 'lecturer':
            preference = LecturerPreference.objects.get(id=preference_id)
        elif preference_type == 'student':
            preference = StudentPreference.objects.get(id=preference_id)
        else:
            raise ValueError(f"Invalid preference type: {preference_type}")
        
        # Get applicable validation rules
        rules = ValidationRule.objects.filter(
            rule_type__in=[preference_type, 'global'],
            is_enabled=True
        )
        
        validation_results = []
        
        for rule in rules:
            try:
                # Apply validation rule
                result = apply_validation_rule(rule, preference)
                
                if result:
                    # Create validation result
                    validation_result = ValidationResult.objects.create(
                        preference_type=preference_type,
                        preference_id=preference.id,
                        user=preference.user,
                        status=result['status'],
                        validation_rule=rule,
                        message=result['message'],
                        details=result.get('details', {})
                    )
                    validation_results.append(validation_result)
                    
                    # Increment rule usage
                    rule.increment_usage()
                
            except Exception as e:
                logger.error(f"Error applying validation rule {rule.id}: {str(e)}")
        
        # Run custom validation logic
        custom_results = run_custom_validation(preference_type, preference)
        validation_results.extend(custom_results)
        
        # Detect conflicts
        conflicts = detect_conflicts(preference_type, preference)
        
        # Send WebSocket notification
        send_validation_notification(preference, validation_results, conflicts)
        
        logger.info(f"Validated {preference_type} preference {preference_id}: {len(validation_results)} issues found")
        
        return {
            'preference_id': str(preference_id),
            'preference_type': preference_type,
            'validation_results': len(validation_results),
            'conflicts': len(conflicts)
        }
        
    except Exception as e:
        logger.error(f"Error validating {preference_type} preference {preference_id}: {str(e)}")
        raise


def apply_validation_rule(rule, preference):
    """
    Apply a validation rule to a preference.
    """
    try:
        # Basic validation rules
        if rule.name == 'minimum_courses':
            return validate_minimum_courses(rule, preference)
        elif rule.name == 'maximum_workload':
            return validate_maximum_workload(rule, preference)
        elif rule.name == 'time_slot_availability':
            return validate_time_slot_availability(rule, preference)
        elif rule.name == 'course_prerequisites':
            return validate_course_prerequisites(rule, preference)
        elif rule.name == 'credit_limits':
            return validate_credit_limits(rule, preference)
        elif rule.name == 'schedule_conflicts':
            return validate_schedule_conflicts(rule, preference)
        
        # Custom validation logic (if provided)
        if rule.validation_logic:
            return execute_custom_validation_logic(rule, preference)
        
        return None
        
    except Exception as e:
        logger.error(f"Error applying validation rule {rule.name}: {str(e)}")
        return None


def validate_minimum_courses(rule, preference):
    """
    Validate minimum number of courses.
    """
    config = rule.rule_config
    min_courses = config.get('min_courses', 1)
    
    if hasattr(preference, 'preferred_courses'):
        # Lecturer preference
        course_count = preference.preferred_courses.count()
    else:
        # Student preference
        course_count = preference.studentcoursepreference_set.count()
    
    if course_count < min_courses:
        return {
            'status': 'invalid',
            'message': rule.error_message_template.format(
                min_courses=min_courses,
                actual_courses=course_count
            ),
            'details': {
                'min_courses': min_courses,
                'actual_courses': course_count
            }
        }
    
    return None


def validate_maximum_workload(rule, preference):
    """
    Validate maximum workload for lecturers.
    """
    if not hasattr(preference, 'max_hours_per_week'):
        return None
    
    config = rule.rule_config
    max_hours = config.get('max_hours', 40)
    
    if preference.max_hours_per_week > max_hours:
        return {
            'status': 'warning',
            'message': rule.error_message_template.format(
                max_hours=max_hours,
                requested_hours=preference.max_hours_per_week
            ),
            'details': {
                'max_hours': max_hours,
                'requested_hours': preference.max_hours_per_week
            }
        }
    
    return None


def validate_time_slot_availability(rule, preference):
    """
    Validate time slot availability.
    """
    if not hasattr(preference, 'available_time_slots'):
        return None
    
    config = rule.rule_config
    min_slots = config.get('min_time_slots', 5)
    
    slot_count = preference.available_time_slots.count()
    
    if slot_count < min_slots:
        return {
            'status': 'warning',
            'message': rule.error_message_template.format(
                min_slots=min_slots,
                actual_slots=slot_count
            ),
            'details': {
                'min_slots': min_slots,
                'actual_slots': slot_count
            }
        }
    
    return None


def validate_course_prerequisites(rule, preference):
    """
    Validate course prerequisites for students.
    """
    if not hasattr(preference, 'studentcoursepreference_set'):
        return None
    
    # This would require integration with course prerequisite data
    # For now, return None (no validation)
    return None


def validate_credit_limits(rule, preference):
    """
    Validate credit limits for students.
    """
    if not hasattr(preference, 'max_credits_per_semester'):
        return None
    
    config = rule.rule_config
    max_credits = config.get('max_credits', 18)
    
    total_credits = preference.get_total_preferred_credits()
    
    if total_credits > max_credits:
        return {
            'status': 'invalid',
            'message': rule.error_message_template.format(
                max_credits=max_credits,
                total_credits=total_credits
            ),
            'details': {
                'max_credits': max_credits,
                'total_credits': total_credits
            }
        }
    
    return None


def validate_schedule_conflicts(rule, preference):
    """
    Validate schedule conflicts.
    """
    # This would require complex scheduling logic
    # For now, return None (no validation)
    return None


def execute_custom_validation_logic(rule, preference):
    """
    Execute custom validation logic (Python code).
    """
    try:
        # This is a simplified implementation
        # In production, you'd want to use a sandboxed execution environment
        
        # Create a safe execution context
        context = {
            'preference': preference,
            'rule_config': rule.rule_config,
            'timezone': timezone,
        }
        
        # Execute the validation logic
        exec(rule.validation_logic, context)
        
        # Check if validation failed
        if context.get('validation_failed'):
            return {
                'status': context.get('status', 'invalid'),
                'message': context.get('message', 'Custom validation failed'),
                'details': context.get('details', {})
            }
        
        return None
        
    except Exception as e:
        logger.error(f"Error executing custom validation logic: {str(e)}")
        return {
            'status': 'invalid',
            'message': f"Validation error: {str(e)}",
            'details': {'error': str(e)}
        }


def run_custom_validation(preference_type, preference):
    """
    Run custom validation logic specific to preference types.
    """
    results = []
    
    try:
        if preference_type == 'lecturer':
            results.extend(validate_lecturer_specific(preference))
        elif preference_type == 'student':
            results.extend(validate_student_specific(preference))
        
    except Exception as e:
        logger.error(f"Error in custom validation: {str(e)}")
    
    return results


def validate_lecturer_specific(preference):
    """
    Lecturer-specific validation logic.
    """
    from validation.models import ValidationResult
    
    results = []
    
    # Check if workload is realistic
    if preference.max_hours_per_week > 0 and preference.max_courses_per_semester > 0:
        avg_hours_per_course = preference.max_hours_per_week / preference.max_courses_per_semester
        
        if avg_hours_per_course < 2:
            results.append(ValidationResult(
                preference_type='lecturer',
                preference_id=preference.id,
                user=preference.user,
                status='warning',
                message='Average hours per course seems low (less than 2 hours)',
                details={
                    'avg_hours_per_course': avg_hours_per_course,
                    'max_hours_per_week': preference.max_hours_per_week,
                    'max_courses_per_semester': preference.max_courses_per_semester
                }
            ))
    
    return results


def validate_student_specific(preference):
    """
    Student-specific validation logic.
    """
    from validation.models import ValidationResult
    
    results = []
    
    # Check if credit load is appropriate for year of study
    if preference.user.year_of_study and preference.max_credits_per_semester:
        expected_credits = {1: 15, 2: 16, 3: 17, 4: 18}.get(preference.user.year_of_study, 18)
        
        if preference.max_credits_per_semester > expected_credits + 3:
            results.append(ValidationResult(
                preference_type='student',
                preference_id=preference.id,
                user=preference.user,
                status='warning',
                message=f'Credit load seems high for year {preference.user.year_of_study} student',
                details={
                    'year_of_study': preference.user.year_of_study,
                    'requested_credits': preference.max_credits_per_semester,
                    'expected_credits': expected_credits
                }
            ))
    
    return results


def detect_conflicts(preference_type, preference):
    """
    Detect conflicts with other preferences.
    """
    from validation.models import ConflictDetection
    
    conflicts = []
    
    try:
        if preference_type == 'lecturer':
            conflicts.extend(detect_lecturer_conflicts(preference))
        elif preference_type == 'student':
            conflicts.extend(detect_student_conflicts(preference))
        
    except Exception as e:
        logger.error(f"Error detecting conflicts: {str(e)}")
    
    return conflicts


def detect_lecturer_conflicts(preference):
    """
    Detect conflicts for lecturer preferences.
    """
    # This would implement complex conflict detection logic
    # For now, return empty list
    return []


def detect_student_conflicts(preference):
    """
    Detect conflicts for student preferences.
    """
    # This would implement complex conflict detection logic
    # For now, return empty list
    return []


def send_validation_notification(preference, validation_results, conflicts):
    """
    Send WebSocket notification about validation results.
    """
    try:
        from channels.layers import get_channel_layer
        from asgiref.sync import async_to_sync
        
        channel_layer = get_channel_layer()
        
        if channel_layer:
            # Send to user-specific group
            group_name = f"preferences_group_{preference.user.id}"
            
            async_to_sync(channel_layer.group_send)(
                group_name,
                {
                    'type': 'validation_result',
                    'preference_id': str(preference.id),
                    'validation_status': 'completed',
                    'errors': [
                        {
                            'message': result.message,
                            'status': result.status,
                            'details': result.details
                        }
                        for result in validation_results if result.status == 'invalid'
                    ],
                    'warnings': [
                        {
                            'message': result.message,
                            'status': result.status,
                            'details': result.details
                        }
                        for result in validation_results if result.status == 'warning'
                    ],
                    'timestamp': timezone.now().isoformat()
                }
            )
            
            # Send conflicts if any
            for conflict in conflicts:
                async_to_sync(channel_layer.group_send)(
                    group_name,
                    {
                        'type': 'conflict_detected',
                        'conflict_id': str(conflict.id),
                        'conflict_type': conflict.conflict_type,
                        'severity': conflict.severity,
                        'affected_preferences': conflict.affected_preferences,
                        'description': conflict.description,
                        'timestamp': timezone.now().isoformat()
                    }
                )
        
    except Exception as e:
        logger.error(f"Error sending validation notification: {str(e)}")


@shared_task
def batch_validate_preferences(preference_type=None, academic_year=None, semester=None):
    """
    Validate preferences in batch.
    """
    try:
        from preferences.models import LecturerPreference, StudentPreference
        from validation.models import ValidationSession
        
        # Create validation session
        session = ValidationSession.objects.create(
            session_name=f"Batch validation - {timezone.now().strftime('%Y-%m-%d %H:%M')}",
            validation_parameters={
                'preference_type': preference_type,
                'academic_year': academic_year,
                'semester': semester
            }
        )
        
        preferences_to_validate = []
        
        # Get lecturer preferences
        if not preference_type or preference_type == 'lecturer':
            lecturer_prefs = LecturerPreference.objects.filter(is_active=True)
            if academic_year:
                lecturer_prefs = lecturer_prefs.filter(academic_year=academic_year)
            if semester:
                lecturer_prefs = lecturer_prefs.filter(semester=semester)
            
            for pref in lecturer_prefs:
                preferences_to_validate.append(('lecturer', str(pref.id)))
        
        # Get student preferences
        if not preference_type or preference_type == 'student':
            student_prefs = StudentPreference.objects.filter(is_active=True)
            if academic_year:
                student_prefs = student_prefs.filter(academic_year=academic_year)
            if semester:
                student_prefs = student_prefs.filter(semester=semester)
            
            for pref in student_prefs:
                preferences_to_validate.append(('student', str(pref.id)))
        
        # Update session with total count
        session.total_preferences = len(preferences_to_validate)
        session.save()
        
        # Validate each preference
        for pref_type, pref_id in preferences_to_validate:
            try:
                validate_preference.delay(pref_type, pref_id)
                session.validated_preferences += 1
                session.save()
                
            except Exception as e:
                logger.error(f"Error queuing validation for {pref_type} {pref_id}: {str(e)}")
        
        session.mark_as_completed()
        
        logger.info(f"Batch validation completed: {session.validated_preferences} preferences queued")
        
        return {
            'session_id': str(session.id),
            'total_preferences': session.total_preferences,
            'queued_validations': session.validated_preferences
        }
        
    except Exception as e:
        logger.error(f"Error in batch validation: {str(e)}")
        if 'session' in locals():
            session.mark_as_failed(str(e))
        raise
