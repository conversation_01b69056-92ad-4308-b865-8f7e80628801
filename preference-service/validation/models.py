"""
Models for preference validation and conflict detection.
"""
from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from core.models import BaseModel, User


class ValidationRule(BaseModel):
    """
    Configurable validation rules for preferences.
    """
    RULE_TYPE_CHOICES = [
        ('lecturer', 'Lecturer Preference Rule'),
        ('student', 'Student Preference Rule'),
        ('global', 'Global Rule'),
    ]

    SEVERITY_CHOICES = [
        ('error', 'Error'),
        ('warning', 'Warning'),
        ('info', 'Information'),
    ]

    name = models.CharField(max_length=200, help_text="Rule name")
    description = models.TextField(help_text="Rule description")
    rule_type = models.CharField(max_length=20, choices=RULE_TYPE_CHOICES)
    severity = models.CharField(max_length=10, choices=SEVERITY_CHOICES, default='error')
    
    # Rule configuration
    rule_config = models.JSONField(
        default=dict,
        help_text="Rule configuration and parameters"
    )
    
    # Rule logic (Python code or configuration)
    validation_logic = models.TextField(
        help_text="Validation logic (Python code or configuration)"
    )
    
    # Error message template
    error_message_template = models.TextField(
        help_text="Error message template with placeholders"
    )
    
    # Rule status
    is_enabled = models.BooleanField(default=True)
    
    # Usage tracking
    usage_count = models.IntegerField(default=0)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='validation_rules')

    class Meta:
        db_table = 'validation_rules'
        verbose_name = 'Validation Rule'
        verbose_name_plural = 'Validation Rules'
        indexes = [
            models.Index(fields=['rule_type']),
            models.Index(fields=['severity']),
            models.Index(fields=['is_enabled']),
            models.Index(fields=['created_by']),
        ]

    def __str__(self):
        return f"{self.name} ({self.get_rule_type_display()})"

    def increment_usage(self):
        """Increment usage count."""
        self.usage_count += 1
        self.save(update_fields=['usage_count'])


class ValidationResult(BaseModel):
    """
    Results of preference validation.
    """
    STATUS_CHOICES = [
        ('valid', 'Valid'),
        ('invalid', 'Invalid'),
        ('warning', 'Warning'),
    ]

    PREFERENCE_TYPE_CHOICES = [
        ('lecturer', 'Lecturer Preference'),
        ('student', 'Student Preference'),
    ]

    preference_type = models.CharField(max_length=20, choices=PREFERENCE_TYPE_CHOICES)
    preference_id = models.UUIDField(help_text="ID of the validated preference")
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='validation_results')
    
    # Validation details
    status = models.CharField(max_length=10, choices=STATUS_CHOICES)
    validation_rule = models.ForeignKey(
        ValidationRule,
        on_delete=models.CASCADE,
        related_name='results',
        null=True,
        blank=True
    )
    
    # Error/warning details
    message = models.TextField(help_text="Validation message")
    details = models.JSONField(
        default=dict,
        help_text="Additional validation details"
    )
    
    # Resolution tracking
    is_resolved = models.BooleanField(default=False)
    resolved_at = models.DateTimeField(null=True, blank=True)
    resolved_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='resolved_validations'
    )
    resolution_notes = models.TextField(blank=True)

    class Meta:
        db_table = 'validation_results'
        verbose_name = 'Validation Result'
        verbose_name_plural = 'Validation Results'
        indexes = [
            models.Index(fields=['preference_type', 'preference_id']),
            models.Index(fields=['user']),
            models.Index(fields=['status']),
            models.Index(fields=['validation_rule']),
            models.Index(fields=['is_resolved']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"Validation for {self.preference_type} - {self.status}"

    def resolve(self, resolved_by_user, notes=""):
        """Mark validation result as resolved."""
        from django.utils import timezone
        self.is_resolved = True
        self.resolved_at = timezone.now()
        self.resolved_by = resolved_by_user
        self.resolution_notes = notes
        self.save()


class ConflictDetection(BaseModel):
    """
    Detect and track conflicts between preferences.
    """
    CONFLICT_TYPE_CHOICES = [
        ('time_overlap', 'Time Slot Overlap'),
        ('resource_conflict', 'Resource Conflict'),
        ('capacity_exceeded', 'Capacity Exceeded'),
        ('prerequisite_missing', 'Prerequisite Missing'),
        ('schedule_conflict', 'Schedule Conflict'),
        ('workload_exceeded', 'Workload Exceeded'),
    ]

    SEVERITY_CHOICES = [
        ('critical', 'Critical'),
        ('high', 'High'),
        ('medium', 'Medium'),
        ('low', 'Low'),
    ]

    conflict_type = models.CharField(max_length=30, choices=CONFLICT_TYPE_CHOICES)
    severity = models.CharField(max_length=10, choices=SEVERITY_CHOICES, default='medium')
    
    # Affected preferences
    affected_preferences = models.JSONField(
        default=list,
        help_text="List of affected preference IDs and types"
    )
    
    # Conflict details
    description = models.TextField(help_text="Conflict description")
    conflict_data = models.JSONField(
        default=dict,
        help_text="Detailed conflict information"
    )
    
    # Suggested resolutions
    suggested_resolutions = models.JSONField(
        default=list,
        help_text="List of suggested resolutions"
    )
    
    # Resolution tracking
    is_resolved = models.BooleanField(default=False)
    resolved_at = models.DateTimeField(null=True, blank=True)
    resolved_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='resolved_conflicts'
    )
    resolution_method = models.TextField(blank=True)
    
    # Detection metadata
    detected_by_rule = models.CharField(max_length=200, blank=True)
    detection_timestamp = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'conflict_detections'
        verbose_name = 'Conflict Detection'
        verbose_name_plural = 'Conflict Detections'
        indexes = [
            models.Index(fields=['conflict_type']),
            models.Index(fields=['severity']),
            models.Index(fields=['is_resolved']),
            models.Index(fields=['detection_timestamp']),
            models.Index(fields=['resolved_by']),
        ]

    def __str__(self):
        return f"{self.get_conflict_type_display()} - {self.severity}"

    def resolve(self, resolved_by_user, resolution_method=""):
        """Mark conflict as resolved."""
        from django.utils import timezone
        self.is_resolved = True
        self.resolved_at = timezone.now()
        self.resolved_by = resolved_by_user
        self.resolution_method = resolution_method
        self.save()

    def get_affected_users(self):
        """Get list of users affected by this conflict."""
        user_ids = []
        for pref in self.affected_preferences:
            if 'user_id' in pref:
                user_ids.append(pref['user_id'])
        
        return User.objects.filter(id__in=user_ids)


class ValidationSession(BaseModel):
    """
    Track validation sessions for batch operations.
    """
    STATUS_CHOICES = [
        ('running', 'Running'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ]

    session_name = models.CharField(max_length=200, help_text="Validation session name")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='running')
    initiated_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='validation_sessions')
    
    # Session parameters
    validation_parameters = models.JSONField(
        default=dict,
        help_text="Parameters for this validation session"
    )
    
    # Progress tracking
    total_preferences = models.IntegerField(default=0)
    validated_preferences = models.IntegerField(default=0)
    errors_found = models.IntegerField(default=0)
    warnings_found = models.IntegerField(default=0)
    conflicts_detected = models.IntegerField(default=0)
    
    # Timing
    started_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    
    # Results summary
    results_summary = models.JSONField(
        default=dict,
        help_text="Summary of validation results"
    )

    class Meta:
        db_table = 'validation_sessions'
        verbose_name = 'Validation Session'
        verbose_name_plural = 'Validation Sessions'
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['initiated_by']),
            models.Index(fields=['started_at']),
            models.Index(fields=['completed_at']),
        ]

    def __str__(self):
        return f"{self.session_name} - {self.status}"

    @property
    def progress_percentage(self):
        """Calculate validation progress percentage."""
        if self.total_preferences == 0:
            return 0
        return round((self.validated_preferences / self.total_preferences) * 100, 2)

    @property
    def duration_seconds(self):
        """Calculate session duration in seconds."""
        if self.completed_at and self.started_at:
            return (self.completed_at - self.started_at).total_seconds()
        return None

    def mark_as_completed(self):
        """Mark session as completed."""
        from django.utils import timezone
        self.status = 'completed'
        self.completed_at = timezone.now()
        self.save()

    def mark_as_failed(self, error_message=""):
        """Mark session as failed."""
        from django.utils import timezone
        self.status = 'failed'
        self.completed_at = timezone.now()
        if error_message:
            self.results_summary['error'] = error_message
        self.save()

    def cancel(self):
        """Cancel the validation session."""
        from django.utils import timezone
        self.status = 'cancelled'
        self.completed_at = timezone.now()
        self.save()
