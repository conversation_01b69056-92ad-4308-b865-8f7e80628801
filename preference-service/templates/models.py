"""
Models for preference templates and bulk operations.
"""
from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from core.models import BaseModel, User, Course, TimeSlot


class PreferenceTemplate(BaseModel):
    """
    Template for common preference patterns.
    """
    TEMPLATE_TYPE_CHOICES = [
        ('lecturer', 'Lecturer Template'),
        ('student', 'Student Template'),
    ]

    name = models.CharField(max_length=200, help_text="Template name")
    description = models.TextField(blank=True, help_text="Template description")
    template_type = models.CharField(max_length=20, choices=TEMPLATE_TYPE_CHOICES)
    
    # Template data
    template_data = models.J<PERSON><PERSON>ield(
        default=dict,
        help_text="Template configuration data"
    )
    
    # Usage tracking
    usage_count = models.IntegerField(default=0)
    
    # Access control
    is_public = models.BooleanField(default=True, help_text="Whether template is available to all users")
    created_by = models.Foreign<PERSON><PERSON>(
        User,
        on_delete=models.CASCADE,
        related_name='created_templates'
    )
    
    # Department/role restrictions
    allowed_departments = models.JSONField(
        default=list,
        blank=True,
        help_text="List of departments that can use this template"
    )
    allowed_roles = models.JSONField(
        default=list,
        blank=True,
        help_text="List of roles that can use this template"
    )

    class Meta:
        db_table = 'preference_templates'
        verbose_name = 'Preference Template'
        verbose_name_plural = 'Preference Templates'
        indexes = [
            models.Index(fields=['template_type']),
            models.Index(fields=['is_public']),
            models.Index(fields=['created_by']),
            models.Index(fields=['usage_count']),
        ]

    def __str__(self):
        return f"{self.name} ({self.get_template_type_display()})"

    def can_be_used_by(self, user):
        """Check if user can use this template."""
        if not self.is_public and self.created_by != user:
            return False
        
        # Check department restrictions
        if self.allowed_departments and user.department not in self.allowed_departments:
            return False
        
        # Check role restrictions
        if self.allowed_roles and user.role_name not in self.allowed_roles:
            return False
        
        return True

    def increment_usage(self):
        """Increment usage count."""
        self.usage_count += 1
        self.save(update_fields=['usage_count'])


class BulkPreferenceOperation(BaseModel):
    """
    Track bulk preference operations.
    """
    OPERATION_TYPE_CHOICES = [
        ('import', 'Import Preferences'),
        ('export', 'Export Preferences'),
        ('bulk_update', 'Bulk Update'),
        ('template_apply', 'Apply Template'),
        ('validation', 'Bulk Validation'),
    ]

    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ]

    operation_type = models.CharField(max_length=20, choices=OPERATION_TYPE_CHOICES)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    initiated_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='bulk_operations')
    
    # Operation details
    total_records = models.IntegerField(default=0)
    processed_records = models.IntegerField(default=0)
    successful_records = models.IntegerField(default=0)
    failed_records = models.IntegerField(default=0)
    
    # Files
    input_file = models.FileField(upload_to='bulk_operations/input/', null=True, blank=True)
    output_file = models.FileField(upload_to='bulk_operations/output/', null=True, blank=True)
    error_file = models.FileField(upload_to='bulk_operations/errors/', null=True, blank=True)
    
    # Operation parameters
    operation_parameters = models.JSONField(
        default=dict,
        help_text="Parameters for the bulk operation"
    )
    
    # Results and errors
    results = models.JSONField(
        default=dict,
        help_text="Operation results and statistics"
    )
    error_details = models.JSONField(
        default=list,
        help_text="Detailed error information"
    )
    
    # Timing
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'bulk_preference_operations'
        verbose_name = 'Bulk Preference Operation'
        verbose_name_plural = 'Bulk Preference Operations'
        indexes = [
            models.Index(fields=['operation_type']),
            models.Index(fields=['status']),
            models.Index(fields=['initiated_by']),
            models.Index(fields=['started_at']),
            models.Index(fields=['completed_at']),
        ]

    def __str__(self):
        return f"{self.get_operation_type_display()} - {self.status}"

    @property
    def progress_percentage(self):
        """Calculate progress percentage."""
        if self.total_records == 0:
            return 0
        return round((self.processed_records / self.total_records) * 100, 2)

    @property
    def success_rate(self):
        """Calculate success rate."""
        if self.processed_records == 0:
            return 0
        return round((self.successful_records / self.processed_records) * 100, 2)

    def mark_as_processing(self):
        """Mark operation as processing."""
        from django.utils import timezone
        self.status = 'processing'
        self.started_at = timezone.now()
        self.save()

    def mark_as_completed(self):
        """Mark operation as completed."""
        from django.utils import timezone
        self.status = 'completed'
        self.completed_at = timezone.now()
        self.save()

    def mark_as_failed(self, error_message=None):
        """Mark operation as failed."""
        from django.utils import timezone
        self.status = 'failed'
        self.completed_at = timezone.now()
        if error_message:
            self.error_details.append({
                'timestamp': timezone.now().isoformat(),
                'error': error_message
            })
        self.save()

    def cancel(self):
        """Cancel the operation."""
        from django.utils import timezone
        self.status = 'cancelled'
        self.completed_at = timezone.now()
        self.save()


class PreferenceImportMapping(BaseModel):
    """
    Mapping configuration for preference imports.
    """
    MAPPING_TYPE_CHOICES = [
        ('lecturer', 'Lecturer Preference Mapping'),
        ('student', 'Student Preference Mapping'),
    ]

    name = models.CharField(max_length=200, help_text="Mapping configuration name")
    mapping_type = models.CharField(max_length=20, choices=MAPPING_TYPE_CHOICES)
    description = models.TextField(blank=True)
    
    # Field mappings
    field_mappings = models.JSONField(
        default=dict,
        help_text="Mapping of CSV columns to model fields"
    )
    
    # Validation rules
    validation_rules = models.JSONField(
        default=dict,
        help_text="Validation rules for imported data"
    )
    
    # Default values
    default_values = models.JSONField(
        default=dict,
        help_text="Default values for missing fields"
    )
    
    # Usage tracking
    usage_count = models.IntegerField(default=0)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='import_mappings')

    class Meta:
        db_table = 'preference_import_mappings'
        verbose_name = 'Preference Import Mapping'
        verbose_name_plural = 'Preference Import Mappings'
        indexes = [
            models.Index(fields=['mapping_type']),
            models.Index(fields=['created_by']),
            models.Index(fields=['usage_count']),
        ]

    def __str__(self):
        return f"{self.name} ({self.get_mapping_type_display()})"

    def increment_usage(self):
        """Increment usage count."""
        self.usage_count += 1
        self.save(update_fields=['usage_count'])


class TemplateUsage(BaseModel):
    """
    Track template usage for analytics.
    """
    template = models.ForeignKey(PreferenceTemplate, on_delete=models.CASCADE, related_name='usage_logs')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='template_usage')
    
    # Usage details
    applied_to_preference_type = models.CharField(max_length=20)
    applied_to_preference_id = models.UUIDField()
    
    # Customizations made
    customizations = models.JSONField(
        default=dict,
        help_text="Customizations made to the template"
    )
    
    # Success tracking
    was_successful = models.BooleanField(default=True)
    error_message = models.TextField(blank=True)

    class Meta:
        db_table = 'template_usage'
        verbose_name = 'Template Usage'
        verbose_name_plural = 'Template Usage'
        indexes = [
            models.Index(fields=['template']),
            models.Index(fields=['user']),
            models.Index(fields=['applied_to_preference_type', 'applied_to_preference_id']),
            models.Index(fields=['was_successful']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"{self.template.name} used by {self.user.get_full_name()}"
