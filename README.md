# Timetable Management System - Microservices Architecture

A comprehensive timetable management system built with microservices architecture using Django, MongoDB, Docker, and Kubernetes.

## Architecture Overview

This system consists of 7 microservices:

1. **Authentication Service** - JWT-based authentication and authorization
2. **User Management Service** - CRUD operations for users and roles
3. **Preference Collection Service** - Collect lecturer and student preferences
4. **Timetable Generation Service** - Generate timetables using Genetic Algorithm
5. **Notification Service** - Email and in-app notifications
6. **Data Analytics Service** - Analytics and reporting
7. **API Gateway** - Request routing and load balancing

## Detailed Architecture

### 🔐 Authentication Service (`auth-service/`)

- **Technology**: Django REST Framework + JWT
- **Database**: MongoDB
- **Features**:
  - User registration and login
  - JWT token management (access/refresh tokens)
  - Role-based access control
  - Email verification and password reset
  - Session management
- **Port**: 8001

### 👥 User Management Service (`user-service/`)

- **Technology**: Django REST Framework
- **Database**: MongoDB (shared with auth service)
- **Features**:
  - CRUD operations for <PERSON><PERSON>, Lecturers, Students
  - Role assignment and permission management
  - User blocking/unblocking functionality
  - Extended profile management with academic information
  - Bulk user operations (import/export CSV/Excel)
  - User statistics and analytics
  - Audit logging for all operations
  - Inter-service communication with auth service
- **Port**: 8002

### 🎯 Preference Collection Service (`preference-service/`)

- **Technology**: Django REST Framework + Django Channels
- **Database**: MongoDB (shared with other services)
- **Features**:
  - Lecturer preference collection (time slots, courses, constraints)
  - Student preference collection (course selections, time constraints)
  - Real-time updates via WebSockets
  - Advanced preference validation and conflict detection
  - Preference templates and bulk operations
  - Integration with timetable generation service
  - Comprehensive analytics and reporting
  - Audit logging and change tracking
- **Port**: 8003

### 🌐 API Gateway (`api-gateway/`)

- **Technology**: NGINX
- **Features**:
  - Request routing to appropriate microservices
  - Load balancing and rate limiting
  - CORS handling
  - SSL termination (production)
  - Health checks and monitoring
- **Port**: 8000

## Technologies

- **Backend**: Django REST Framework
- **Authentication**: JWT (Simple JWT for Django)
- **Database**: MongoDB
- **Containerization**: Docker
- **Orchestration**: Kubernetes
- **Algorithm**: Genetic Algorithm (DEAP/PyGAD)
- **Frontend**: React.js + TailwindCSS

## User Roles

- **Superadmin**: Manages admins and system configuration
- **Admin**: Manages lecturers, students, and timetable generation
- **Lecturer**: Provides preferences and views timetables
- **Student**: Provides preferences and views timetables

## Quick Start

### Prerequisites

- Python 3.9+
- Docker & Docker Compose
- Kubernetes (minikube for local development)
- MongoDB

### Local Development

1. Clone the repository

```bash
git clone <repository-url>
cd dagito
```

2. Start development environment (automated)

```bash
./deploy.sh dev
```

This will:

- Copy `.env.example` to `.env` (edit as needed)
- Start all services with Docker Compose
- Run database migrations
- Set up initial data (roles, permissions, superuser)

3. Manual setup (alternative)

```bash
# Set up environment variables
cp .env.example .env
# Edit .env with your configuration

# Start services
docker-compose up -d

# Run migrations and setup
docker-compose exec auth-service python manage.py migrate
docker-compose exec auth-service python manage.py setup_initial_data
```

### Default Superuser Account

- **Email**: <EMAIL>
- **Password**: admin123
- **Role**: Super Administrator

⚠️ **Important**: Change the default password after first login!

## Project Structure

```
dagito/
├── auth-service/           # Authentication microservice
├── user-service/           # User management microservice
├── preference-service/     # Preference collection microservice
├── timetable-service/      # Timetable generation microservice
├── notification-service/   # Notification microservice
├── analytics-service/      # Data analytics microservice
├── api-gateway/           # API Gateway
├── k8s/                   # Kubernetes manifests
├── docker-compose.yml     # Local development setup
└── README.md
```

## API Documentation

### Interactive API Documentation

- **Swagger UI**: http://localhost:8000/api/docs/
- **ReDoc**: http://localhost:8000/api/redoc/
- **OpenAPI Schema**: http://localhost:8000/api/schema/

### Authentication Service Endpoints

#### Authentication

- `POST /api/auth/register/` - User registration
- `POST /api/auth/login/` - User login
- `POST /api/auth/logout/` - User logout
- `POST /api/auth/token/refresh/` - Refresh JWT token
- `POST /api/auth/token/validate/` - Validate JWT token

#### Password Management

- `POST /api/auth/password/change/` - Change password
- `POST /api/auth/password/reset/request/` - Request password reset
- `POST /api/auth/password/reset/confirm/` - Confirm password reset

#### Email Verification

- `POST /api/auth/email/verify/` - Verify email address

#### User Profile

- `GET /api/auth/profile/` - Get current user profile

#### System

- `GET /api/auth/roles/` - List available roles
- `GET /api/health/` - Health check
- `GET /api/info/` - Service information

### User Management Service Endpoints

#### User Management (Admin Only)

- `GET /api/users/` - List users with filtering and pagination
- `POST /api/users/` - Create new user (via auth service)
- `GET /api/users/{id}/` - Get user details
- `PATCH /api/users/{id}/` - Update user information
- `DELETE /api/users/{id}/` - Soft delete user
- `POST /api/users/{id}/block/` - Block user
- `POST /api/users/{id}/unblock/` - Unblock user
- `POST /api/users/{id}/verify/` - Verify user email
- `GET /api/users/{id}/activities/` - Get user activity history

#### Bulk Operations (Admin Only)

- `POST /api/users/bulk-update/` - Bulk update users
- `POST /api/users/import-users/` - Import users from CSV/Excel
- `GET /api/users/export/` - Export users to CSV
- `GET /api/users/bulk-operations/` - List bulk operations
- `POST /api/users/bulk-operations/{id}/cancel/` - Cancel bulk operation

#### Profile Management

- `GET /api/profiles/me/` - Get current user's profile
- `GET /api/profiles/` - List user profiles (admin only)
- `GET /api/profiles/{id}/` - Get profile details
- `PATCH /api/profiles/{id}/` - Update profile
- `GET /api/profiles/{id}/history/` - Get profile change history
- `POST /api/profiles/bulk-update/` - Bulk update profiles
- `GET /api/profiles/statistics/` - Get profile statistics

#### Department Management (Admin Only)

- `GET /api/profiles/departments/` - List departments
- `POST /api/profiles/departments/` - Create department
- `GET /api/profiles/departments/{id}/` - Get department details
- `PATCH /api/profiles/departments/{id}/` - Update department
- `DELETE /api/profiles/departments/{id}/` - Delete department
- `POST /api/profiles/departments/{id}/update-statistics/` - Update department stats
- `GET /api/profiles/departments/{id}/users/` - Get users in department

#### Course Management (Admin Only)

- `GET /api/profiles/courses/` - List courses
- `POST /api/profiles/courses/` - Create course
- `GET /api/profiles/courses/{id}/` - Get course details
- `PATCH /api/profiles/courses/{id}/` - Update course
- `DELETE /api/profiles/courses/{id}/` - Delete course
- `POST /api/profiles/courses/{id}/assign-lecturer/` - Assign lecturer to course
- `POST /api/profiles/courses/{id}/remove-lecturer/` - Remove lecturer from course
- `POST /api/profiles/courses/{id}/enroll-student/` - Enroll student in course
- `POST /api/profiles/courses/{id}/unenroll-student/` - Unenroll student from course

#### System Endpoints

- `GET /api/statistics/` - Get system statistics
- `GET /api/audit/` - Get audit logs (admin only)

### Preference Collection Service Endpoints

#### Lecturer Preferences

- `GET /api/preferences/lecturer-preferences/` - List lecturer preferences
- `POST /api/preferences/lecturer-preferences/` - Create lecturer preference
- `GET /api/preferences/lecturer-preferences/{id}/` - Get lecturer preference details
- `PATCH /api/preferences/lecturer-preferences/{id}/` - Update lecturer preference
- `DELETE /api/preferences/lecturer-preferences/{id}/` - Delete lecturer preference
- `POST /api/preferences/lecturer-preferences/{id}/submit/` - Submit for approval
- `POST /api/preferences/lecturer-preferences/{id}/approve/` - Approve (admin only)
- `POST /api/preferences/lecturer-preferences/{id}/reject/` - Reject (admin only)
- `GET /api/preferences/lecturer-preferences/{id}/history/` - Get change history
- `GET /api/preferences/lecturer-preferences/my-preferences/` - Get current user's preferences
- `GET /api/preferences/lecturer-preferences/statistics/` - Get statistics (admin only)

#### Student Preferences

- `GET /api/preferences/student-preferences/` - List student preferences
- `POST /api/preferences/student-preferences/` - Create student preference
- `GET /api/preferences/student-preferences/{id}/` - Get student preference details
- `PATCH /api/preferences/student-preferences/{id}/` - Update student preference
- `DELETE /api/preferences/student-preferences/{id}/` - Delete student preference
- `POST /api/preferences/student-preferences/{id}/submit/` - Submit for approval
- `GET /api/preferences/student-preferences/my-preferences/` - Get current user's preferences

#### Preference Submissions

- `GET /api/preferences/submissions/` - List submissions (admin only)
- `POST /api/preferences/submissions/` - Create submission (admin only)
- `GET /api/preferences/submissions/{id}/` - Get submission details
- `POST /api/preferences/submissions/{id}/reprocess/` - Reprocess submission
- `GET /api/preferences/submissions/latest/` - Get latest submissions

#### Analytics & Summary

- `GET /api/preferences/summary/` - Get preference summary for current user
- `GET /api/preferences/analytics/` - Get preference analytics (admin only)

#### WebSocket Endpoints

- `ws://localhost:8003/ws/preferences/` - Real-time preference updates
- `ws://localhost:8003/ws/admin/` - Admin dashboard updates

## Deployment

### Automated Deployment

Use the deployment script for easy setup:

```bash
# Build and deploy to Kubernetes
./deploy.sh deploy

# Deploy specific service
./deploy.sh deploy --service auth-service

# Deploy to specific environment
./deploy.sh deploy --env staging
```

### Manual Kubernetes Deployment

1. Build Docker images

```bash
./deploy.sh build
```

2. Deploy to Kubernetes

```bash
# Create namespace and apply configurations
kubectl apply -f k8s/namespace/

# Deploy databases
kubectl apply -f k8s/mongodb/

# Deploy auth service
kubectl apply -f k8s/auth-service/

# Deploy API Gateway
kubectl apply -f k8s/api-gateway/
```

3. Verify deployment

```bash
./deploy.sh status
```

## Development Guidelines

1. Each microservice is independent with its own database
2. Communication between services via REST APIs
3. Use JWT tokens for authentication across services
4. Follow Django best practices and DRF conventions
5. Write tests for all endpoints
6. Use environment variables for configuration

## Available Commands

### Deployment Script Commands

```bash
# Development
./deploy.sh dev                    # Start development environment
./deploy.sh test                   # Run tests
./deploy.sh test --service auth-service

# Building and Deployment
./deploy.sh build                  # Build all Docker images
./deploy.sh build --service auth-service
./deploy.sh deploy                 # Deploy to Kubernetes
./deploy.sh deploy --env staging

# Monitoring and Debugging
./deploy.sh status                 # Show deployment status
./deploy.sh logs --service auth-service
./deploy.sh clean                  # Clean up resources
```

### Django Management Commands

```bash
# Database operations
docker-compose exec auth-service python manage.py migrate
docker-compose exec user-service python manage.py migrate
docker-compose exec auth-service python manage.py makemigrations
docker-compose exec user-service python manage.py makemigrations

# Initial setup
docker-compose exec auth-service python manage.py setup_initial_data

# User management
docker-compose exec auth-service python manage.py createsuperuser

# Testing
docker-compose exec auth-service python manage.py test
docker-compose exec user-service python manage.py test
docker-compose exec auth-service python manage.py test authentication.tests
docker-compose exec user-service python manage.py test users.tests

# Utilities
docker-compose exec auth-service python manage.py shell
docker-compose exec user-service python manage.py shell
docker-compose exec auth-service python manage.py collectstatic
docker-compose exec user-service python manage.py collectstatic
```

## Troubleshooting

### Common Issues

1. **Port conflicts**: Make sure ports 8000, 8001, 8002, 27017, 6379 are available
2. **Docker permission issues**: Add your user to the docker group
3. **MongoDB connection issues**: Check if MongoDB is running and accessible
4. **JWT token issues**: Verify JWT_SECRET_KEY is set correctly

### Logs and Debugging

```bash
# View service logs
./deploy.sh logs --service auth-service
docker-compose logs -f auth-service

# Check service health
curl http://localhost:8000/api/health/
curl http://localhost:8001/api/health/
curl http://localhost:8002/api/health/
curl http://localhost:8003/api/health/

# Database access
docker-compose exec mongodb mongo -u admin -p password123
```

## Security Considerations

1. **Change default passwords** in production
2. **Use strong JWT secrets** and rotate them regularly
3. **Enable HTTPS** in production environments
4. **Configure proper CORS** settings
5. **Use environment variables** for sensitive data
6. **Regular security updates** for dependencies

## Performance Optimization

1. **Database indexing**: Indexes are created automatically
2. **Caching**: Redis is configured for session and cache storage
3. **Load balancing**: API Gateway distributes requests
4. **Horizontal scaling**: Increase replicas in Kubernetes
5. **Monitoring**: Use Prometheus and Grafana (to be implemented)

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Workflow

1. **Setup**: Use `./deploy.sh dev` for local development
2. **Testing**: Write tests and run `./deploy.sh test`
3. **Code Quality**: Follow PEP 8 and Django conventions
4. **Documentation**: Update API docs and README as needed
5. **Review**: Submit PR with clear description and tests

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

For support and questions:

- Create an issue in the repository
- Check the troubleshooting section
- Review the API documentation
