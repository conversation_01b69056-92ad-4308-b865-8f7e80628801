"""
URLs configuration for timetables app.
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

app_name = 'timetables'

# Create router for viewsets
router = DefaultRouter()
router.register(r'', views.TimetableViewSet, basename='timetable')
router.register(r'entries', views.TimetableEntryViewSet, basename='timetable-entry')
router.register(r'tasks', views.GenerationTaskViewSet, basename='generation-task')
router.register(r'constraints', views.TimetableConstraintViewSet, basename='timetable-constraint')

urlpatterns = [
    # ViewSet URLs
    path('', include(router.urls)),
]
