"""
WebSocket consumers for real-time timetable updates.
"""
import json
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.contrib.auth.models import AnonymousUser
from .models import Timetable, GenerationTask


class TimetableGenerationConsumer(AsyncWebsocketConsumer):
    """
    WebSocket consumer for real-time timetable generation progress.
    """
    
    async def connect(self):
        self.timetable_id = self.scope['url_route']['kwargs']['timetable_id']
        self.room_group_name = f'timetable_generation_{self.timetable_id}'
        
        # Check if user is authenticated
        if self.scope["user"] == AnonymousUser():
            await self.close()
            return
        
        # Check if timetable exists and user has access
        timetable = await self.get_timetable(self.timetable_id)
        if not timetable:
            await self.close()
            return
        
        # Join room group
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )
        
        await self.accept()
        
        # Send current generation status
        await self.send_generation_status()
    
    async def disconnect(self, close_code):
        # Leave room group
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )
    
    async def receive(self, text_data):
        """Handle incoming WebSocket messages."""
        try:
            text_data_json = json.loads(text_data)
            message_type = text_data_json.get('type')
            
            if message_type == 'get_status':
                await self.send_generation_status()
            elif message_type == 'cancel_generation':
                await self.cancel_generation()
                
        except json.JSONDecodeError:
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': 'Invalid JSON format'
            }))
    
    async def send_generation_status(self):
        """Send current generation status."""
        task = await self.get_latest_generation_task(self.timetable_id)
        
        if task:
            await self.send(text_data=json.dumps({
                'type': 'generation_status',
                'task_id': str(task.id),
                'status': task.status,
                'progress_percentage': task.progress_percentage,
                'current_generation': task.current_generation,
                'total_generations': task.total_generations,
                'best_fitness': task.best_fitness,
                'error_message': task.error_message,
                'started_at': task.started_at.isoformat() if task.started_at else None,
                'completed_at': task.completed_at.isoformat() if task.completed_at else None
            }))
        else:
            await self.send(text_data=json.dumps({
                'type': 'generation_status',
                'status': 'no_task'
            }))
    
    async def cancel_generation(self):
        """Cancel the current generation task."""
        task = await self.get_latest_generation_task(self.timetable_id)
        
        if task and task.status in ['pending', 'running']:
            # Cancel Celery task
            if task.task_id:
                from celery import current_app
                current_app.control.revoke(task.task_id, terminate=True)
            
            # Update task status
            await self.update_task_status(task.id, 'cancelled')
            
            await self.send(text_data=json.dumps({
                'type': 'generation_cancelled',
                'task_id': str(task.id)
            }))
        else:
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': 'No active generation task to cancel'
            }))
    
    # Group message handlers
    async def generation_progress(self, event):
        """Handle generation progress updates."""
        await self.send(text_data=json.dumps({
            'type': 'generation_progress',
            'task_id': event['task_id'],
            'progress_percentage': event['progress_percentage'],
            'current_generation': event['current_generation'],
            'total_generations': event['total_generations'],
            'best_fitness': event['best_fitness'],
            'message': event.get('message', '')
        }))
    
    async def generation_completed(self, event):
        """Handle generation completion."""
        await self.send(text_data=json.dumps({
            'type': 'generation_completed',
            'task_id': event['task_id'],
            'result_data': event['result_data']
        }))
    
    async def generation_failed(self, event):
        """Handle generation failure."""
        await self.send(text_data=json.dumps({
            'type': 'generation_failed',
            'task_id': event['task_id'],
            'error_message': event['error_message']
        }))
    
    # Database operations
    @database_sync_to_async
    def get_timetable(self, timetable_id):
        try:
            return Timetable.objects.get(id=timetable_id)
        except Timetable.DoesNotExist:
            return None
    
    @database_sync_to_async
    def get_latest_generation_task(self, timetable_id):
        try:
            return GenerationTask.objects.filter(
                timetable_id=timetable_id
            ).order_by('-created_at').first()
        except GenerationTask.DoesNotExist:
            return None
    
    @database_sync_to_async
    def update_task_status(self, task_id, status):
        try:
            task = GenerationTask.objects.get(id=task_id)
            task.status = status
            task.save()
            return task
        except GenerationTask.DoesNotExist:
            return None


class TimetableConsumer(AsyncWebsocketConsumer):
    """
    WebSocket consumer for real-time timetable updates.
    """
    
    async def connect(self):
        self.timetable_id = self.scope['url_route']['kwargs']['timetable_id']
        self.room_group_name = f'timetable_{self.timetable_id}'
        
        # Check if user is authenticated
        if self.scope["user"] == AnonymousUser():
            await self.close()
            return
        
        # Check if timetable exists
        timetable = await self.get_timetable(self.timetable_id)
        if not timetable:
            await self.close()
            return
        
        # Join room group
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )
        
        await self.accept()
    
    async def disconnect(self, close_code):
        # Leave room group
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )
    
    async def receive(self, text_data):
        """Handle incoming WebSocket messages."""
        try:
            text_data_json = json.loads(text_data)
            message_type = text_data_json.get('type')
            
            if message_type == 'ping':
                await self.send(text_data=json.dumps({
                    'type': 'pong'
                }))
                
        except json.JSONDecodeError:
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': 'Invalid JSON format'
            }))
    
    # Group message handlers
    async def timetable_updated(self, event):
        """Handle timetable updates."""
        await self.send(text_data=json.dumps({
            'type': 'timetable_updated',
            'timetable_id': event['timetable_id'],
            'changes': event['changes']
        }))
    
    async def entry_created(self, event):
        """Handle new timetable entry creation."""
        await self.send(text_data=json.dumps({
            'type': 'entry_created',
            'entry_id': event['entry_id'],
            'entry_data': event['entry_data']
        }))
    
    async def entry_updated(self, event):
        """Handle timetable entry updates."""
        await self.send(text_data=json.dumps({
            'type': 'entry_updated',
            'entry_id': event['entry_id'],
            'changes': event['changes']
        }))
    
    async def entry_deleted(self, event):
        """Handle timetable entry deletion."""
        await self.send(text_data=json.dumps({
            'type': 'entry_deleted',
            'entry_id': event['entry_id']
        }))
    
    # Database operations
    @database_sync_to_async
    def get_timetable(self, timetable_id):
        try:
            return Timetable.objects.get(id=timetable_id)
        except Timetable.DoesNotExist:
            return None
