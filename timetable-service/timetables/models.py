"""
Timetable models for the timetable service.
"""
from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from core.models import BaseModel, User, Course, Room, TimeSlot, Department, AcademicYear
import json


class Timetable(BaseModel):
    """
    Main timetable model representing a complete timetable for an academic period.
    """
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('generating', 'Generating'),
        ('generated', 'Generated'),
        ('published', 'Published'),
        ('archived', 'Archived'),
    ]
    
    name = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    academic_year = models.ForeignKey(AcademicYear, on_delete=models.CASCADE)
    semester = models.CharField(
        max_length=20,
        choices=[
            ('fall', 'Fall'),
            ('spring', 'Spring'),
            ('summer', 'Summer'),
        ]
    )
    department = models.ForeignKey(Department, on_delete=models.CASCADE, null=True, blank=True)
    
    # Timetable status and metadata
    status = models.Char<PERSON>ield(max_length=20, choices=STATUS_CHOICES, default='draft')
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_timetables')
    published_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='published_timetables')
    published_at = models.DateTimeField(null=True, blank=True)
    
    # Generation metadata
    generation_algorithm = models.CharField(max_length=50, default='genetic_algorithm')
    generation_parameters = models.JSONField(default=dict, blank=True)
    fitness_score = models.FloatField(null=True, blank=True, help_text="Overall fitness score from genetic algorithm")
    conflict_count = models.PositiveIntegerField(default=0, help_text="Number of conflicts in the timetable")
    
    # Constraints and preferences
    hard_constraints_satisfied = models.BooleanField(default=False)
    soft_constraints_score = models.FloatField(null=True, blank=True)
    
    class Meta:
        db_table = 'timetables_timetable'
        ordering = ['-created_at']
        unique_together = ['academic_year', 'semester', 'department', 'name']

    def __str__(self):
        dept_str = f" - {self.department.name}" if self.department else ""
        return f"{self.name} ({self.academic_year.name} {self.semester.title()}){dept_str}"

    def publish(self, user):
        """Publish the timetable."""
        self.status = 'published'
        self.published_by = user
        self.published_at = timezone.now()
        self.save()

    def archive(self):
        """Archive the timetable."""
        self.status = 'archived'
        self.save()

    @property
    def total_entries(self):
        """Get total number of timetable entries."""
        return self.entries.count()

    @property
    def total_courses(self):
        """Get total number of unique courses."""
        return self.entries.values('course').distinct().count()


class TimetableEntry(BaseModel):
    """
    Individual entry in a timetable representing a scheduled class.
    """
    timetable = models.ForeignKey(Timetable, on_delete=models.CASCADE, related_name='entries')
    course = models.ForeignKey(Course, on_delete=models.CASCADE)
    lecturer = models.ForeignKey(User, on_delete=models.CASCADE, related_name='teaching_entries')
    room = models.ForeignKey(Room, on_delete=models.CASCADE)
    time_slot = models.ForeignKey(TimeSlot, on_delete=models.CASCADE)
    
    # Additional scheduling information
    week_pattern = models.CharField(
        max_length=20,
        choices=[
            ('all', 'All Weeks'),
            ('odd', 'Odd Weeks'),
            ('even', 'Even Weeks'),
            ('custom', 'Custom Pattern'),
        ],
        default='all'
    )
    custom_weeks = models.JSONField(default=list, blank=True, help_text="List of week numbers for custom pattern")
    
    # Entry metadata
    notes = models.TextField(blank=True)
    is_locked = models.BooleanField(default=False, help_text="Prevent automatic changes to this entry")
    priority = models.IntegerField(default=0, help_text="Priority for conflict resolution")
    
    # Students enrolled (for tracking capacity)
    enrolled_students = models.ManyToManyField(User, blank=True, related_name='enrolled_entries')
    max_students = models.PositiveIntegerField(null=True, blank=True)

    class Meta:
        db_table = 'timetables_entry'
        ordering = ['time_slot__day_of_week', 'time_slot__start_time']
        unique_together = ['timetable', 'course', 'time_slot', 'room']

    def __str__(self):
        return f"{self.course.code} - {self.time_slot} - {self.room.code}"

    @property
    def enrolled_count(self):
        """Get number of enrolled students."""
        return self.enrolled_students.count()

    @property
    def capacity_utilization(self):
        """Get capacity utilization percentage."""
        max_capacity = self.max_students or self.room.capacity
        if max_capacity > 0:
            return (self.enrolled_count / max_capacity) * 100
        return 0


class GenerationTask(BaseModel):
    """
    Model to track timetable generation tasks.
    """
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('running', 'Running'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ]
    
    timetable = models.ForeignKey(Timetable, on_delete=models.CASCADE, related_name='generation_tasks')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    
    # Task metadata
    task_id = models.CharField(max_length=255, unique=True, null=True, blank=True)
    started_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    
    # Generation parameters
    algorithm_name = models.CharField(max_length=50, default='genetic_algorithm')
    parameters = models.JSONField(default=dict)
    
    # Progress tracking
    progress_percentage = models.FloatField(default=0.0, validators=[MinValueValidator(0), MaxValueValidator(100)])
    current_generation = models.PositiveIntegerField(default=0)
    total_generations = models.PositiveIntegerField(default=0)
    best_fitness = models.FloatField(null=True, blank=True)
    
    # Results and errors
    result_data = models.JSONField(default=dict, blank=True)
    error_message = models.TextField(blank=True)
    log_data = models.JSONField(default=list, blank=True)

    class Meta:
        db_table = 'timetables_generation_task'
        ordering = ['-created_at']

    def __str__(self):
        return f"Generation Task {self.id} - {self.timetable.name} ({self.status})"

    def start(self, task_id=None):
        """Mark task as started."""
        self.status = 'running'
        self.started_at = timezone.now()
        if task_id:
            self.task_id = task_id
        self.save()

    def complete(self, result_data=None):
        """Mark task as completed."""
        self.status = 'completed'
        self.completed_at = timezone.now()
        self.progress_percentage = 100.0
        if result_data:
            self.result_data = result_data
        self.save()

    def fail(self, error_message):
        """Mark task as failed."""
        self.status = 'failed'
        self.completed_at = timezone.now()
        self.error_message = error_message
        self.save()

    def cancel(self):
        """Cancel the task."""
        self.status = 'cancelled'
        self.completed_at = timezone.now()
        self.save()

    def update_progress(self, percentage, generation=None, fitness=None, log_entry=None):
        """Update task progress."""
        self.progress_percentage = min(100.0, max(0.0, percentage))
        if generation is not None:
            self.current_generation = generation
        if fitness is not None:
            self.best_fitness = fitness
        if log_entry:
            if not isinstance(self.log_data, list):
                self.log_data = []
            self.log_data.append({
                'timestamp': timezone.now().isoformat(),
                'message': log_entry
            })
        self.save()

    @property
    def duration(self):
        """Get task duration."""
        if self.started_at:
            end_time = self.completed_at or timezone.now()
            return end_time - self.started_at
        return None


class TimetableConstraint(BaseModel):
    """
    Model for storing timetable constraints.
    """
    CONSTRAINT_TYPE_CHOICES = [
        ('hard', 'Hard Constraint'),
        ('soft', 'Soft Constraint'),
    ]
    
    CONSTRAINT_CATEGORY_CHOICES = [
        ('room_capacity', 'Room Capacity'),
        ('lecturer_availability', 'Lecturer Availability'),
        ('room_availability', 'Room Availability'),
        ('course_scheduling', 'Course Scheduling'),
        ('student_conflicts', 'Student Conflicts'),
        ('resource_conflicts', 'Resource Conflicts'),
        ('time_preferences', 'Time Preferences'),
        ('workload_distribution', 'Workload Distribution'),
    ]
    
    name = models.CharField(max_length=200)
    description = models.TextField()
    constraint_type = models.CharField(max_length=10, choices=CONSTRAINT_TYPE_CHOICES)
    category = models.CharField(max_length=30, choices=CONSTRAINT_CATEGORY_CHOICES)
    
    # Constraint configuration
    is_enabled = models.BooleanField(default=True)
    weight = models.FloatField(default=1.0, validators=[MinValueValidator(0.0), MaxValueValidator(10.0)])
    parameters = models.JSONField(default=dict, blank=True)
    
    # Scope
    department = models.ForeignKey(Department, on_delete=models.CASCADE, null=True, blank=True)
    academic_year = models.ForeignKey(AcademicYear, on_delete=models.CASCADE, null=True, blank=True)

    class Meta:
        db_table = 'timetables_constraint'
        ordering = ['constraint_type', 'category', 'name']

    def __str__(self):
        return f"{self.name} ({self.constraint_type})"
