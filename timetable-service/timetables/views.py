"""
Views for timetable management.
"""
from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import <PERSON>Filter, OrderingFilter
from django.shortcuts import get_object_or_404
from django.utils import timezone
from .models import Timetable, TimetableEntry, GenerationTask, TimetableConstraint
from .serializers import (
    TimetableSerializer, TimetableEntrySerializer, TimetableEntryCreateSerializer,
    GenerationTaskSerializer, TimetableConstraintSerializer,
    TimetableGenerationRequestSerializer, TimetableStatsSerializer,
    TimetableExportRequestSerializer
)
from .tasks import generate_timetable_task, validate_timetable_constraints, optimize_timetable
from core.models import User
import logging

logger = logging.getLogger(__name__)


class TimetableViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing timetables.
    """
    queryset = Timetable.objects.all()
    serializer_class = TimetableSerializer
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['status', 'academic_year', 'semester', 'department']
    search_fields = ['name', 'description']
    ordering_fields = ['created_at', 'updated_at', 'name', 'fitness_score']
    ordering = ['-created_at']
    
    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)
    
    @action(detail=True, methods=['post'])
    def generate(self, request, pk=None):
        """Generate timetable using genetic algorithm."""
        timetable = self.get_object()
        
        if timetable.status in ['generating']:
            return Response(
                {'error': 'Timetable generation is already in progress'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Validate generation parameters
        serializer = TimetableGenerationRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        parameters = serializer.validated_data
        
        # Create generation task
        generation_task = GenerationTask.objects.create(
            timetable=timetable,
            algorithm_name=parameters.get('algorithm', 'genetic_algorithm'),
            parameters=parameters,
            started_by=request.user
        )
        
        # Start async generation
        task = generate_timetable_task.delay(str(timetable.id), parameters)
        generation_task.task_id = task.id
        generation_task.save()
        
        return Response({
            'message': 'Timetable generation started',
            'task_id': task.id,
            'generation_task_id': str(generation_task.id)
        }, status=status.HTTP_202_ACCEPTED)
    
    @action(detail=True, methods=['post'])
    def publish(self, request, pk=None):
        """Publish the timetable."""
        timetable = self.get_object()
        
        if timetable.status != 'generated':
            return Response(
                {'error': 'Only generated timetables can be published'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        timetable.publish(request.user)
        
        return Response({
            'message': 'Timetable published successfully',
            'published_at': timetable.published_at
        })
    
    @action(detail=True, methods=['post'])
    def archive(self, request, pk=None):
        """Archive the timetable."""
        timetable = self.get_object()
        timetable.archive()
        
        return Response({'message': 'Timetable archived successfully'})
    
    @action(detail=True, methods=['get'])
    def stats(self, request, pk=None):
        """Get timetable statistics."""
        timetable = self.get_object()
        entries = TimetableEntry.objects.filter(timetable=timetable)
        
        # Calculate statistics
        stats_data = {
            'total_entries': entries.count(),
            'total_courses': entries.values('course').distinct().count(),
            'total_lecturers': entries.values('lecturer').distinct().count(),
            'total_rooms': entries.values('room').distinct().count(),
            'total_time_slots': entries.values('time_slot').distinct().count(),
            'conflict_count': timetable.conflict_count,
            'fitness_score': timetable.fitness_score,
            'room_utilization': self._calculate_room_utilization(entries),
            'lecturer_workload': self._calculate_lecturer_workload(entries),
            'time_slot_usage': self._calculate_time_slot_usage(entries),
            'conflict_types': self._analyze_conflicts(entries),
            'preference_satisfaction': timetable.soft_constraints_score
        }
        
        serializer = TimetableStatsSerializer(stats_data)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def validate_constraints(self, request, pk=None):
        """Validate timetable constraints."""
        timetable = self.get_object()
        
        # Start async validation
        task = validate_timetable_constraints.delay(str(timetable.id))
        
        return Response({
            'message': 'Constraint validation started',
            'task_id': task.id
        }, status=status.HTTP_202_ACCEPTED)
    
    @action(detail=True, methods=['post'])
    def optimize(self, request, pk=None):
        """Optimize existing timetable."""
        timetable = self.get_object()
        
        if timetable.status not in ['generated', 'published']:
            return Response(
                {'error': 'Only generated or published timetables can be optimized'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Validate optimization parameters
        serializer = TimetableGenerationRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        parameters = serializer.validated_data
        
        # Start async optimization
        task = optimize_timetable.delay(str(timetable.id), parameters)
        
        return Response({
            'message': 'Timetable optimization started',
            'task_id': task.id
        }, status=status.HTTP_202_ACCEPTED)
    
    @action(detail=True, methods=['post'])
    def export(self, request, pk=None):
        """Export timetable in various formats."""
        timetable = self.get_object()
        
        serializer = TimetableExportRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        export_format = serializer.validated_data['format']
        
        # Import export functions
        from exports.tasks import export_timetable_task
        
        # Start async export
        task = export_timetable_task.delay(str(timetable.id), serializer.validated_data)
        
        return Response({
            'message': f'Timetable export to {export_format} started',
            'task_id': task.id
        }, status=status.HTTP_202_ACCEPTED)
    
    def _calculate_room_utilization(self, entries):
        """Calculate room utilization statistics."""
        room_usage = {}
        for entry in entries:
            room_id = str(entry.room.id)
            if room_id not in room_usage:
                room_usage[room_id] = {
                    'room_name': entry.room.name,
                    'usage_count': 0,
                    'capacity': entry.room.capacity
                }
            room_usage[room_id]['usage_count'] += 1
        
        return room_usage
    
    def _calculate_lecturer_workload(self, entries):
        """Calculate lecturer workload statistics."""
        lecturer_workload = {}
        for entry in entries:
            lecturer_id = str(entry.lecturer.id)
            if lecturer_id not in lecturer_workload:
                lecturer_workload[lecturer_id] = {
                    'lecturer_name': entry.lecturer.full_name,
                    'total_hours': 0,
                    'course_count': 0
                }
            lecturer_workload[lecturer_id]['total_hours'] += entry.course.duration_hours
            lecturer_workload[lecturer_id]['course_count'] += 1
        
        return lecturer_workload
    
    def _calculate_time_slot_usage(self, entries):
        """Calculate time slot usage statistics."""
        time_slot_usage = {}
        for entry in entries:
            slot_id = str(entry.time_slot.id)
            if slot_id not in time_slot_usage:
                time_slot_usage[slot_id] = {
                    'time_slot': str(entry.time_slot),
                    'usage_count': 0
                }
            time_slot_usage[slot_id]['usage_count'] += 1
        
        return time_slot_usage
    
    def _analyze_conflicts(self, entries):
        """Analyze different types of conflicts."""
        conflicts = {
            'room_conflicts': 0,
            'lecturer_conflicts': 0,
            'capacity_conflicts': 0
        }
        
        # Check room conflicts
        room_time_slots = {}
        for entry in entries:
            key = (entry.room.id, entry.time_slot.id)
            if key in room_time_slots:
                conflicts['room_conflicts'] += 1
            else:
                room_time_slots[key] = entry
        
        # Check lecturer conflicts
        lecturer_time_slots = {}
        for entry in entries:
            key = (entry.lecturer.id, entry.time_slot.id)
            if key in lecturer_time_slots:
                conflicts['lecturer_conflicts'] += 1
            else:
                lecturer_time_slots[key] = entry
        
        # Check capacity conflicts
        for entry in entries:
            if entry.course.max_students > entry.room.capacity:
                conflicts['capacity_conflicts'] += 1
        
        return conflicts


class TimetableEntryViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing timetable entries.
    """
    queryset = TimetableEntry.objects.all()
    serializer_class = TimetableEntrySerializer
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['timetable', 'course', 'lecturer', 'room', 'time_slot', 'is_locked']
    search_fields = ['course__name', 'course__code', 'lecturer__first_name', 'lecturer__last_name']
    ordering_fields = ['time_slot__day_of_week', 'time_slot__start_time', 'created_at']
    ordering = ['time_slot__day_of_week', 'time_slot__start_time']
    
    def get_serializer_class(self):
        if self.action == 'create':
            return TimetableEntryCreateSerializer
        return TimetableEntrySerializer
    
    @action(detail=True, methods=['post'])
    def lock(self, request, pk=None):
        """Lock a timetable entry to prevent automatic changes."""
        entry = self.get_object()
        entry.is_locked = True
        entry.save()
        
        return Response({'message': 'Timetable entry locked successfully'})
    
    @action(detail=True, methods=['post'])
    def unlock(self, request, pk=None):
        """Unlock a timetable entry."""
        entry = self.get_object()
        entry.is_locked = False
        entry.save()
        
        return Response({'message': 'Timetable entry unlocked successfully'})


class GenerationTaskViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for viewing generation tasks.
    """
    queryset = GenerationTask.objects.all()
    serializer_class = GenerationTaskSerializer
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ['status', 'timetable', 'algorithm_name']
    ordering_fields = ['created_at', 'started_at', 'completed_at']
    ordering = ['-created_at']
    
    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        """Cancel a running generation task."""
        task = self.get_object()
        
        if task.status not in ['pending', 'running']:
            return Response(
                {'error': 'Only pending or running tasks can be cancelled'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Cancel Celery task if it exists
        if task.task_id:
            from celery import current_app
            current_app.control.revoke(task.task_id, terminate=True)
        
        task.cancel()
        
        return Response({'message': 'Generation task cancelled successfully'})


class TimetableConstraintViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing timetable constraints.
    """
    queryset = TimetableConstraint.objects.all()
    serializer_class = TimetableConstraintSerializer
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['constraint_type', 'category', 'is_enabled', 'department']
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'weight', 'created_at']
    ordering = ['constraint_type', 'category', 'name']
