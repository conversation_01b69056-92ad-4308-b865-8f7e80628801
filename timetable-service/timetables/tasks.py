"""
Celery tasks for timetable generation.
"""
from celery import shared_task
from django.utils import timezone
from django.conf import settings
from .models import Timetable, TimetableEntry, GenerationTask
from algorithms.genetic_algorithm import TimetableGeneticAlgorithm
from core.models import Course, Room, TimeSlot, User
import logging
import traceback

logger = logging.getLogger(__name__)


@shared_task(bind=True)
def generate_timetable_task(self, timetable_id, parameters=None):
    """
    Celery task to generate a timetable using genetic algorithm.
    """
    try:
        # Get the timetable and generation task
        timetable = Timetable.objects.get(id=timetable_id)
        generation_task = GenerationTask.objects.filter(
            timetable=timetable,
            status='pending'
        ).first()
        
        if not generation_task:
            logger.error(f"No pending generation task found for timetable {timetable_id}")
            return {'error': 'No pending generation task found'}
        
        # Start the task
        generation_task.start(task_id=self.request.id)
        timetable.status = 'generating'
        timetable.save()
        
        logger.info(f"Starting timetable generation for {timetable.name}")
        
        # Initialize genetic algorithm
        ga = TimetableGeneticAlgorithm(timetable, parameters)
        
        # Progress callback function
        def progress_callback(generation, fitness, message=None):
            progress = (generation / ga.generations) * 100
            generation_task.update_progress(
                percentage=progress,
                generation=generation,
                fitness=fitness,
                log_entry=message
            )
            
            # Update Celery task state
            self.update_state(
                state='PROGRESS',
                meta={
                    'current': generation,
                    'total': ga.generations,
                    'fitness': fitness,
                    'message': message or f'Generation {generation}/{ga.generations}'
                }
            )
        
        # Run the genetic algorithm
        result = ga.run(progress_callback=progress_callback)
        
        # Process the results
        best_individual = result['best_individual']
        best_fitness = result['best_fitness']
        
        # Clear existing entries
        TimetableEntry.objects.filter(timetable=timetable).delete()
        
        # Create new timetable entries from the best solution
        entries_created = 0
        for gene in best_individual:
            try:
                course = Course.objects.get(id=gene['course_id'])
                room = Room.objects.get(id=gene['room_id'])
                time_slot = TimeSlot.objects.get(id=gene['time_slot_id'])
                lecturer = User.objects.get(id=gene['lecturer_id'])
                
                entry = TimetableEntry.objects.create(
                    timetable=timetable,
                    course=course,
                    lecturer=lecturer,
                    room=room,
                    time_slot=time_slot,
                    max_students=course.max_students
                )
                entries_created += 1
                
            except Exception as e:
                logger.error(f"Error creating timetable entry: {e}")
                continue
        
        # Update timetable with results
        timetable.status = 'generated'
        timetable.fitness_score = best_fitness
        timetable.generation_algorithm = 'genetic_algorithm'
        timetable.generation_parameters = parameters or {}
        
        # Calculate conflicts
        conflict_count = calculate_timetable_conflicts(timetable)
        timetable.conflict_count = conflict_count
        timetable.hard_constraints_satisfied = conflict_count == 0
        
        timetable.save()
        
        # Complete the generation task
        result_data = {
            'entries_created': entries_created,
            'fitness_score': best_fitness,
            'conflict_count': conflict_count,
            'algorithm': 'genetic_algorithm',
            'parameters': parameters or {}
        }
        generation_task.complete(result_data)
        
        logger.info(f"Timetable generation completed for {timetable.name}. "
                   f"Created {entries_created} entries with fitness {best_fitness}")
        
        # Send notification (integrate with notification service)
        send_generation_notification.delay(timetable_id, 'completed', result_data)
        
        return result_data
        
    except Exception as e:
        error_message = f"Timetable generation failed: {str(e)}"
        logger.error(f"{error_message}\n{traceback.format_exc()}")
        
        # Update task and timetable status
        if 'generation_task' in locals():
            generation_task.fail(error_message)
        
        if 'timetable' in locals():
            timetable.status = 'draft'
            timetable.save()
        
        # Send failure notification
        send_generation_notification.delay(timetable_id, 'failed', {'error': error_message})
        
        raise


@shared_task
def send_generation_notification(timetable_id, status, data):
    """
    Send notification about timetable generation completion.
    """
    try:
        import requests
        
        timetable = Timetable.objects.get(id=timetable_id)
        
        notification_data = {
            'recipient_id': str(timetable.created_by.id) if timetable.created_by else None,
            'type': 'timetable_generation',
            'title': f'Timetable Generation {status.title()}',
            'message': f'Timetable "{timetable.name}" generation has {status}.',
            'data': {
                'timetable_id': str(timetable_id),
                'timetable_name': timetable.name,
                'status': status,
                **data
            }
        }
        
        # Send to notification service
        notification_url = f"{settings.NOTIFICATION_SERVICE_URL}/api/notifications/"
        headers = {
            'Authorization': f'Bearer {settings.INTERNAL_SERVICE_TOKEN}',
            'Content-Type': 'application/json'
        }
        
        response = requests.post(notification_url, json=notification_data, headers=headers)
        
        if response.status_code == 201:
            logger.info(f"Notification sent for timetable {timetable_id}")
        else:
            logger.error(f"Failed to send notification: {response.status_code} - {response.text}")
            
    except Exception as e:
        logger.error(f"Error sending notification: {e}")


@shared_task
def cleanup_old_generation_tasks():
    """
    Clean up old generation tasks.
    """
    from datetime import timedelta
    
    cutoff_date = timezone.now() - timedelta(days=30)
    
    # Delete old completed/failed tasks
    deleted_count = GenerationTask.objects.filter(
        status__in=['completed', 'failed', 'cancelled'],
        created_at__lt=cutoff_date
    ).delete()[0]
    
    logger.info(f"Cleaned up {deleted_count} old generation tasks")
    return deleted_count


@shared_task
def validate_timetable_constraints(timetable_id):
    """
    Validate timetable against all constraints.
    """
    try:
        timetable = Timetable.objects.get(id=timetable_id)
        entries = TimetableEntry.objects.filter(timetable=timetable)
        
        # Calculate conflicts
        conflict_count = calculate_timetable_conflicts(timetable)
        
        # Update timetable
        timetable.conflict_count = conflict_count
        timetable.hard_constraints_satisfied = conflict_count == 0
        timetable.save()
        
        return {
            'timetable_id': str(timetable_id),
            'conflict_count': conflict_count,
            'hard_constraints_satisfied': conflict_count == 0,
            'total_entries': entries.count()
        }
        
    except Exception as e:
        logger.error(f"Error validating timetable constraints: {e}")
        raise


def calculate_timetable_conflicts(timetable):
    """
    Calculate the number of conflicts in a timetable.
    """
    entries = TimetableEntry.objects.filter(timetable=timetable)
    conflicts = 0
    
    # Room conflicts
    room_time_slots = {}
    for entry in entries:
        key = (entry.room.id, entry.time_slot.id)
        if key in room_time_slots:
            conflicts += 1
        else:
            room_time_slots[key] = entry
    
    # Lecturer conflicts
    lecturer_time_slots = {}
    for entry in entries:
        key = (entry.lecturer.id, entry.time_slot.id)
        if key in lecturer_time_slots:
            conflicts += 1
        else:
            lecturer_time_slots[key] = entry
    
    # Capacity conflicts
    for entry in entries:
        if entry.course.max_students > entry.room.capacity:
            conflicts += 1
    
    return conflicts


@shared_task
def optimize_timetable(timetable_id, optimization_parameters=None):
    """
    Optimize an existing timetable.
    """
    try:
        timetable = Timetable.objects.get(id=timetable_id)
        
        # Create a new generation task for optimization
        generation_task = GenerationTask.objects.create(
            timetable=timetable,
            algorithm_name='genetic_algorithm_optimization',
            parameters=optimization_parameters or {},
            started_by=None  # System optimization
        )
        
        # Run optimization with fewer generations
        optimization_params = optimization_parameters or {}
        optimization_params.update({
            'generations': optimization_params.get('generations', 20),  # Fewer generations for optimization
            'population_size': optimization_params.get('population_size', 50)
        })
        
        # Use the existing timetable as a starting point
        # This would require modifying the genetic algorithm to accept initial solutions
        
        return generate_timetable_task.delay(timetable_id, optimization_params)
        
    except Exception as e:
        logger.error(f"Error optimizing timetable: {e}")
        raise
