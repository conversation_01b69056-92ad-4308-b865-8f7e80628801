"""
Serializers for timetable models.
"""
from rest_framework import serializers
from .models import Timetable, TimetableEntry, GenerationTask, TimetableConstraint
from core.serializers import UserSerializer, CourseSerializer, RoomSerializer, TimeSlotSerializer


class TimetableSerializer(serializers.ModelSerializer):
    created_by_name = serializers.CharField(source='created_by.full_name', read_only=True)
    published_by_name = serializers.CharField(source='published_by.full_name', read_only=True)
    academic_year_name = serializers.CharField(source='academic_year.name', read_only=True)
    department_name = serializers.CharField(source='department.name', read_only=True)
    total_entries = serializers.ReadOnlyField()
    total_courses = serializers.ReadOnlyField()
    
    class Meta:
        model = Timetable
        fields = [
            'id', 'name', 'description', 'academic_year', 'academic_year_name',
            'semester', 'department', 'department_name', 'status',
            'created_by', 'created_by_name', 'published_by', 'published_by_name',
            'published_at', 'generation_algorithm', 'generation_parameters',
            'fitness_score', 'conflict_count', 'hard_constraints_satisfied',
            'soft_constraints_score', 'total_entries', 'total_courses',
            'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'created_by', 'published_by', 'published_at',
            'fitness_score', 'conflict_count', 'hard_constraints_satisfied',
            'soft_constraints_score', 'created_at', 'updated_at'
        ]


class TimetableEntrySerializer(serializers.ModelSerializer):
    course_details = CourseSerializer(source='course', read_only=True)
    lecturer_details = UserSerializer(source='lecturer', read_only=True)
    room_details = RoomSerializer(source='room', read_only=True)
    time_slot_details = TimeSlotSerializer(source='time_slot', read_only=True)
    enrolled_count = serializers.ReadOnlyField()
    capacity_utilization = serializers.ReadOnlyField()
    
    class Meta:
        model = TimetableEntry
        fields = [
            'id', 'timetable', 'course', 'course_details', 'lecturer', 'lecturer_details',
            'room', 'room_details', 'time_slot', 'time_slot_details',
            'week_pattern', 'custom_weeks', 'notes', 'is_locked', 'priority',
            'enrolled_students', 'max_students', 'enrolled_count', 'capacity_utilization',
            'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class TimetableEntryCreateSerializer(serializers.ModelSerializer):
    """Simplified serializer for creating timetable entries."""
    
    class Meta:
        model = TimetableEntry
        fields = [
            'timetable', 'course', 'lecturer', 'room', 'time_slot',
            'week_pattern', 'custom_weeks', 'notes', 'is_locked',
            'priority', 'max_students'
        ]


class GenerationTaskSerializer(serializers.ModelSerializer):
    timetable_name = serializers.CharField(source='timetable.name', read_only=True)
    started_by_name = serializers.CharField(source='started_by.full_name', read_only=True)
    duration = serializers.ReadOnlyField()
    
    class Meta:
        model = GenerationTask
        fields = [
            'id', 'timetable', 'timetable_name', 'status', 'task_id',
            'started_by', 'started_by_name', 'started_at', 'completed_at',
            'algorithm_name', 'parameters', 'progress_percentage',
            'current_generation', 'total_generations', 'best_fitness',
            'result_data', 'error_message', 'log_data', 'duration',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'task_id', 'started_at', 'completed_at', 'progress_percentage',
            'current_generation', 'best_fitness', 'result_data', 'error_message',
            'log_data', 'created_at', 'updated_at'
        ]


class TimetableConstraintSerializer(serializers.ModelSerializer):
    department_name = serializers.CharField(source='department.name', read_only=True)
    academic_year_name = serializers.CharField(source='academic_year.name', read_only=True)
    
    class Meta:
        model = TimetableConstraint
        fields = [
            'id', 'name', 'description', 'constraint_type', 'category',
            'is_enabled', 'weight', 'parameters', 'department', 'department_name',
            'academic_year', 'academic_year_name', 'is_active',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class TimetableGenerationRequestSerializer(serializers.Serializer):
    """Serializer for timetable generation requests."""
    
    algorithm = serializers.ChoiceField(
        choices=['genetic_algorithm'],
        default='genetic_algorithm'
    )
    population_size = serializers.IntegerField(min_value=10, max_value=500, required=False)
    generations = serializers.IntegerField(min_value=5, max_value=200, required=False)
    mutation_rate = serializers.FloatField(min_value=0.01, max_value=0.5, required=False)
    crossover_rate = serializers.FloatField(min_value=0.1, max_value=1.0, required=False)
    
    # Constraint weights
    hard_constraint_weight = serializers.FloatField(min_value=0.0, max_value=10.0, required=False)
    soft_constraint_weight = serializers.FloatField(min_value=0.0, max_value=10.0, required=False)
    
    # Additional parameters
    use_preferences = serializers.BooleanField(default=True)
    optimize_workload = serializers.BooleanField(default=True)
    optimize_room_usage = serializers.BooleanField(default=True)


class TimetableStatsSerializer(serializers.Serializer):
    """Serializer for timetable statistics."""
    
    total_entries = serializers.IntegerField()
    total_courses = serializers.IntegerField()
    total_lecturers = serializers.IntegerField()
    total_rooms = serializers.IntegerField()
    total_time_slots = serializers.IntegerField()
    
    # Utilization stats
    room_utilization = serializers.DictField()
    lecturer_workload = serializers.DictField()
    time_slot_usage = serializers.DictField()
    
    # Conflict stats
    conflict_count = serializers.IntegerField()
    conflict_types = serializers.DictField()
    
    # Quality metrics
    fitness_score = serializers.FloatField(allow_null=True)
    preference_satisfaction = serializers.FloatField(allow_null=True)


class TimetableExportRequestSerializer(serializers.Serializer):
    """Serializer for timetable export requests."""
    
    format = serializers.ChoiceField(
        choices=['pdf', 'excel', 'ical', 'json'],
        required=True
    )
    
    # Filter options
    department = serializers.UUIDField(required=False)
    lecturer = serializers.UUIDField(required=False)
    room = serializers.UUIDField(required=False)
    course = serializers.UUIDField(required=False)
    
    # Layout options for PDF
    layout = serializers.ChoiceField(
        choices=['weekly', 'daily', 'lecturer', 'room'],
        default='weekly',
        required=False
    )
    
    # Include options
    include_details = serializers.BooleanField(default=True)
    include_conflicts = serializers.BooleanField(default=False)
    include_statistics = serializers.BooleanField(default=False)
