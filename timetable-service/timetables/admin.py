"""
Admin configuration for timetable models.
"""
from django.contrib import admin
from .models import Timetable, TimetableEntry, GenerationTask, TimetableConstraint


@admin.register(Timetable)
class TimetableAdmin(admin.ModelAdmin):
    list_display = ('name', 'academic_year', 'semester', 'department', 'status', 'fitness_score', 'conflict_count', 'created_at')
    list_filter = ('status', 'academic_year', 'semester', 'department', 'hard_constraints_satisfied')
    search_fields = ('name', 'description')
    ordering = ('-created_at',)
    readonly_fields = ('fitness_score', 'conflict_count', 'hard_constraints_satisfied', 'soft_constraints_score', 'published_at')
    
    fieldsets = (
        (None, {
            'fields': ('name', 'description', 'academic_year', 'semester', 'department')
        }),
        ('Status', {
            'fields': ('status', 'created_by', 'published_by', 'published_at')
        }),
        ('Generation Results', {
            'fields': ('generation_algorithm', 'generation_parameters', 'fitness_score', 'conflict_count', 'hard_constraints_satisfied', 'soft_constraints_score'),
            'classes': ('collapse',)
        }),
    )


@admin.register(TimetableEntry)
class TimetableEntryAdmin(admin.ModelAdmin):
    list_display = ('course', 'lecturer', 'room', 'time_slot', 'timetable', 'is_locked', 'priority')
    list_filter = ('timetable', 'course__department', 'week_pattern', 'is_locked')
    search_fields = ('course__code', 'course__name', 'lecturer__first_name', 'lecturer__last_name', 'room__code')
    ordering = ('timetable', 'time_slot__day_of_week', 'time_slot__start_time')
    
    fieldsets = (
        (None, {
            'fields': ('timetable', 'course', 'lecturer', 'room', 'time_slot')
        }),
        ('Scheduling', {
            'fields': ('week_pattern', 'custom_weeks', 'is_locked', 'priority')
        }),
        ('Capacity', {
            'fields': ('max_students', 'enrolled_students')
        }),
        ('Notes', {
            'fields': ('notes',)
        }),
    )
    
    filter_horizontal = ('enrolled_students',)


@admin.register(GenerationTask)
class GenerationTaskAdmin(admin.ModelAdmin):
    list_display = ('timetable', 'status', 'algorithm_name', 'progress_percentage', 'started_by', 'started_at', 'completed_at')
    list_filter = ('status', 'algorithm_name', 'started_at')
    search_fields = ('timetable__name', 'task_id')
    ordering = ('-created_at',)
    readonly_fields = ('task_id', 'started_at', 'completed_at', 'progress_percentage', 'current_generation', 'best_fitness', 'result_data', 'error_message', 'log_data')
    
    fieldsets = (
        (None, {
            'fields': ('timetable', 'status', 'algorithm_name', 'started_by')
        }),
        ('Parameters', {
            'fields': ('parameters',)
        }),
        ('Progress', {
            'fields': ('task_id', 'started_at', 'completed_at', 'progress_percentage', 'current_generation', 'total_generations', 'best_fitness'),
            'classes': ('collapse',)
        }),
        ('Results', {
            'fields': ('result_data', 'error_message', 'log_data'),
            'classes': ('collapse',)
        }),
    )


@admin.register(TimetableConstraint)
class TimetableConstraintAdmin(admin.ModelAdmin):
    list_display = ('name', 'constraint_type', 'category', 'is_enabled', 'weight', 'department')
    list_filter = ('constraint_type', 'category', 'is_enabled', 'department')
    search_fields = ('name', 'description')
    ordering = ('constraint_type', 'category', 'name')
    
    fieldsets = (
        (None, {
            'fields': ('name', 'description', 'constraint_type', 'category')
        }),
        ('Configuration', {
            'fields': ('is_enabled', 'weight', 'parameters')
        }),
        ('Scope', {
            'fields': ('department', 'academic_year')
        }),
    )
