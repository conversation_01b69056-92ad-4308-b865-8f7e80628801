# Timetable Generation Service Environment Configuration

# Django Settings
DEBUG=True
SECRET_KEY=timetable-service-secret-key-development
ALLOWED_HOSTS=localhost,127.0.0.1,timetable-service

# Database Configuration
MONGODB_URI=*****************************************************************************
MONGODB_DB_NAME=timetable_system

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# JWT Configuration
JWT_SECRET_KEY=jwt-secret-key-development
JWT_ACCESS_TOKEN_LIFETIME=60
JWT_REFRESH_TOKEN_LIFETIME=7
JWT_ALGORITHM=HS256

# Service URLs
USER_SERVICE_URL=http://user-service:8002
AUTH_SERVICE_URL=http://auth-service:8001
PREFERENCE_SERVICE_URL=http://preference-service:8003
NOTIFICATION_SERVICE_URL=http://notification-service:8005

# Internal service authentication
INTERNAL_SERVICE_TOKEN=internal-service-token

# CORS Configuration
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# Email Configuration
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
DEFAULT_FROM_EMAIL=<EMAIL>

# Timetable Generation Specific Settings
TIMETABLE_GENERATION_TIMEOUT=300
GENETIC_ALGORITHM_POPULATION_SIZE=100
GENETIC_ALGORITHM_GENERATIONS=50
GENETIC_ALGORITHM_MUTATION_RATE=0.1
GENETIC_ALGORITHM_CROSSOVER_RATE=0.8

# Export Settings
EXPORT_FILE_RETENTION_DAYS=7
MAX_EXPORT_FILE_SIZE=50

# Logging Level
LOG_LEVEL=INFO

# Performance Settings
CELERY_WORKER_CONCURRENCY=4
CELERY_WORKER_MAX_TASKS_PER_CHILD=1000

# Security Settings (Production)
SECURE_SSL_REDIRECT=False
SECURE_HSTS_SECONDS=0
SECURE_HSTS_INCLUDE_SUBDOMAINS=False
SECURE_HSTS_PRELOAD=False
SECURE_CONTENT_TYPE_NOSNIFF=True
SECURE_BROWSER_XSS_FILTER=True
X_FRAME_OPTIONS=DENY
