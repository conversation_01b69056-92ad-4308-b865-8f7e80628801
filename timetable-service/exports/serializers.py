"""
Serializers for export models.
"""
from rest_framework import serializers
from .models import ExportTask, ExportTemplate, ExportFormat
from core.serializers import UserSerializer


class ExportTaskSerializer(serializers.ModelSerializer):
    timetable_name = serializers.CharField(source='timetable.name', read_only=True)
    requested_by_name = serializers.CharField(source='requested_by.full_name', read_only=True)
    filename = serializers.ReadOnlyField()
    is_expired = serializers.ReadOnlyField()
    
    class Meta:
        model = ExportTask
        fields = [
            'id', 'timetable', 'timetable_name', 'format', 'status',
            'task_id', 'requested_by', 'requested_by_name', 'started_at',
            'completed_at', 'parameters', 'file_path', 'filename',
            'file_size', 'download_count', 'error_message', 'expires_at',
            'is_expired', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'task_id', 'started_at', 'completed_at', 'file_path',
            'file_size', 'download_count', 'error_message', 'created_at', 'updated_at'
        ]


class ExportTemplateSerializer(serializers.ModelSerializer):
    created_by_name = serializers.CharField(source='created_by.full_name', read_only=True)
    
    class Meta:
        model = ExportTemplate
        fields = [
            'id', 'name', 'description', 'template_type', 'template_content',
            'css_content', 'is_default', 'is_public', 'created_by',
            'created_by_name', 'parameters', 'is_active',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_by', 'created_at', 'updated_at']


class ExportFormatSerializer(serializers.ModelSerializer):
    class Meta:
        model = ExportFormat
        fields = [
            'id', 'name', 'description', 'file_extension', 'mime_type',
            'is_enabled', 'supports_filtering', 'supports_templates',
            'max_file_size_mb', 'processor_class', 'default_parameters',
            'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
