"""
Views for export functionality.
"""
from rest_framework import viewsets, status
from rest_framework.decorators import api_view, permission_classes, action
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import <PERSON>Filter, OrderingFilter
from django.http import FileResponse, Http404
from django.shortcuts import get_object_or_404
from .models import ExportTask, ExportTemplate, ExportFormat
from .serializers import ExportTaskSerializer, ExportTemplateSerializer, ExportFormatSerializer
from .tasks import export_timetable_task, batch_export_timetables
import os
import logging

logger = logging.getLogger(__name__)


class ExportTaskViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for viewing export tasks.
    """
    queryset = ExportTask.objects.all()
    serializer_class = ExportTaskSerializer
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['format', 'status', 'timetable']
    search_fields = ['timetable__name']
    ordering_fields = ['created_at', 'completed_at', 'file_size']
    ordering = ['-created_at']
    
    @action(detail=True, methods=['post'])
    def retry(self, request, pk=None):
        """Retry a failed export task."""
        task = self.get_object()
        
        if task.status != 'failed':
            return Response(
                {'error': 'Only failed tasks can be retried'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Create new export task
        new_task = export_timetable_task.delay(str(task.timetable.id), task.parameters)
        
        return Response({
            'message': 'Export task retried',
            'new_task_id': new_task.id
        })


class ExportTemplateViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing export templates.
    """
    queryset = ExportTemplate.objects.all()
    serializer_class = ExportTemplateSerializer
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['template_type', 'is_default', 'is_public']
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'created_at']
    ordering = ['template_type', 'name']
    
    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)


class ExportFormatViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for viewing export formats.
    """
    queryset = ExportFormat.objects.filter(is_enabled=True)
    serializer_class = ExportFormatSerializer
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['supports_filtering', 'supports_templates']
    search_fields = ['name', 'description']
    ordering_fields = ['name']
    ordering = ['name']


@api_view(['GET'])
@permission_classes([AllowAny])
def download_export(request, task_id):
    """
    Download an exported file.
    """
    try:
        task = ExportTask.objects.get(id=task_id)
    except ExportTask.DoesNotExist:
        raise Http404("Export task not found")
    
    if task.status != 'completed':
        return Response(
            {'error': 'Export is not completed yet'},
            status=status.HTTP_400_BAD_REQUEST
        )
    
    if task.is_expired:
        return Response(
            {'error': 'Export file has expired'},
            status=status.HTTP_410_GONE
        )
    
    if not task.file_path or not os.path.exists(task.file_path):
        return Response(
            {'error': 'Export file not found'},
            status=status.HTTP_404_NOT_FOUND
        )
    
    # Mark as downloaded
    task.mark_downloaded()
    
    # Return file response
    response = FileResponse(
        open(task.file_path, 'rb'),
        content_type=ExportFormat.objects.filter(name=task.format).first().mime_type if ExportFormat.objects.filter(name=task.format).exists() else 'application/octet-stream'
    )
    response['Content-Disposition'] = f'attachment; filename="{task.filename}"'
    
    return response


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def batch_export(request):
    """
    Export multiple timetables in batch.
    """
    timetable_ids = request.data.get('timetable_ids', [])
    export_format = request.data.get('format')
    export_parameters = request.data.get('parameters', {})
    
    if not timetable_ids:
        return Response(
            {'error': 'timetable_ids is required'},
            status=status.HTTP_400_BAD_REQUEST
        )
    
    if not export_format:
        return Response(
            {'error': 'format is required'},
            status=status.HTTP_400_BAD_REQUEST
        )
    
    # Validate format
    if not ExportFormat.objects.filter(name=export_format, is_enabled=True).exists():
        return Response(
            {'error': f'Export format "{export_format}" is not supported'},
            status=status.HTTP_400_BAD_REQUEST
        )
    
    # Start batch export
    task = batch_export_timetables.delay(timetable_ids, export_format, export_parameters)
    
    return Response({
        'message': 'Batch export started',
        'task_id': task.id,
        'timetable_count': len(timetable_ids),
        'format': export_format
    }, status=status.HTTP_202_ACCEPTED)
