"""
Models for export functionality.
"""
from django.db import models
from django.core.validators import FileExtensionValidator
from core.models import BaseModel, User
from timetables.models import Timetable
import os


class ExportTask(BaseModel):
    """
    Model for tracking export tasks.
    """
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    ]
    
    FORMAT_CHOICES = [
        ('pdf', 'PDF'),
        ('excel', 'Excel'),
        ('ical', 'iCal'),
        ('json', 'JSON'),
        ('csv', 'CSV'),
    ]
    
    timetable = models.ForeignKey(Timetable, on_delete=models.CASCADE, related_name='export_tasks')
    format = models.CharField(max_length=10, choices=FORMAT_CHOICES)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    
    # Task metadata
    task_id = models.Char<PERSON>ield(max_length=255, unique=True, null=True, blank=True)
    requested_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    
    # Export parameters
    parameters = models.JSONField(default=dict, blank=True)
    
    # File information
    file_path = models.CharField(max_length=500, blank=True)
    file_size = models.PositiveIntegerField(null=True, blank=True, help_text="File size in bytes")
    download_count = models.PositiveIntegerField(default=0)
    
    # Error handling
    error_message = models.TextField(blank=True)
    
    # Expiration
    expires_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        db_table = 'exports_task'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.timetable.name} - {self.format.upper()} Export ({self.status})"
    
    @property
    def filename(self):
        """Get the filename from file_path."""
        if self.file_path:
            return os.path.basename(self.file_path)
        return None
    
    @property
    def is_expired(self):
        """Check if the export has expired."""
        if self.expires_at:
            from django.utils import timezone
            return timezone.now() > self.expires_at
        return False
    
    def mark_downloaded(self):
        """Increment download count."""
        self.download_count += 1
        self.save(update_fields=['download_count'])


class ExportTemplate(BaseModel):
    """
    Model for export templates.
    """
    TEMPLATE_TYPE_CHOICES = [
        ('pdf', 'PDF Template'),
        ('excel', 'Excel Template'),
        ('html', 'HTML Template'),
    ]
    
    name = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    template_type = models.CharField(max_length=10, choices=TEMPLATE_TYPE_CHOICES)
    
    # Template content
    template_content = models.TextField(help_text="Template content (HTML, etc.)")
    css_content = models.TextField(blank=True, help_text="CSS styles for the template")
    
    # Template settings
    is_default = models.BooleanField(default=False)
    is_public = models.BooleanField(default=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    
    # Template parameters
    parameters = models.JSONField(default=dict, blank=True)
    
    class Meta:
        db_table = 'exports_template'
        ordering = ['template_type', 'name']
    
    def __str__(self):
        return f"{self.name} ({self.template_type.upper()})"


class ExportFormat(BaseModel):
    """
    Model for configuring export formats.
    """
    name = models.CharField(max_length=50, unique=True)
    description = models.TextField()
    file_extension = models.CharField(max_length=10)
    mime_type = models.CharField(max_length=100)
    
    # Format settings
    is_enabled = models.BooleanField(default=True)
    supports_filtering = models.BooleanField(default=True)
    supports_templates = models.BooleanField(default=False)
    max_file_size_mb = models.PositiveIntegerField(default=50)
    
    # Processing settings
    processor_class = models.CharField(max_length=200, help_text="Python class for processing this format")
    default_parameters = models.JSONField(default=dict, blank=True)
    
    class Meta:
        db_table = 'exports_format'
        ordering = ['name']
    
    def __str__(self):
        return f"{self.name} (.{self.file_extension})"
