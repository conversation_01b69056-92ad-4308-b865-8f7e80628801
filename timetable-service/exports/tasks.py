"""
Celery tasks for export functionality.
"""
from celery import shared_task
from django.conf import settings
from django.utils import timezone
from .models import ExportTask
from .processors import PDFProcessor, ExcelProcessor, ICalProcessor, JSONProcessor
import logging
import os
from datetime import timedelta

logger = logging.getLogger(__name__)


@shared_task(bind=True)
def export_timetable_task(self, timetable_id, export_parameters):
    """
    Celery task to export a timetable in the specified format.
    """
    try:
        from timetables.models import Timetable
        
        # Get the timetable
        timetable = Timetable.objects.get(id=timetable_id)
        export_format = export_parameters.get('format')
        
        # Create or get export task
        export_task = ExportTask.objects.filter(
            timetable=timetable,
            format=export_format,
            status='pending'
        ).first()
        
        if not export_task:
            export_task = ExportTask.objects.create(
                timetable=timetable,
                format=export_format,
                parameters=export_parameters,
                task_id=self.request.id
            )
        else:
            export_task.task_id = self.request.id
            export_task.save()
        
        # Start the task
        export_task.status = 'processing'
        export_task.started_at = timezone.now()
        export_task.save()
        
        logger.info(f"Starting {export_format} export for timetable {timetable.name}")
        
        # Get the appropriate processor
        processor = get_processor(export_format)
        if not processor:
            raise ValueError(f"Unsupported export format: {export_format}")
        
        # Process the export
        file_path = processor.process(timetable, export_parameters)
        
        # Calculate file size
        file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
        
        # Set expiration date
        expires_at = timezone.now() + timedelta(days=settings.EXPORT_FILE_RETENTION_DAYS)
        
        # Update export task
        export_task.status = 'completed'
        export_task.completed_at = timezone.now()
        export_task.file_path = file_path
        export_task.file_size = file_size
        export_task.expires_at = expires_at
        export_task.save()
        
        logger.info(f"Export completed for timetable {timetable.name}. File: {file_path}")
        
        return {
            'export_task_id': str(export_task.id),
            'file_path': file_path,
            'file_size': file_size,
            'format': export_format
        }
        
    except Exception as e:
        error_message = f"Export failed: {str(e)}"
        logger.error(error_message)
        
        # Update task status
        if 'export_task' in locals():
            export_task.status = 'failed'
            export_task.completed_at = timezone.now()
            export_task.error_message = error_message
            export_task.save()
        
        raise


@shared_task
def cleanup_expired_exports():
    """
    Clean up expired export files.
    """
    from datetime import datetime
    
    now = timezone.now()
    expired_tasks = ExportTask.objects.filter(
        status='completed',
        expires_at__lt=now
    )
    
    deleted_files = 0
    deleted_tasks = 0
    
    for task in expired_tasks:
        # Delete the file if it exists
        if task.file_path and os.path.exists(task.file_path):
            try:
                os.remove(task.file_path)
                deleted_files += 1
                logger.info(f"Deleted expired export file: {task.file_path}")
            except OSError as e:
                logger.error(f"Error deleting file {task.file_path}: {e}")
        
        # Delete the task record
        task.delete()
        deleted_tasks += 1
    
    logger.info(f"Cleanup completed: {deleted_files} files and {deleted_tasks} tasks deleted")
    return {'deleted_files': deleted_files, 'deleted_tasks': deleted_tasks}


def get_processor(export_format):
    """
    Get the appropriate processor for the export format.
    """
    processors = {
        'pdf': PDFProcessor,
        'excel': ExcelProcessor,
        'ical': ICalProcessor,
        'json': JSONProcessor,
    }
    
    processor_class = processors.get(export_format)
    if processor_class:
        return processor_class()
    return None


@shared_task
def batch_export_timetables(timetable_ids, export_format, export_parameters):
    """
    Export multiple timetables in batch.
    """
    try:
        results = []
        
        for timetable_id in timetable_ids:
            try:
                result = export_timetable_task.delay(timetable_id, {
                    'format': export_format,
                    **export_parameters
                })
                results.append({
                    'timetable_id': timetable_id,
                    'task_id': result.id,
                    'status': 'started'
                })
            except Exception as e:
                results.append({
                    'timetable_id': timetable_id,
                    'status': 'failed',
                    'error': str(e)
                })
        
        return {
            'batch_id': f"batch_{timezone.now().strftime('%Y%m%d_%H%M%S')}",
            'total_timetables': len(timetable_ids),
            'results': results
        }
        
    except Exception as e:
        logger.error(f"Batch export failed: {e}")
        raise


@shared_task
def generate_export_report(export_task_ids):
    """
    Generate a report for multiple export tasks.
    """
    try:
        export_tasks = ExportTask.objects.filter(id__in=export_task_ids)
        
        report_data = {
            'generated_at': timezone.now().isoformat(),
            'total_tasks': len(export_task_ids),
            'completed_tasks': export_tasks.filter(status='completed').count(),
            'failed_tasks': export_tasks.filter(status='failed').count(),
            'pending_tasks': export_tasks.filter(status__in=['pending', 'processing']).count(),
            'total_file_size': sum(task.file_size or 0 for task in export_tasks),
            'tasks': []
        }
        
        for task in export_tasks:
            task_data = {
                'id': str(task.id),
                'timetable_name': task.timetable.name,
                'format': task.format,
                'status': task.status,
                'file_size': task.file_size,
                'download_count': task.download_count,
                'created_at': task.created_at.isoformat(),
                'completed_at': task.completed_at.isoformat() if task.completed_at else None,
                'error_message': task.error_message
            }
            report_data['tasks'].append(task_data)
        
        return report_data
        
    except Exception as e:
        logger.error(f"Export report generation failed: {e}")
        raise
