"""
URLs configuration for exports app.
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

app_name = 'exports'

# Create router for viewsets
router = DefaultRouter()
router.register(r'tasks', views.ExportTaskViewSet, basename='export-task')
router.register(r'templates', views.ExportTemplateViewSet, basename='export-template')
router.register(r'formats', views.ExportFormatViewSet, basename='export-format')

urlpatterns = [
    # ViewSet URLs
    path('', include(router.urls)),
    
    # Download endpoint
    path('download/<uuid:task_id>/', views.download_export, name='download-export'),
    
    # Batch export
    path('batch/', views.batch_export, name='batch-export'),
]
