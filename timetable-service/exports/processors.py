"""
Export processors for different file formats.
"""
import os
import json
from datetime import datetime
from django.conf import settings
from django.template.loader import render_to_string
from timetables.models import TimetableEntry
import logging

logger = logging.getLogger(__name__)


class BaseProcessor:
    """
    Base class for export processors.
    """
    
    def __init__(self):
        self.export_dir = os.path.join(settings.MEDIA_ROOT, 'exports')
        os.makedirs(self.export_dir, exist_ok=True)
    
    def process(self, timetable, parameters):
        """
        Process the export. Must be implemented by subclasses.
        """
        raise NotImplementedError("Subclasses must implement process method")
    
    def get_timetable_data(self, timetable, parameters):
        """
        Get timetable data with optional filtering.
        """
        entries = TimetableEntry.objects.filter(timetable=timetable).select_related(
            'course', 'lecturer', 'room', 'time_slot'
        ).order_by('time_slot__day_of_week', 'time_slot__start_time')
        
        # Apply filters
        if parameters.get('department'):
            entries = entries.filter(course__department_id=parameters['department'])
        if parameters.get('lecturer'):
            entries = entries.filter(lecturer_id=parameters['lecturer'])
        if parameters.get('room'):
            entries = entries.filter(room_id=parameters['room'])
        if parameters.get('course'):
            entries = entries.filter(course_id=parameters['course'])
        
        return entries
    
    def generate_filename(self, timetable, format_ext):
        """
        Generate a unique filename for the export.
        """
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        safe_name = "".join(c for c in timetable.name if c.isalnum() or c in (' ', '-', '_')).rstrip()
        safe_name = safe_name.replace(' ', '_')
        return f"{safe_name}_{timestamp}.{format_ext}"


class PDFProcessor(BaseProcessor):
    """
    PDF export processor using ReportLab.
    """
    
    def process(self, timetable, parameters):
        from reportlab.lib import colors
        from reportlab.lib.pagesizes import letter, A4, landscape
        from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib.units import inch
        
        # Get timetable data
        entries = self.get_timetable_data(timetable, parameters)
        
        # Generate filename
        filename = self.generate_filename(timetable, 'pdf')
        file_path = os.path.join(self.export_dir, filename)
        
        # Create PDF document
        layout = parameters.get('layout', 'weekly')
        page_size = landscape(A4) if layout == 'weekly' else A4
        doc = SimpleDocTemplate(file_path, pagesize=page_size)
        
        # Build content
        story = []
        styles = getSampleStyleSheet()
        
        # Title
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=16,
            spaceAfter=30,
            alignment=1  # Center alignment
        )
        story.append(Paragraph(f"Timetable: {timetable.name}", title_style))
        story.append(Paragraph(f"Academic Year: {timetable.academic_year.name} - {timetable.semester.title()}", styles['Normal']))
        story.append(Spacer(1, 20))
        
        if layout == 'weekly':
            story.extend(self._create_weekly_layout(entries, styles))
        elif layout == 'daily':
            story.extend(self._create_daily_layout(entries, styles))
        elif layout == 'lecturer':
            story.extend(self._create_lecturer_layout(entries, styles))
        elif layout == 'room':
            story.extend(self._create_room_layout(entries, styles))
        
        # Build PDF
        doc.build(story)
        
        logger.info(f"PDF export completed: {file_path}")
        return file_path
    
    def _create_weekly_layout(self, entries, styles):
        """Create weekly timetable layout."""
        from reportlab.platypus import Table, TableStyle
        from reportlab.lib import colors
        
        # Group entries by day and time
        schedule = {}
        time_slots = set()
        
        for entry in entries:
            day = entry.time_slot.day_of_week
            time_key = f"{entry.time_slot.start_time.strftime('%H:%M')}-{entry.time_slot.end_time.strftime('%H:%M')}"
            time_slots.add(time_key)
            
            if time_key not in schedule:
                schedule[time_key] = {}
            
            schedule[time_key][day] = f"{entry.course.code}\n{entry.lecturer.full_name}\n{entry.room.code}"
        
        # Create table data
        days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
        sorted_times = sorted(time_slots)
        
        # Header row
        table_data = [['Time'] + days]
        
        # Data rows
        for time_slot in sorted_times:
            row = [time_slot]
            for day_idx in range(7):
                cell_content = schedule.get(time_slot, {}).get(day_idx, '')
                row.append(cell_content)
            table_data.append(row)
        
        # Create table
        table = Table(table_data)
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('FONTSIZE', (0, 1), (-1, -1), 8),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
        ]))
        
        return [table]
    
    def _create_daily_layout(self, entries, styles):
        """Create daily breakdown layout."""
        content = []
        
        # Group by day
        days = {}
        for entry in entries:
            day_name = entry.time_slot.get_day_of_week_display()
            if day_name not in days:
                days[day_name] = []
            days[day_name].append(entry)
        
        for day_name, day_entries in days.items():
            content.append(Paragraph(f"<b>{day_name}</b>", styles['Heading2']))
            
            # Create table for the day
            table_data = [['Time', 'Course', 'Lecturer', 'Room']]
            for entry in sorted(day_entries, key=lambda x: x.time_slot.start_time):
                table_data.append([
                    f"{entry.time_slot.start_time.strftime('%H:%M')}-{entry.time_slot.end_time.strftime('%H:%M')}",
                    f"{entry.course.code} - {entry.course.name}",
                    entry.lecturer.full_name,
                    f"{entry.room.code} - {entry.room.name}"
                ])
            
            table = Table(table_data)
            table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, -1), 9),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ]))
            
            content.append(table)
            content.append(Spacer(1, 20))
        
        return content
    
    def _create_lecturer_layout(self, entries, styles):
        """Create lecturer-wise layout."""
        content = []
        
        # Group by lecturer
        lecturers = {}
        for entry in entries:
            lecturer_name = entry.lecturer.full_name
            if lecturer_name not in lecturers:
                lecturers[lecturer_name] = []
            lecturers[lecturer_name].append(entry)
        
        for lecturer_name, lecturer_entries in lecturers.items():
            content.append(Paragraph(f"<b>{lecturer_name}</b>", styles['Heading2']))
            
            # Create table for the lecturer
            table_data = [['Day', 'Time', 'Course', 'Room']]
            for entry in sorted(lecturer_entries, key=lambda x: (x.time_slot.day_of_week, x.time_slot.start_time)):
                table_data.append([
                    entry.time_slot.get_day_of_week_display(),
                    f"{entry.time_slot.start_time.strftime('%H:%M')}-{entry.time_slot.end_time.strftime('%H:%M')}",
                    f"{entry.course.code} - {entry.course.name}",
                    f"{entry.room.code} - {entry.room.name}"
                ])
            
            table = Table(table_data)
            table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, -1), 9),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ]))
            
            content.append(table)
            content.append(Spacer(1, 20))
        
        return content
    
    def _create_room_layout(self, entries, styles):
        """Create room-wise layout."""
        content = []
        
        # Group by room
        rooms = {}
        for entry in entries:
            room_name = f"{entry.room.code} - {entry.room.name}"
            if room_name not in rooms:
                rooms[room_name] = []
            rooms[room_name].append(entry)
        
        for room_name, room_entries in rooms.items():
            content.append(Paragraph(f"<b>{room_name}</b>", styles['Heading2']))
            
            # Create table for the room
            table_data = [['Day', 'Time', 'Course', 'Lecturer']]
            for entry in sorted(room_entries, key=lambda x: (x.time_slot.day_of_week, x.time_slot.start_time)):
                table_data.append([
                    entry.time_slot.get_day_of_week_display(),
                    f"{entry.time_slot.start_time.strftime('%H:%M')}-{entry.time_slot.end_time.strftime('%H:%M')}",
                    f"{entry.course.code} - {entry.course.name}",
                    entry.lecturer.full_name
                ])
            
            table = Table(table_data)
            table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, -1), 9),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ]))
            
            content.append(table)
            content.append(Spacer(1, 20))
        
        return content


class ExcelProcessor(BaseProcessor):
    """
    Excel export processor using openpyxl.
    """
    
    def process(self, timetable, parameters):
        from openpyxl import Workbook
        from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
        from openpyxl.utils import get_column_letter
        
        # Get timetable data
        entries = self.get_timetable_data(timetable, parameters)
        
        # Generate filename
        filename = self.generate_filename(timetable, 'xlsx')
        file_path = os.path.join(self.export_dir, filename)
        
        # Create workbook
        wb = Workbook()
        
        # Create different sheets based on layout
        layout = parameters.get('layout', 'weekly')
        
        if layout == 'weekly':
            self._create_weekly_sheet(wb, entries, timetable)
        
        # Always create a detailed sheet
        self._create_detailed_sheet(wb, entries, timetable)
        
        # Create summary sheet
        self._create_summary_sheet(wb, entries, timetable)
        
        # Remove default sheet if we created others
        if len(wb.worksheets) > 1 and wb.worksheets[0].title == 'Sheet':
            wb.remove(wb.worksheets[0])
        
        # Save workbook
        wb.save(file_path)
        
        logger.info(f"Excel export completed: {file_path}")
        return file_path
    
    def _create_weekly_sheet(self, wb, entries, timetable):
        """Create weekly timetable sheet."""
        ws = wb.create_sheet("Weekly Timetable")
        
        # Styles
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        border = Border(left=Side(style='thin'), right=Side(style='thin'), 
                       top=Side(style='thin'), bottom=Side(style='thin'))
        
        # Group entries by day and time
        schedule = {}
        time_slots = set()
        
        for entry in entries:
            day = entry.time_slot.day_of_week
            time_key = f"{entry.time_slot.start_time.strftime('%H:%M')}-{entry.time_slot.end_time.strftime('%H:%M')}"
            time_slots.add(time_key)
            
            if time_key not in schedule:
                schedule[time_key] = {}
            
            schedule[time_key][day] = f"{entry.course.code}\n{entry.lecturer.full_name}\n{entry.room.code}"
        
        # Headers
        days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
        headers = ['Time'] + days
        
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = Alignment(horizontal='center')
            cell.border = border
        
        # Data
        sorted_times = sorted(time_slots)
        for row, time_slot in enumerate(sorted_times, 2):
            ws.cell(row=row, column=1, value=time_slot).border = border
            
            for day_idx in range(7):
                cell_content = schedule.get(time_slot, {}).get(day_idx, '')
                cell = ws.cell(row=row, column=day_idx + 2, value=cell_content)
                cell.border = border
                cell.alignment = Alignment(wrap_text=True, vertical='top')
        
        # Adjust column widths
        for col in range(1, len(headers) + 1):
            ws.column_dimensions[get_column_letter(col)].width = 15
    
    def _create_detailed_sheet(self, wb, entries, timetable):
        """Create detailed entries sheet."""
        ws = wb.create_sheet("Detailed Schedule")
        
        # Headers
        headers = ['Day', 'Time', 'Course Code', 'Course Name', 'Lecturer', 'Room', 'Capacity', 'Type']
        
        # Style headers
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = Alignment(horizontal='center')
        
        # Data
        for row, entry in enumerate(sorted(entries, key=lambda x: (x.time_slot.day_of_week, x.time_slot.start_time)), 2):
            ws.cell(row=row, column=1, value=entry.time_slot.get_day_of_week_display())
            ws.cell(row=row, column=2, value=f"{entry.time_slot.start_time.strftime('%H:%M')}-{entry.time_slot.end_time.strftime('%H:%M')}")
            ws.cell(row=row, column=3, value=entry.course.code)
            ws.cell(row=row, column=4, value=entry.course.name)
            ws.cell(row=row, column=5, value=entry.lecturer.full_name)
            ws.cell(row=row, column=6, value=f"{entry.room.code} - {entry.room.name}")
            ws.cell(row=row, column=7, value=entry.room.capacity)
            ws.cell(row=row, column=8, value=entry.course.course_type.title())
        
        # Auto-adjust column widths
        for col in range(1, len(headers) + 1):
            ws.column_dimensions[get_column_letter(col)].width = 20
    
    def _create_summary_sheet(self, wb, entries, timetable):
        """Create summary statistics sheet."""
        ws = wb.create_sheet("Summary")
        
        # Timetable info
        ws.cell(row=1, column=1, value="Timetable Summary").font = Font(bold=True, size=14)
        ws.cell(row=3, column=1, value="Timetable Name:")
        ws.cell(row=3, column=2, value=timetable.name)
        ws.cell(row=4, column=1, value="Academic Year:")
        ws.cell(row=4, column=2, value=timetable.academic_year.name)
        ws.cell(row=5, column=1, value="Semester:")
        ws.cell(row=5, column=2, value=timetable.semester.title())
        
        # Statistics
        ws.cell(row=7, column=1, value="Statistics").font = Font(bold=True)
        ws.cell(row=8, column=1, value="Total Classes:")
        ws.cell(row=8, column=2, value=len(entries))
        ws.cell(row=9, column=1, value="Unique Courses:")
        ws.cell(row=9, column=2, value=len(set(entry.course.id for entry in entries)))
        ws.cell(row=10, column=1, value="Unique Lecturers:")
        ws.cell(row=10, column=2, value=len(set(entry.lecturer.id for entry in entries)))
        ws.cell(row=11, column=1, value="Unique Rooms:")
        ws.cell(row=11, column=2, value=len(set(entry.room.id for entry in entries)))


class ICalProcessor(BaseProcessor):
    """
    iCal export processor using icalendar.
    """
    
    def process(self, timetable, parameters):
        from icalendar import Calendar, Event
        from datetime import datetime, timedelta
        import pytz
        
        # Get timetable data
        entries = self.get_timetable_data(timetable, parameters)
        
        # Generate filename
        filename = self.generate_filename(timetable, 'ics')
        file_path = os.path.join(self.export_dir, filename)
        
        # Create calendar
        cal = Calendar()
        cal.add('prodid', '-//Timetable Management System//Timetable//EN')
        cal.add('version', '2.0')
        cal.add('calscale', 'GREGORIAN')
        cal.add('method', 'PUBLISH')
        cal.add('x-wr-calname', f"{timetable.name} - {timetable.academic_year.name}")
        cal.add('x-wr-caldesc', f"Timetable for {timetable.academic_year.name} {timetable.semester}")
        
        # Get timezone
        tz = pytz.timezone(settings.TIME_ZONE)
        
        # Create events for each entry
        # For this example, we'll create recurring events for the semester
        semester_start = timetable.academic_year.start_date
        semester_end = timetable.academic_year.end_date
        
        for entry in entries:
            event = Event()
            
            # Calculate the first occurrence date
            days_ahead = entry.time_slot.day_of_week - semester_start.weekday()
            if days_ahead <= 0:
                days_ahead += 7
            first_occurrence = semester_start + timedelta(days=days_ahead)
            
            # Create datetime objects
            start_datetime = datetime.combine(
                first_occurrence,
                entry.time_slot.start_time
            )
            end_datetime = datetime.combine(
                first_occurrence,
                entry.time_slot.end_time
            )
            
            # Localize to timezone
            start_datetime = tz.localize(start_datetime)
            end_datetime = tz.localize(end_datetime)
            
            # Event details
            event.add('uid', f"{entry.id}@timetable.system")
            event.add('dtstart', start_datetime)
            event.add('dtend', end_datetime)
            event.add('summary', f"{entry.course.code} - {entry.course.name}")
            event.add('description', f"Lecturer: {entry.lecturer.full_name}\nRoom: {entry.room.code} - {entry.room.name}")
            event.add('location', f"{entry.room.code} - {entry.room.name}")
            
            # Add recurrence rule (weekly until semester end)
            event.add('rrule', {'freq': 'weekly', 'until': semester_end})
            
            cal.add_component(event)
        
        # Write to file
        with open(file_path, 'wb') as f:
            f.write(cal.to_ical())
        
        logger.info(f"iCal export completed: {file_path}")
        return file_path


class JSONProcessor(BaseProcessor):
    """
    JSON export processor.
    """
    
    def process(self, timetable, parameters):
        # Get timetable data
        entries = self.get_timetable_data(timetable, parameters)
        
        # Generate filename
        filename = self.generate_filename(timetable, 'json')
        file_path = os.path.join(self.export_dir, filename)
        
        # Build JSON data
        data = {
            'timetable': {
                'id': str(timetable.id),
                'name': timetable.name,
                'description': timetable.description,
                'academic_year': timetable.academic_year.name,
                'semester': timetable.semester,
                'status': timetable.status,
                'created_at': timetable.created_at.isoformat(),
                'fitness_score': timetable.fitness_score,
                'conflict_count': timetable.conflict_count
            },
            'entries': [],
            'export_info': {
                'exported_at': datetime.now().isoformat(),
                'total_entries': 0,
                'parameters': parameters
            }
        }
        
        # Add entries
        for entry in entries:
            entry_data = {
                'id': str(entry.id),
                'course': {
                    'id': str(entry.course.id),
                    'code': entry.course.code,
                    'name': entry.course.name,
                    'type': entry.course.course_type,
                    'credits': entry.course.credits,
                    'duration_hours': entry.course.duration_hours
                },
                'lecturer': {
                    'id': str(entry.lecturer.id),
                    'name': entry.lecturer.full_name,
                    'email': entry.lecturer.email
                },
                'room': {
                    'id': str(entry.room.id),
                    'code': entry.room.code,
                    'name': entry.room.name,
                    'type': entry.room.room_type,
                    'capacity': entry.room.capacity,
                    'building': entry.room.building
                },
                'time_slot': {
                    'id': str(entry.time_slot.id),
                    'day_of_week': entry.time_slot.day_of_week,
                    'day_name': entry.time_slot.get_day_of_week_display(),
                    'start_time': entry.time_slot.start_time.strftime('%H:%M'),
                    'end_time': entry.time_slot.end_time.strftime('%H:%M'),
                    'duration_minutes': entry.time_slot.duration_minutes
                },
                'week_pattern': entry.week_pattern,
                'is_locked': entry.is_locked,
                'priority': entry.priority,
                'max_students': entry.max_students
            }
            data['entries'].append(entry_data)
        
        data['export_info']['total_entries'] = len(data['entries'])
        
        # Write to file
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"JSON export completed: {file_path}")
        return file_path
