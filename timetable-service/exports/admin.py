"""
Admin configuration for export models.
"""
from django.contrib import admin
from .models import ExportTask, ExportTemplate, ExportFormat


@admin.register(ExportTask)
class ExportTaskAdmin(admin.ModelAdmin):
    list_display = ('timetable', 'format', 'status', 'requested_by', 'file_size', 'download_count', 'created_at')
    list_filter = ('format', 'status', 'created_at', 'expires_at')
    search_fields = ('timetable__name', 'task_id')
    ordering = ('-created_at',)
    readonly_fields = ('task_id', 'started_at', 'completed_at', 'file_path', 'file_size', 'download_count')
    
    fieldsets = (
        (None, {
            'fields': ('timetable', 'format', 'status', 'requested_by')
        }),
        ('Task Details', {
            'fields': ('task_id', 'started_at', 'completed_at', 'parameters')
        }),
        ('File Information', {
            'fields': ('file_path', 'file_size', 'download_count', 'expires_at')
        }),
        ('Error Information', {
            'fields': ('error_message',),
            'classes': ('collapse',)
        }),
    )


@admin.register(ExportTemplate)
class ExportTemplateAdmin(admin.ModelAdmin):
    list_display = ('name', 'template_type', 'is_default', 'is_public', 'created_by', 'created_at')
    list_filter = ('template_type', 'is_default', 'is_public')
    search_fields = ('name', 'description')
    ordering = ('template_type', 'name')
    
    fieldsets = (
        (None, {
            'fields': ('name', 'description', 'template_type', 'created_by')
        }),
        ('Settings', {
            'fields': ('is_default', 'is_public', 'parameters')
        }),
        ('Template Content', {
            'fields': ('template_content', 'css_content')
        }),
    )


@admin.register(ExportFormat)
class ExportFormatAdmin(admin.ModelAdmin):
    list_display = ('name', 'file_extension', 'mime_type', 'is_enabled', 'supports_filtering', 'supports_templates')
    list_filter = ('is_enabled', 'supports_filtering', 'supports_templates')
    search_fields = ('name', 'description')
    ordering = ('name',)
    
    fieldsets = (
        (None, {
            'fields': ('name', 'description', 'file_extension', 'mime_type')
        }),
        ('Capabilities', {
            'fields': ('is_enabled', 'supports_filtering', 'supports_templates', 'max_file_size_mb')
        }),
        ('Processing', {
            'fields': ('processor_class', 'default_parameters')
        }),
    )
