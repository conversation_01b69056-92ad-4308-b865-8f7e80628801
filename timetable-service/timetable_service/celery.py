"""
Celery configuration for timetable_service project.
"""
import os
from celery import Celery
from django.conf import settings

# Set the default Django settings module for the 'celery' program.
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'timetable_service.settings')

app = Celery('timetable_service')

# Using a string here means the worker doesn't have to serialize
# the configuration object to child processes.
app.config_from_object('django.conf:settings', namespace='CELERY')

# Load task modules from all registered Django apps.
app.autodiscover_tasks()

# Celery beat schedule for periodic tasks
app.conf.beat_schedule = {
    'cleanup-expired-exports': {
        'task': 'exports.tasks.cleanup_expired_exports',
        'schedule': 3600.0,  # Run every hour
    },
    'cleanup-old-generation-tasks': {
        'task': 'timetables.tasks.cleanup_old_generation_tasks',
        'schedule': 86400.0,  # Run daily
    },
}

app.conf.timezone = settings.TIME_ZONE

@app.task(bind=True)
def debug_task(self):
    print(f'Request: {self.request!r}')
