# Timetable Generation Service

The core microservice for generating academic timetables using genetic algorithms, with advanced conflict detection and multiple export formats.

## Overview

This service is the heart of the timetable management system, responsible for:

- **Intelligent Timetable Generation** using Genetic Algorithms (DEAP library)
- **Advanced Conflict Detection** with custom resolution suggestions
- **Multiple Export Formats** (PDF, Excel, iCal, JSON)
- **Real-time Progress Tracking** via WebSockets
- **Manual Timetable Adjustments** with drag-and-drop support
- **Integration** with User and Preference services

## Technology Stack

- **Framework**: Django REST Framework
- **Algorithm**: Genetic Algorithm (DEAP library)
- **Database**: MongoDB (shared with other services)
- **Task Queue**: Celery with Redis
- **Real-time**: Django Channels with Redis
- **Authentication**: JWT tokens from Authentication Service
- **Export Libraries**: ReportLab (PDF), openpyxl (Excel), icalendar (iCal)
- **Scientific Computing**: NumPy, SciPy
- **API Documentation**: Swagger/OpenAPI

## Key Features

### 🧬 Genetic Algorithm Engine
- Multi-objective optimization for timetable generation
- Configurable population size, generations, mutation/crossover rates
- Hard and soft constraint satisfaction
- Preference integration from Preference Service
- Workload balancing and room utilization optimization

### 🔍 Conflict Detection System
- Real-time conflict detection during generation
- Multiple conflict types (room, lecturer, capacity, compatibility)
- Automatic resolution suggestions
- Conflict severity classification
- Custom conflict rules and detection logic

### 📊 Export Capabilities
- **PDF**: Multiple layouts (weekly, daily, lecturer-wise, room-wise)
- **Excel**: Detailed spreadsheets with multiple sheets
- **iCal**: Calendar format for integration with calendar apps
- **JSON**: Structured data for API consumption
- Batch export for multiple timetables
- Customizable templates

### 🔄 Real-time Features
- WebSocket connections for live generation progress
- Real-time timetable updates
- Progress tracking with fitness scores
- Generation cancellation support

## API Endpoints

### Timetables
- `GET /api/timetables/` - List all timetables
- `POST /api/timetables/` - Create new timetable
- `GET /api/timetables/{id}/` - Get timetable details
- `POST /api/timetables/{id}/generate/` - Start generation
- `POST /api/timetables/{id}/publish/` - Publish timetable
- `GET /api/timetables/{id}/stats/` - Get statistics
- `POST /api/timetables/{id}/export/` - Export timetable

### Generation Tasks
- `GET /api/timetables/tasks/` - List generation tasks
- `GET /api/timetables/tasks/{id}/` - Get task details
- `POST /api/timetables/tasks/{id}/cancel/` - Cancel task

### Conflicts
- `GET /api/conflicts/` - List conflicts
- `POST /api/conflicts/detect/{timetable_id}/` - Detect conflicts
- `GET /api/conflicts/analyze/{timetable_id}/` - Analyze conflicts
- `POST /api/conflicts/{id}/resolve/` - Resolve conflict

### Exports
- `GET /api/exports/tasks/` - List export tasks
- `GET /api/exports/download/{task_id}/` - Download export
- `POST /api/exports/batch/` - Batch export

### Algorithms
- `GET /api/algorithms/genetic-algorithm/info/` - Algorithm info
- `POST /api/algorithms/genetic-algorithm/test/` - Test algorithm
- `GET /api/algorithms/parameters/` - Get parameters

## Genetic Algorithm Configuration

### Default Parameters
```python
GENETIC_ALGORITHM_POPULATION_SIZE = 100
GENETIC_ALGORITHM_GENERATIONS = 50
GENETIC_ALGORITHM_MUTATION_RATE = 0.1
GENETIC_ALGORITHM_CROSSOVER_RATE = 0.8
```

### Fitness Function Components
1. **Hard Constraint Violations** (heavily penalized)
   - Room conflicts (same room, same time)
   - Lecturer conflicts (same lecturer, same time)
   - Capacity violations
   - Room-course type compatibility

2. **Soft Constraint Satisfaction**
   - Lecturer time preferences
   - Student preferences
   - Workload distribution
   - Room utilization efficiency

3. **Optimization Objectives**
   - Minimize conflicts
   - Maximize preference satisfaction
   - Balance lecturer workload
   - Optimize room usage

## WebSocket Endpoints

### Generation Progress
```
ws://localhost:8004/ws/timetables/{timetable_id}/generation/
```

**Messages:**
- `generation_progress` - Real-time progress updates
- `generation_completed` - Generation finished
- `generation_failed` - Generation failed

### Timetable Updates
```
ws://localhost:8004/ws/timetables/{timetable_id}/
```

**Messages:**
- `timetable_updated` - Timetable modified
- `entry_created` - New entry added
- `entry_updated` - Entry modified
- `entry_deleted` - Entry removed

## Environment Variables

```bash
# Core Settings
DEBUG=False
SECRET_KEY=your-secret-key
ALLOWED_HOSTS=localhost,timetable-service

# Database
MONGODB_URI=*********************************************************
MONGODB_DB_NAME=timetable_system

# Redis & Celery
REDIS_URL=redis://localhost:6379/0
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# Service URLs
USER_SERVICE_URL=http://user-service:8002
PREFERENCE_SERVICE_URL=http://preference-service:8003
NOTIFICATION_SERVICE_URL=http://notification-service:8005

# Algorithm Parameters
GENETIC_ALGORITHM_POPULATION_SIZE=100
GENETIC_ALGORITHM_GENERATIONS=50
GENETIC_ALGORITHM_MUTATION_RATE=0.1
GENETIC_ALGORITHM_CROSSOVER_RATE=0.8

# Export Settings
EXPORT_FILE_RETENTION_DAYS=7
MAX_EXPORT_FILE_SIZE=50
```

## Docker Deployment

### Build Image
```bash
docker build -t timetable/timetable-service:latest .
```

### Run Service
```bash
docker run -d \
  --name timetable-service \
  -p 8004:8004 \
  --env-file .env \
  timetable/timetable-service:latest
```

### Run Celery Worker
```bash
docker run -d \
  --name timetable-celery-worker \
  --env-file .env \
  timetable/timetable-service:latest \
  celery -A timetable_service worker --loglevel=info
```

## Kubernetes Deployment

```bash
# Deploy service
kubectl apply -f k8s/timetable-service/

# Check status
kubectl get pods -n timetable-system -l app=timetable-service
```

## Development Setup

### Prerequisites
- Python 3.11+
- MongoDB
- Redis
- Node.js (for frontend integration)

### Installation
```bash
# Clone repository
git clone <repository-url>
cd timetable-service

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Set up environment
cp .env.example .env
# Edit .env with your configuration

# Run migrations
python manage.py migrate

# Create superuser
python manage.py createsuperuser

# Start development server
python manage.py runserver 0.0.0.0:8004

# In another terminal, start Celery worker
celery -A timetable_service worker --loglevel=info

# In another terminal, start Celery beat
celery -A timetable_service beat --loglevel=info
```

## Testing

### Run Tests
```bash
# Run all tests
python manage.py test

# Run specific app tests
python manage.py test timetables
python manage.py test algorithms
python manage.py test conflicts
python manage.py test exports

# Run with coverage
pytest --cov=. --cov-report=html
```

### Test Genetic Algorithm
```bash
# Test algorithm with sample data
curl -X POST http://localhost:8004/api/algorithms/genetic-algorithm/test/ \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"timetable_id": "your-timetable-id", "parameters": {"generations": 10}}'
```

## Performance Considerations

### Genetic Algorithm Optimization
- **Population Size**: 50-200 for most cases
- **Generations**: 20-100 depending on complexity
- **Parallel Processing**: Use multiple Celery workers
- **Memory Usage**: Monitor for large datasets

### Celery Configuration
- **Worker Concurrency**: 2-4 per worker
- **Task Timeout**: 300 seconds for generation
- **Result Backend**: Redis for task results
- **Beat Scheduler**: Database scheduler for periodic tasks

### Database Optimization
- **Indexes**: On frequently queried fields
- **Connection Pooling**: Configure MongoDB connections
- **Query Optimization**: Use select_related and prefetch_related

## Monitoring and Logging

### Health Checks
```bash
curl http://localhost:8004/api/health/
```

### Metrics
- Generation success rate
- Average generation time
- Conflict detection accuracy
- Export completion rate

### Logs
- Application logs: `/app/logs/timetable_service.log`
- Celery logs: Separate worker logs
- Performance metrics: Integration with monitoring tools

## Integration with Other Services

### User Service
- Fetch lecturer and student information
- Sync user data for timetable generation

### Preference Service
- Retrieve lecturer time preferences
- Get student course preferences
- Integrate preferences into fitness function

### Notification Service
- Send generation completion notifications
- Alert on conflicts and failures
- Export completion notifications

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
