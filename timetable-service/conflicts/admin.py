"""
Admin configuration for conflict models.
"""
from django.contrib import admin
from .models import ConflictType, Conflict, ConflictResolution, ConflictRule


@admin.register(ConflictType)
class ConflictTypeAdmin(admin.ModelAdmin):
    list_display = ('name', 'severity', 'is_hard_constraint', 'auto_resolve', 'resolution_priority')
    list_filter = ('severity', 'is_hard_constraint', 'auto_resolve')
    search_fields = ('name', 'description')
    ordering = ('-severity', 'name')


@admin.register(Conflict)
class ConflictAdmin(admin.ModelAdmin):
    list_display = ('title', 'timetable', 'conflict_type', 'severity', 'status', 'detected_by', 'created_at')
    list_filter = ('status', 'severity', 'conflict_type', 'timetable')
    search_fields = ('title', 'description', 'timetable__name')
    ordering = ('-severity', '-created_at')
    readonly_fields = ('detected_by', 'resolved_by', 'resolved_at')
    
    fieldsets = (
        (None, {
            'fields': ('timetable', 'conflict_type', 'title', 'description', 'severity', 'status')
        }),
        ('Affected Entities', {
            'fields': ('affected_entries', 'affected_courses', 'affected_lecturers', 'affected_rooms', 'affected_time_slots'),
            'classes': ('collapse',)
        }),
        ('Resolution', {
            'fields': ('detected_by', 'resolved_by', 'resolved_at', 'resolution_notes')
        }),
        ('Metadata', {
            'fields': ('detection_data', 'resolution_data'),
            'classes': ('collapse',)
        }),
    )
    
    filter_horizontal = ('affected_entries', 'affected_courses', 'affected_lecturers', 'affected_rooms', 'affected_time_slots')


@admin.register(ConflictResolution)
class ConflictResolutionAdmin(admin.ModelAdmin):
    list_display = ('title', 'conflict', 'resolution_type', 'impact_score', 'is_applied', 'applied_by')
    list_filter = ('resolution_type', 'is_applied')
    search_fields = ('title', 'description', 'conflict__title')
    ordering = ('impact_score', '-created_at')
    readonly_fields = ('applied_by', 'applied_at')


@admin.register(ConflictRule)
class ConflictRuleAdmin(admin.ModelAdmin):
    list_display = ('name', 'conflict_type', 'is_enabled', 'execution_order', 'run_on_generation', 'run_on_modification')
    list_filter = ('conflict_type', 'is_enabled', 'run_on_generation', 'run_on_modification')
    search_fields = ('name', 'description')
    ordering = ('execution_order', 'name')
