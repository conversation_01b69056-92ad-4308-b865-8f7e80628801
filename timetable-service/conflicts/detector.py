"""
Conflict detection engine for timetables.
"""
from django.db.models import Q
from .models import Conflict, ConflictType
from timetables.models import TimetableEntry
import logging

logger = logging.getLogger(__name__)


class ConflictDetector:
    """
    Main conflict detection engine.
    """
    
    def __init__(self, timetable):
        self.timetable = timetable
        self.entries = TimetableEntry.objects.filter(timetable=timetable).select_related(
            'course', 'lecturer', 'room', 'time_slot'
        )
        
    def detect_all_conflicts(self):
        """
        Detect all types of conflicts in the timetable.
        """
        conflicts = []
        
        # Clear existing conflicts for this timetable
        Conflict.objects.filter(timetable=self.timetable).delete()
        
        # Detect different types of conflicts
        conflicts.extend(self.detect_room_conflicts())
        conflicts.extend(self.detect_lecturer_conflicts())
        conflicts.extend(self.detect_capacity_conflicts())
        conflicts.extend(self.detect_compatibility_conflicts())
        
        logger.info(f"Detected {len(conflicts)} conflicts in timetable {self.timetable.name}")
        return conflicts
    
    def detect_room_conflicts(self):
        """
        Detect room conflicts (same room, same time).
        """
        conflicts = []
        room_time_map = {}
        
        for entry in self.entries:
            key = (entry.room.id, entry.time_slot.id)
            
            if key in room_time_map:
                # Conflict detected
                existing_entry = room_time_map[key]
                
                conflict_type, _ = ConflictType.objects.get_or_create(
                    name='Room Conflict',
                    defaults={
                        'description': 'Multiple classes scheduled in the same room at the same time',
                        'severity': 'critical',
                        'is_hard_constraint': True
                    }
                )
                
                conflict = Conflict.objects.create(
                    timetable=self.timetable,
                    conflict_type=conflict_type,
                    title=f'Room conflict in {entry.room.code}',
                    description=f'Room {entry.room.code} is double-booked at {entry.time_slot}',
                    severity='critical'
                )
                
                conflict.affected_entries.add(entry, existing_entry)
                conflict.affected_rooms.add(entry.room)
                conflict.affected_time_slots.add(entry.time_slot)
                
                conflicts.append(conflict)
            else:
                room_time_map[key] = entry
        
        return conflicts
    
    def detect_lecturer_conflicts(self):
        """
        Detect lecturer conflicts (same lecturer, same time).
        """
        conflicts = []
        lecturer_time_map = {}
        
        for entry in self.entries:
            key = (entry.lecturer.id, entry.time_slot.id)
            
            if key in lecturer_time_map:
                # Conflict detected
                existing_entry = lecturer_time_map[key]
                
                conflict_type, _ = ConflictType.objects.get_or_create(
                    name='Lecturer Conflict',
                    defaults={
                        'description': 'Lecturer scheduled for multiple classes at the same time',
                        'severity': 'critical',
                        'is_hard_constraint': True
                    }
                )
                
                conflict = Conflict.objects.create(
                    timetable=self.timetable,
                    conflict_type=conflict_type,
                    title=f'Lecturer conflict for {entry.lecturer.full_name}',
                    description=f'{entry.lecturer.full_name} is scheduled for multiple classes at {entry.time_slot}',
                    severity='critical'
                )
                
                conflict.affected_entries.add(entry, existing_entry)
                conflict.affected_lecturers.add(entry.lecturer)
                conflict.affected_time_slots.add(entry.time_slot)
                
                conflicts.append(conflict)
            else:
                lecturer_time_map[key] = entry
        
        return conflicts
    
    def detect_capacity_conflicts(self):
        """
        Detect room capacity conflicts.
        """
        conflicts = []
        
        for entry in self.entries:
            if entry.course.max_students > entry.room.capacity:
                conflict_type, _ = ConflictType.objects.get_or_create(
                    name='Capacity Conflict',
                    defaults={
                        'description': 'Course enrollment exceeds room capacity',
                        'severity': 'high',
                        'is_hard_constraint': True
                    }
                )
                
                conflict = Conflict.objects.create(
                    timetable=self.timetable,
                    conflict_type=conflict_type,
                    title=f'Capacity exceeded for {entry.course.code}',
                    description=f'Course {entry.course.code} has {entry.course.max_students} students but room {entry.room.code} only has capacity for {entry.room.capacity}',
                    severity='high'
                )
                
                conflict.affected_entries.add(entry)
                conflict.affected_courses.add(entry.course)
                conflict.affected_rooms.add(entry.room)
                
                conflicts.append(conflict)
        
        return conflicts
    
    def detect_compatibility_conflicts(self):
        """
        Detect course-room type compatibility conflicts.
        """
        conflicts = []
        
        # Define compatibility rules
        compatibility_rules = {
            'lab': ['laboratory', 'computer_lab'],
            'lecture': ['classroom', 'auditorium', 'seminar_room'],
            'seminar': ['seminar_room', 'classroom'],
            'project': ['laboratory', 'computer_lab', 'classroom']
        }
        
        for entry in self.entries:
            course_type = entry.course.course_type
            room_type = entry.room.room_type
            
            if course_type in compatibility_rules:
                compatible_rooms = compatibility_rules[course_type]
                
                if room_type not in compatible_rooms:
                    conflict_type, _ = ConflictType.objects.get_or_create(
                        name='Compatibility Conflict',
                        defaults={
                            'description': 'Course type is not compatible with room type',
                            'severity': 'medium',
                            'is_hard_constraint': False
                        }
                    )
                    
                    conflict = Conflict.objects.create(
                        timetable=self.timetable,
                        conflict_type=conflict_type,
                        title=f'Incompatible room for {entry.course.code}',
                        description=f'{entry.course.course_type.title()} course {entry.course.code} is scheduled in {entry.room.room_type} room {entry.room.code}',
                        severity='medium'
                    )
                    
                    conflict.affected_entries.add(entry)
                    conflict.affected_courses.add(entry.course)
                    conflict.affected_rooms.add(entry.room)
                    
                    conflicts.append(conflict)
        
        return conflicts
    
    def detect_preference_conflicts(self):
        """
        Detect conflicts with lecturer and student preferences.
        This would integrate with the preference service.
        """
        conflicts = []
        
        # This is a placeholder for preference-based conflict detection
        # In a real implementation, this would:
        # 1. Fetch lecturer preferences from preference service
        # 2. Check if scheduled times conflict with preferences
        # 3. Create soft conflicts for preference violations
        
        return conflicts
    
    def detect_workload_conflicts(self):
        """
        Detect lecturer workload distribution conflicts.
        """
        conflicts = []
        
        # Calculate lecturer workloads
        lecturer_workloads = {}
        for entry in self.entries:
            lecturer_id = entry.lecturer.id
            if lecturer_id not in lecturer_workloads:
                lecturer_workloads[lecturer_id] = {
                    'lecturer': entry.lecturer,
                    'total_hours': 0,
                    'courses': set()
                }
            
            lecturer_workloads[lecturer_id]['total_hours'] += entry.course.duration_hours
            lecturer_workloads[lecturer_id]['courses'].add(entry.course)
        
        # Check for excessive workloads (example: more than 20 hours per week)
        for lecturer_id, workload in lecturer_workloads.items():
            if workload['total_hours'] > 20:
                conflict_type, _ = ConflictType.objects.get_or_create(
                    name='Workload Conflict',
                    defaults={
                        'description': 'Lecturer has excessive teaching workload',
                        'severity': 'medium',
                        'is_hard_constraint': False
                    }
                )
                
                conflict = Conflict.objects.create(
                    timetable=self.timetable,
                    conflict_type=conflict_type,
                    title=f'Excessive workload for {workload["lecturer"].full_name}',
                    description=f'{workload["lecturer"].full_name} is scheduled for {workload["total_hours"]} hours per week',
                    severity='medium'
                )
                
                conflict.affected_lecturers.add(workload['lecturer'])
                
                # Add affected entries
                lecturer_entries = self.entries.filter(lecturer_id=lecturer_id)
                conflict.affected_entries.add(*lecturer_entries)
                
                conflicts.append(conflict)
        
        return conflicts
