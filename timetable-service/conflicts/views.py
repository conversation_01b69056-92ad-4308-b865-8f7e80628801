"""
Views for conflict detection and management.
"""
from rest_framework import viewsets, status
from rest_framework.decorators import api_view, permission_classes, action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import <PERSON>Filter, OrderingFilter
from .models import ConflictType, Conflict, ConflictResolution, ConflictRule
from .serializers import (
    ConflictTypeSerializer, ConflictSerializer, ConflictResolutionSerializer, ConflictRuleSerializer
)
from timetables.models import Timetable
import logging

logger = logging.getLogger(__name__)


class ConflictTypeViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing conflict types.
    """
    queryset = ConflictType.objects.all()
    serializer_class = ConflictTypeSerializer
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['severity', 'is_hard_constraint', 'auto_resolve']
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'severity', 'resolution_priority']
    ordering = ['-severity', 'name']


class ConflictViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing conflicts.
    """
    queryset = Conflict.objects.all()
    serializer_class = ConflictSerializer
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['status', 'severity', 'conflict_type', 'timetable']
    search_fields = ['title', 'description']
    ordering_fields = ['created_at', 'severity', 'status']
    ordering = ['-severity', '-created_at']
    
    @action(detail=True, methods=['post'])
    def resolve(self, request, pk=None):
        """Resolve a conflict."""
        conflict = self.get_object()
        notes = request.data.get('notes', '')
        
        conflict.resolve(request.user, notes)
        
        return Response({
            'message': 'Conflict resolved successfully',
            'resolved_at': conflict.resolved_at
        })
    
    @action(detail=True, methods=['post'])
    def acknowledge(self, request, pk=None):
        """Acknowledge a conflict."""
        conflict = self.get_object()
        conflict.acknowledge(request.user)
        
        return Response({'message': 'Conflict acknowledged successfully'})
    
    @action(detail=True, methods=['post'])
    def ignore(self, request, pk=None):
        """Ignore a conflict."""
        conflict = self.get_object()
        reason = request.data.get('reason', '')
        
        conflict.ignore(request.user, reason)
        
        return Response({'message': 'Conflict ignored successfully'})


class ConflictResolutionViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing conflict resolutions.
    """
    queryset = ConflictResolution.objects.all()
    serializer_class = ConflictResolutionSerializer
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['resolution_type', 'is_applied', 'conflict']
    search_fields = ['title', 'description']
    ordering_fields = ['impact_score', 'created_at']
    ordering = ['impact_score', '-created_at']
    
    @action(detail=True, methods=['post'])
    def apply(self, request, pk=None):
        """Apply a conflict resolution."""
        resolution = self.get_object()
        
        if resolution.is_applied:
            return Response(
                {'error': 'Resolution has already been applied'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        resolution.apply(request.user)
        
        return Response({
            'message': 'Resolution applied successfully',
            'applied_at': resolution.applied_at
        })


class ConflictRuleViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing conflict detection rules.
    """
    queryset = ConflictRule.objects.all()
    serializer_class = ConflictRuleSerializer
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['conflict_type', 'is_enabled', 'run_on_generation', 'run_on_modification']
    search_fields = ['name', 'description']
    ordering_fields = ['execution_order', 'name']
    ordering = ['execution_order', 'name']


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def detect_conflicts(request, timetable_id):
    """
    Detect conflicts in a specific timetable.
    """
    try:
        timetable = Timetable.objects.get(id=timetable_id)
    except Timetable.DoesNotExist:
        return Response(
            {'error': 'Timetable not found'},
            status=status.HTTP_404_NOT_FOUND
        )
    
    # Import conflict detection logic
    from .detector import ConflictDetector
    
    detector = ConflictDetector(timetable)
    conflicts = detector.detect_all_conflicts()
    
    return Response({
        'timetable_id': str(timetable_id),
        'conflicts_detected': len(conflicts),
        'conflicts': [
            {
                'id': str(conflict.id),
                'title': conflict.title,
                'severity': conflict.severity,
                'type': conflict.conflict_type.name
            }
            for conflict in conflicts
        ]
    })


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def analyze_conflicts(request, timetable_id):
    """
    Analyze conflicts in a timetable and provide statistics.
    """
    try:
        timetable = Timetable.objects.get(id=timetable_id)
    except Timetable.DoesNotExist:
        return Response(
            {'error': 'Timetable not found'},
            status=status.HTTP_404_NOT_FOUND
        )
    
    conflicts = Conflict.objects.filter(timetable=timetable)
    
    # Analyze conflicts
    analysis = {
        'total_conflicts': conflicts.count(),
        'by_severity': {
            'critical': conflicts.filter(severity='critical').count(),
            'high': conflicts.filter(severity='high').count(),
            'medium': conflicts.filter(severity='medium').count(),
            'low': conflicts.filter(severity='low').count(),
        },
        'by_status': {
            'detected': conflicts.filter(status='detected').count(),
            'acknowledged': conflicts.filter(status='acknowledged').count(),
            'resolved': conflicts.filter(status='resolved').count(),
            'ignored': conflicts.filter(status='ignored').count(),
        },
        'by_type': {}
    }
    
    # Group by conflict type
    for conflict_type in ConflictType.objects.all():
        count = conflicts.filter(conflict_type=conflict_type).count()
        if count > 0:
            analysis['by_type'][conflict_type.name] = count
    
    return Response(analysis)
