"""
Models for conflict detection and resolution.
"""
from django.db import models
from core.models import BaseModel, User, Course, Room, TimeSlot
from timetables.models import Timetable, TimetableEntry


class ConflictType(BaseModel):
    """
    Model for different types of conflicts.
    """
    SEVERITY_CHOICES = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical'),
    ]
    
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField()
    severity = models.CharField(max_length=10, choices=SEVERITY_CHOICES, default='medium')
    is_hard_constraint = models.BooleanField(default=True)
    
    # Resolution settings
    auto_resolve = models.BooleanField(default=False)
    resolution_priority = models.IntegerField(default=0)
    
    class Meta:
        db_table = 'conflicts_type'
        ordering = ['-severity', 'name']
    
    def __str__(self):
        return f"{self.name} ({self.severity})"


class Conflict(BaseModel):
    """
    Model for detected conflicts in timetables.
    """
    STATUS_CHOICES = [
        ('detected', 'Detected'),
        ('acknowledged', 'Acknowledged'),
        ('resolved', 'Resolved'),
        ('ignored', 'Ignored'),
    ]
    
    timetable = models.ForeignKey(Timetable, on_delete=models.CASCADE, related_name='conflicts')
    conflict_type = models.ForeignKey(ConflictType, on_delete=models.CASCADE)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='detected')
    
    # Conflict details
    title = models.CharField(max_length=200)
    description = models.TextField()
    severity = models.CharField(max_length=10, choices=ConflictType.SEVERITY_CHOICES)
    
    # Affected entities
    affected_entries = models.ManyToManyField(TimetableEntry, blank=True)
    affected_courses = models.ManyToManyField(Course, blank=True)
    affected_lecturers = models.ManyToManyField(User, blank=True)
    affected_rooms = models.ManyToManyField(Room, blank=True)
    affected_time_slots = models.ManyToManyField(TimeSlot, blank=True)
    
    # Resolution tracking
    detected_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='detected_conflicts')
    resolved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='resolved_conflicts')
    resolved_at = models.DateTimeField(null=True, blank=True)
    resolution_notes = models.TextField(blank=True)
    
    # Metadata
    detection_data = models.JSONField(default=dict, blank=True)
    resolution_data = models.JSONField(default=dict, blank=True)
    
    class Meta:
        db_table = 'conflicts_conflict'
        ordering = ['-severity', '-created_at']
    
    def __str__(self):
        return f"{self.title} - {self.timetable.name} ({self.status})"
    
    def resolve(self, user, notes=""):
        """Mark conflict as resolved."""
        from django.utils import timezone
        
        self.status = 'resolved'
        self.resolved_by = user
        self.resolved_at = timezone.now()
        self.resolution_notes = notes
        self.save()
    
    def acknowledge(self, user):
        """Acknowledge the conflict."""
        self.status = 'acknowledged'
        self.save()
    
    def ignore(self, user, reason=""):
        """Ignore the conflict."""
        self.status = 'ignored'
        self.resolution_notes = reason
        self.save()


class ConflictResolution(BaseModel):
    """
    Model for conflict resolution suggestions and actions.
    """
    RESOLUTION_TYPE_CHOICES = [
        ('move_time', 'Move Time Slot'),
        ('change_room', 'Change Room'),
        ('change_lecturer', 'Change Lecturer'),
        ('split_class', 'Split Class'),
        ('merge_classes', 'Merge Classes'),
        ('cancel_class', 'Cancel Class'),
        ('custom', 'Custom Resolution'),
    ]
    
    conflict = models.ForeignKey(Conflict, on_delete=models.CASCADE, related_name='resolutions')
    resolution_type = models.CharField(max_length=20, choices=RESOLUTION_TYPE_CHOICES)
    
    # Resolution details
    title = models.CharField(max_length=200)
    description = models.TextField()
    impact_score = models.FloatField(default=0.0, help_text="Impact score of this resolution")
    
    # Resolution parameters
    parameters = models.JSONField(default=dict, blank=True)
    
    # Execution tracking
    is_applied = models.BooleanField(default=False)
    applied_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    applied_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        db_table = 'conflicts_resolution'
        ordering = ['impact_score', '-created_at']
    
    def __str__(self):
        return f"{self.title} for {self.conflict.title}"
    
    def apply(self, user):
        """Apply this resolution."""
        from django.utils import timezone
        
        self.is_applied = True
        self.applied_by = user
        self.applied_at = timezone.now()
        self.save()
        
        # Mark conflict as resolved
        self.conflict.resolve(user, f"Applied resolution: {self.title}")


class ConflictRule(BaseModel):
    """
    Model for defining conflict detection rules.
    """
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField()
    conflict_type = models.ForeignKey(ConflictType, on_delete=models.CASCADE)
    
    # Rule configuration
    is_enabled = models.BooleanField(default=True)
    rule_logic = models.TextField(help_text="Python code for conflict detection logic")
    parameters = models.JSONField(default=dict, blank=True)
    
    # Execution settings
    execution_order = models.IntegerField(default=0)
    run_on_generation = models.BooleanField(default=True)
    run_on_modification = models.BooleanField(default=True)
    
    class Meta:
        db_table = 'conflicts_rule'
        ordering = ['execution_order', 'name']
    
    def __str__(self):
        return f"{self.name} ({self.conflict_type.name})"
