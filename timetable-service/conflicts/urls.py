"""
URLs configuration for conflicts app.
"""
from django.urls import path, include
from rest_framework.routers import Default<PERSON><PERSON><PERSON>
from . import views

app_name = 'conflicts'

# Create router for viewsets
router = DefaultRouter()
router.register(r'types', views.ConflictTypeViewSet, basename='conflict-type')
router.register(r'', views.ConflictViewSet, basename='conflict')
router.register(r'resolutions', views.ConflictResolutionViewSet, basename='conflict-resolution')
router.register(r'rules', views.ConflictRuleViewSet, basename='conflict-rule')

urlpatterns = [
    # ViewSet URLs
    path('', include(router.urls)),
    
    # Additional endpoints
    path('detect/<uuid:timetable_id>/', views.detect_conflicts, name='detect-conflicts'),
    path('analyze/<uuid:timetable_id>/', views.analyze_conflicts, name='analyze-conflicts'),
]
