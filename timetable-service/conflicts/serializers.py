"""
Serializers for conflict models.
"""
from rest_framework import serializers
from .models import ConflictType, Conflict, ConflictResolution, ConflictRule
from core.serializers import UserSerializer


class ConflictTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = ConflictType
        fields = [
            'id', 'name', 'description', 'severity', 'is_hard_constraint',
            'auto_resolve', 'resolution_priority', 'is_active',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class ConflictSerializer(serializers.ModelSerializer):
    conflict_type_name = serializers.CharField(source='conflict_type.name', read_only=True)
    timetable_name = serializers.CharField(source='timetable.name', read_only=True)
    detected_by_name = serializers.CharField(source='detected_by.full_name', read_only=True)
    resolved_by_name = serializers.CharField(source='resolved_by.full_name', read_only=True)
    
    class Meta:
        model = Conflict
        fields = [
            'id', 'timetable', 'timetable_name', 'conflict_type', 'conflict_type_name',
            'status', 'title', 'description', 'severity', 'affected_entries',
            'affected_courses', 'affected_lecturers', 'affected_rooms', 'affected_time_slots',
            'detected_by', 'detected_by_name', 'resolved_by', 'resolved_by_name',
            'resolved_at', 'resolution_notes', 'detection_data', 'resolution_data',
            'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'detected_by', 'resolved_by', 'resolved_at',
            'created_at', 'updated_at'
        ]


class ConflictResolutionSerializer(serializers.ModelSerializer):
    conflict_title = serializers.CharField(source='conflict.title', read_only=True)
    applied_by_name = serializers.CharField(source='applied_by.full_name', read_only=True)
    
    class Meta:
        model = ConflictResolution
        fields = [
            'id', 'conflict', 'conflict_title', 'resolution_type', 'title',
            'description', 'impact_score', 'parameters', 'is_applied',
            'applied_by', 'applied_by_name', 'applied_at', 'is_active',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'applied_by', 'applied_at', 'created_at', 'updated_at'
        ]


class ConflictRuleSerializer(serializers.ModelSerializer):
    conflict_type_name = serializers.CharField(source='conflict_type.name', read_only=True)
    
    class Meta:
        model = ConflictRule
        fields = [
            'id', 'name', 'description', 'conflict_type', 'conflict_type_name',
            'is_enabled', 'rule_logic', 'parameters', 'execution_order',
            'run_on_generation', 'run_on_modification', 'is_active',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
