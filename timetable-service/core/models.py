"""
Core models for the timetable service.
"""
from django.db import models
from django.contrib.auth.models import AbstractBaseUser, PermissionsMixin
from django.core.validators import EmailValidator, MinValueValidator, MaxValueValidator
from django.utils import timezone
import uuid


class BaseModel(models.Model):
    """
    Abstract base model with common fields for all models.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        abstract = True


class User(AbstractBaseUser, PermissionsMixin, BaseModel):
    """
    User model proxy for timetable service (synced from user management service).
    """
    email = models.EmailField(
        unique=True,
        validators=[EmailValidator()],
        help_text="User's email address"
    )
    username = models.CharField(
        max_length=150,
        unique=True,
        help_text="Unique username for the user"
    )
    first_name = models.CharField(max_length=150, blank=True)
    last_name = models.CharField(max_length=150, blank=True)
    is_staff = models.BooleanField(default=False)
    is_superuser = models.BooleanField(default=False)
    date_joined = models.DateTimeField(default=timezone.now)
    last_login = models.DateTimeField(blank=True, null=True)
    
    # Role information
    role = models.CharField(
        max_length=50,
        choices=[
            ('superadmin', 'Super Admin'),
            ('admin', 'Admin'),
            ('lecturer', 'Lecturer'),
            ('student', 'Student'),
        ],
        default='student'
    )
    
    # Additional user information
    phone_number = models.CharField(max_length=20, blank=True)
    department = models.CharField(max_length=100, blank=True)
    faculty = models.CharField(max_length=100, blank=True)
    
    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username']

    class Meta:
        db_table = 'core_user'
        verbose_name = 'User'
        verbose_name_plural = 'Users'

    def __str__(self):
        return f"{self.email} ({self.role})"

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}".strip()


class Department(BaseModel):
    """
    Academic department model.
    """
    name = models.CharField(max_length=200, unique=True)
    code = models.CharField(max_length=10, unique=True)
    faculty = models.CharField(max_length=200)
    head_of_department = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='headed_departments'
    )
    description = models.TextField(blank=True)

    class Meta:
        db_table = 'core_department'
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.code})"


class Course(BaseModel):
    """
    Course model.
    """
    COURSE_TYPE_CHOICES = [
        ('lecture', 'Lecture'),
        ('lab', 'Laboratory'),
        ('tutorial', 'Tutorial'),
        ('seminar', 'Seminar'),
        ('project', 'Project'),
    ]
    
    name = models.CharField(max_length=200)
    code = models.CharField(max_length=20, unique=True)
    credits = models.PositiveIntegerField(validators=[MinValueValidator(1), MaxValueValidator(10)])
    course_type = models.CharField(max_length=20, choices=COURSE_TYPE_CHOICES, default='lecture')
    department = models.ForeignKey(Department, on_delete=models.CASCADE, related_name='courses')
    
    # Course scheduling requirements
    duration_hours = models.PositiveIntegerField(
        default=1,
        validators=[MinValueValidator(1), MaxValueValidator(8)],
        help_text="Duration of each class session in hours"
    )
    sessions_per_week = models.PositiveIntegerField(
        default=1,
        validators=[MinValueValidator(1), MaxValueValidator(7)],
        help_text="Number of sessions per week"
    )
    
    # Course details
    description = models.TextField(blank=True)
    prerequisites = models.ManyToManyField('self', blank=True, symmetrical=False)
    max_students = models.PositiveIntegerField(default=50)
    
    # Academic year and semester
    academic_year = models.CharField(max_length=9, help_text="e.g., 2023-2024")
    semester = models.CharField(
        max_length=20,
        choices=[
            ('fall', 'Fall'),
            ('spring', 'Spring'),
            ('summer', 'Summer'),
        ]
    )

    class Meta:
        db_table = 'core_course'
        ordering = ['code']
        unique_together = ['code', 'academic_year', 'semester']

    def __str__(self):
        return f"{self.code} - {self.name}"


class Room(BaseModel):
    """
    Room/Venue model for classes.
    """
    ROOM_TYPE_CHOICES = [
        ('classroom', 'Classroom'),
        ('laboratory', 'Laboratory'),
        ('auditorium', 'Auditorium'),
        ('seminar_room', 'Seminar Room'),
        ('computer_lab', 'Computer Lab'),
        ('library', 'Library'),
        ('outdoor', 'Outdoor'),
    ]
    
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=20, unique=True)
    room_type = models.CharField(max_length=20, choices=ROOM_TYPE_CHOICES)
    capacity = models.PositiveIntegerField()
    building = models.CharField(max_length=100)
    floor = models.CharField(max_length=10, blank=True)
    
    # Room features
    has_projector = models.BooleanField(default=False)
    has_computer = models.BooleanField(default=False)
    has_whiteboard = models.BooleanField(default=True)
    has_audio_system = models.BooleanField(default=False)
    is_air_conditioned = models.BooleanField(default=False)
    
    # Availability
    is_available = models.BooleanField(default=True)
    notes = models.TextField(blank=True)

    class Meta:
        db_table = 'core_room'
        ordering = ['building', 'name']

    def __str__(self):
        return f"{self.code} - {self.name} ({self.building})"


class TimeSlot(BaseModel):
    """
    Time slot model for scheduling.
    """
    WEEKDAY_CHOICES = [
        (0, 'Monday'),
        (1, 'Tuesday'),
        (2, 'Wednesday'),
        (3, 'Thursday'),
        (4, 'Friday'),
        (5, 'Saturday'),
        (6, 'Sunday'),
    ]
    
    day_of_week = models.IntegerField(choices=WEEKDAY_CHOICES)
    start_time = models.TimeField()
    end_time = models.TimeField()
    
    # Slot metadata
    name = models.CharField(max_length=100, blank=True, help_text="e.g., 'Morning Slot 1'")
    is_break_time = models.BooleanField(default=False)
    is_lunch_time = models.BooleanField(default=False)
    
    class Meta:
        db_table = 'core_timeslot'
        ordering = ['day_of_week', 'start_time']
        unique_together = ['day_of_week', 'start_time', 'end_time']

    def __str__(self):
        day_name = dict(self.WEEKDAY_CHOICES)[self.day_of_week]
        return f"{day_name} {self.start_time.strftime('%H:%M')} - {self.end_time.strftime('%H:%M')}"

    @property
    def duration_minutes(self):
        """Calculate duration in minutes."""
        from datetime import datetime, timedelta
        start = datetime.combine(datetime.today(), self.start_time)
        end = datetime.combine(datetime.today(), self.end_time)
        if end < start:  # Handle overnight slots
            end += timedelta(days=1)
        return int((end - start).total_seconds() / 60)


class AcademicYear(BaseModel):
    """
    Academic year model.
    """
    name = models.CharField(max_length=9, unique=True, help_text="e.g., 2023-2024")
    start_date = models.DateField()
    end_date = models.DateField()
    is_current = models.BooleanField(default=False)
    
    class Meta:
        db_table = 'core_academic_year'
        ordering = ['-start_date']

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if self.is_current:
            # Ensure only one academic year is current
            AcademicYear.objects.filter(is_current=True).update(is_current=False)
        super().save(*args, **kwargs)
