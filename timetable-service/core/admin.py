"""
Admin configuration for core models.
"""
from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from .models import User, Department, Course, Room, TimeSlot, AcademicYear


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    list_display = ('email', 'username', 'first_name', 'last_name', 'role', 'is_active', 'date_joined')
    list_filter = ('role', 'is_active', 'is_staff', 'date_joined')
    search_fields = ('email', 'username', 'first_name', 'last_name')
    ordering = ('email',)
    
    fieldsets = (
        (None, {'fields': ('email', 'username', 'password')}),
        ('Personal info', {'fields': ('first_name', 'last_name', 'phone_number')}),
        ('Academic info', {'fields': ('role', 'department', 'faculty')}),
        ('Permissions', {'fields': ('is_active', 'is_staff', 'is_superuser', 'groups', 'user_permissions')}),
        ('Important dates', {'fields': ('last_login', 'date_joined')}),
    )
    
    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('email', 'username', 'password1', 'password2', 'role'),
        }),
    )


@admin.register(Department)
class DepartmentAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'faculty', 'head_of_department', 'is_active')
    list_filter = ('faculty', 'is_active')
    search_fields = ('name', 'code', 'faculty')
    ordering = ('name',)


@admin.register(Course)
class CourseAdmin(admin.ModelAdmin):
    list_display = ('code', 'name', 'course_type', 'credits', 'department', 'academic_year', 'semester')
    list_filter = ('course_type', 'department', 'academic_year', 'semester', 'is_active')
    search_fields = ('code', 'name', 'description')
    ordering = ('code',)
    filter_horizontal = ('prerequisites',)


@admin.register(Room)
class RoomAdmin(admin.ModelAdmin):
    list_display = ('code', 'name', 'room_type', 'capacity', 'building', 'is_available')
    list_filter = ('room_type', 'building', 'is_available', 'has_projector', 'has_computer')
    search_fields = ('code', 'name', 'building')
    ordering = ('building', 'name')


@admin.register(TimeSlot)
class TimeSlotAdmin(admin.ModelAdmin):
    list_display = ('__str__', 'name', 'duration_minutes', 'is_break_time', 'is_lunch_time')
    list_filter = ('day_of_week', 'is_break_time', 'is_lunch_time')
    ordering = ('day_of_week', 'start_time')


@admin.register(AcademicYear)
class AcademicYearAdmin(admin.ModelAdmin):
    list_display = ('name', 'start_date', 'end_date', 'is_current', 'is_active')
    list_filter = ('is_current', 'is_active')
    ordering = ('-start_date',)
