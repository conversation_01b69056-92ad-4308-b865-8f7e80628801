"""
Serializers for core models.
"""
from rest_framework import serializers
from .models import User, Department, Course, Room, TimeSlot, AcademicYear


class UserSerializer(serializers.ModelSerializer):
    full_name = serializers.ReadOnlyField()
    
    class Meta:
        model = User
        fields = [
            'id', 'email', 'username', 'first_name', 'last_name', 'full_name',
            'role', 'phone_number', 'department', 'faculty', 'is_active',
            'date_joined', 'last_login'
        ]
        read_only_fields = ['id', 'date_joined', 'last_login']


class DepartmentSerializer(serializers.ModelSerializer):
    head_of_department_name = serializers.CharField(source='head_of_department.full_name', read_only=True)
    
    class Meta:
        model = Department
        fields = [
            'id', 'name', 'code', 'faculty', 'head_of_department', 
            'head_of_department_name', 'description', 'is_active',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class CourseSerializer(serializers.ModelSerializer):
    department_name = serializers.CharField(source='department.name', read_only=True)
    prerequisites_details = serializers.SerializerMethodField()
    
    class Meta:
        model = Course
        fields = [
            'id', 'name', 'code', 'credits', 'course_type', 'department',
            'department_name', 'duration_hours', 'sessions_per_week',
            'description', 'prerequisites', 'prerequisites_details',
            'max_students', 'academic_year', 'semester', 'is_active',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_prerequisites_details(self, obj):
        return [{'id': prereq.id, 'code': prereq.code, 'name': prereq.name} 
                for prereq in obj.prerequisites.all()]


class RoomSerializer(serializers.ModelSerializer):
    features = serializers.SerializerMethodField()
    
    class Meta:
        model = Room
        fields = [
            'id', 'name', 'code', 'room_type', 'capacity', 'building', 'floor',
            'has_projector', 'has_computer', 'has_whiteboard', 'has_audio_system',
            'is_air_conditioned', 'is_available', 'features', 'notes',
            'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_features(self, obj):
        features = []
        if obj.has_projector:
            features.append('Projector')
        if obj.has_computer:
            features.append('Computer')
        if obj.has_whiteboard:
            features.append('Whiteboard')
        if obj.has_audio_system:
            features.append('Audio System')
        if obj.is_air_conditioned:
            features.append('Air Conditioned')
        return features


class TimeSlotSerializer(serializers.ModelSerializer):
    day_name = serializers.CharField(source='get_day_of_week_display', read_only=True)
    duration_minutes = serializers.ReadOnlyField()
    
    class Meta:
        model = TimeSlot
        fields = [
            'id', 'day_of_week', 'day_name', 'start_time', 'end_time',
            'duration_minutes', 'name', 'is_break_time', 'is_lunch_time',
            'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class AcademicYearSerializer(serializers.ModelSerializer):
    class Meta:
        model = AcademicYear
        fields = [
            'id', 'name', 'start_date', 'end_date', 'is_current',
            'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
