"""
Core views for health checks and basic functionality.
"""
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status
from django.conf import settings
from django.db import connection
from django.core.cache import cache
from django.utils import timezone
import redis
import logging

logger = logging.getLogger(__name__)


@api_view(['GET'])
@permission_classes([AllowAny])
def health_check(request):
    """
    Health check endpoint for monitoring service status.
    """
    health_status = {
        'status': 'healthy',
        'service': 'timetable-service',
        'version': '1.0.0',
        'timestamp': timezone.now().isoformat(),
        'checks': {}
    }
    
    # Check database connection
    try:
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
        health_status['checks']['database'] = 'healthy'
    except Exception as e:
        health_status['checks']['database'] = f'unhealthy: {str(e)}'
        health_status['status'] = 'unhealthy'
        logger.error(f"Database health check failed: {e}")
    
    # Check Redis connection
    try:
        r = redis.from_url(settings.REDIS_URL)
        r.ping()
        health_status['checks']['redis'] = 'healthy'
    except Exception as e:
        health_status['checks']['redis'] = f'unhealthy: {str(e)}'
        health_status['status'] = 'unhealthy'
        logger.error(f"Redis health check failed: {e}")
    
    # Check Celery
    try:
        from celery import current_app
        inspect = current_app.control.inspect()
        stats = inspect.stats()
        if stats:
            health_status['checks']['celery'] = 'healthy'
        else:
            health_status['checks']['celery'] = 'no workers available'
    except Exception as e:
        health_status['checks']['celery'] = f'unhealthy: {str(e)}'
        logger.error(f"Celery health check failed: {e}")
    
    response_status = status.HTTP_200_OK if health_status['status'] == 'healthy' else status.HTTP_503_SERVICE_UNAVAILABLE
    return Response(health_status, status=response_status)


@api_view(['GET'])
@permission_classes([AllowAny])
def service_info(request):
    """
    Service information endpoint.
    """
    from django.utils import timezone
    
    return Response({
        'service': 'Timetable Generation Service',
        'version': '1.0.0',
        'description': 'Microservice for generating academic timetables using genetic algorithms',
        'features': [
            'Genetic Algorithm-based timetable generation',
            'Conflict detection and resolution',
            'Multiple export formats (PDF, Excel, iCal)',
            'Real-time generation progress tracking',
            'Manual timetable adjustments',
            'Integration with preference collection service'
        ],
        'endpoints': {
            'health': '/api/health/',
            'docs': '/api/docs/',
            'timetables': '/api/timetables/',
            'algorithms': '/api/algorithms/',
            'conflicts': '/api/conflicts/',
            'exports': '/api/exports/'
        },
        'timestamp': timezone.now().isoformat()
    })


@api_view(['GET'])
def service_stats(request):
    """
    Service statistics endpoint.
    """
    from timetables.models import Timetable, GenerationTask
    from django.utils import timezone
    from datetime import timedelta
    
    # Calculate statistics
    now = timezone.now()
    last_24h = now - timedelta(hours=24)
    last_7d = now - timedelta(days=7)
    
    stats = {
        'timetables': {
            'total': Timetable.objects.count(),
            'active': Timetable.objects.filter(is_active=True).count(),
            'last_24h': Timetable.objects.filter(created_at__gte=last_24h).count(),
            'last_7d': Timetable.objects.filter(created_at__gte=last_7d).count(),
        },
        'generation_tasks': {
            'total': GenerationTask.objects.count(),
            'pending': GenerationTask.objects.filter(status='pending').count(),
            'running': GenerationTask.objects.filter(status='running').count(),
            'completed': GenerationTask.objects.filter(status='completed').count(),
            'failed': GenerationTask.objects.filter(status='failed').count(),
            'last_24h': GenerationTask.objects.filter(created_at__gte=last_24h).count(),
        },
        'timestamp': now.isoformat()
    }
    
    return Response(stats)
