"""
Views for algorithm management and testing.
"""
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from django.conf import settings
from .genetic_algorithm import TimetableGeneticAlgorithm
from timetables.models import Timetable
import logging

logger = logging.getLogger(__name__)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def genetic_algorithm_info(request):
    """
    Get information about the genetic algorithm implementation.
    """
    info = {
        'algorithm_name': 'Genetic Algorithm for Timetable Generation',
        'library': 'DEAP (Distributed Evolutionary Algorithms in Python)',
        'version': '1.0.0',
        'description': 'Optimizes timetable generation using evolutionary computation',
        'features': [
            'Multi-objective optimization',
            'Constraint satisfaction',
            'Preference integration',
            'Conflict minimization',
            'Workload balancing'
        ],
        'default_parameters': {
            'population_size': settings.GENETIC_ALGORITHM_POPULATION_SIZE,
            'generations': settings.GENETIC_ALGORITHM_GENERATIONS,
            'mutation_rate': settings.GENETIC_ALGORITHM_MUTATION_RATE,
            'crossover_rate': settings.GENETIC_ALGORITHM_CROSSOVER_RATE
        },
        'constraints': {
            'hard_constraints': [
                'No room conflicts (same room, same time)',
                'No lecturer conflicts (same lecturer, same time)',
                'Room capacity must accommodate course enrollment',
                'Course-room type compatibility'
            ],
            'soft_constraints': [
                'Lecturer time preferences',
                'Student time preferences',
                'Workload distribution',
                'Room utilization optimization',
                'Reasonable gaps between classes'
            ]
        },
        'fitness_components': [
            'Hard constraint violations (heavily penalized)',
            'Soft constraint satisfaction',
            'Preference satisfaction score',
            'Workload distribution score',
            'Room utilization efficiency'
        ]
    }
    
    return Response(info)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def test_genetic_algorithm(request):
    """
    Test the genetic algorithm with a small dataset.
    """
    try:
        # Get test parameters
        test_params = request.data.get('parameters', {})
        timetable_id = request.data.get('timetable_id')
        
        if not timetable_id:
            return Response(
                {'error': 'timetable_id is required for testing'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Get the timetable
        try:
            timetable = Timetable.objects.get(id=timetable_id)
        except Timetable.DoesNotExist:
            return Response(
                {'error': 'Timetable not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Set test parameters (smaller values for quick testing)
        test_parameters = {
            'population_size': test_params.get('population_size', 20),
            'generations': test_params.get('generations', 10),
            'mutation_rate': test_params.get('mutation_rate', 0.1),
            'crossover_rate': test_params.get('crossover_rate', 0.8)
        }
        
        # Initialize and run the genetic algorithm
        ga = TimetableGeneticAlgorithm(timetable, test_parameters)
        
        # Load data first to check if we have enough data
        ga.load_data()
        
        if not ga.courses:
            return Response(
                {'error': 'No courses found for the specified timetable'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        if not ga.rooms:
            return Response(
                {'error': 'No rooms available for scheduling'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        if not ga.time_slots:
            return Response(
                {'error': 'No time slots available for scheduling'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        if not ga.lecturers:
            return Response(
                {'error': 'No lecturers available for scheduling'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Run the algorithm
        result = ga.run()
        
        # Prepare response
        response_data = {
            'test_completed': True,
            'parameters_used': test_parameters,
            'data_summary': {
                'courses': len(ga.courses),
                'rooms': len(ga.rooms),
                'time_slots': len(ga.time_slots),
                'lecturers': len(ga.lecturers),
                'constraints': len(ga.constraints)
            },
            'results': {
                'best_fitness': result['best_fitness'],
                'population_size': len(result['population']),
                'generations_completed': test_parameters['generations']
            },
            'performance': {
                'algorithm_runtime': 'Available in production logs',
                'convergence_info': 'Check logbook for detailed convergence data'
            }
        }
        
        logger.info(f"Genetic algorithm test completed for timetable {timetable.name}")
        
        return Response(response_data)
        
    except Exception as e:
        logger.error(f"Genetic algorithm test failed: {e}")
        return Response(
            {'error': f'Test failed: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def algorithm_parameters(request):
    """
    Get available algorithm parameters and their valid ranges.
    """
    parameters = {
        'genetic_algorithm': {
            'population_size': {
                'description': 'Number of individuals in each generation',
                'type': 'integer',
                'min_value': 10,
                'max_value': 500,
                'default': settings.GENETIC_ALGORITHM_POPULATION_SIZE,
                'recommended_range': [50, 200],
                'impact': 'Higher values improve solution quality but increase computation time'
            },
            'generations': {
                'description': 'Number of generations to evolve',
                'type': 'integer',
                'min_value': 5,
                'max_value': 200,
                'default': settings.GENETIC_ALGORITHM_GENERATIONS,
                'recommended_range': [20, 100],
                'impact': 'More generations allow better convergence but take longer'
            },
            'mutation_rate': {
                'description': 'Probability of mutation for each gene',
                'type': 'float',
                'min_value': 0.01,
                'max_value': 0.5,
                'default': settings.GENETIC_ALGORITHM_MUTATION_RATE,
                'recommended_range': [0.05, 0.2],
                'impact': 'Higher rates increase exploration but may prevent convergence'
            },
            'crossover_rate': {
                'description': 'Probability of crossover between parents',
                'type': 'float',
                'min_value': 0.1,
                'max_value': 1.0,
                'default': settings.GENETIC_ALGORITHM_CROSSOVER_RATE,
                'recommended_range': [0.6, 0.9],
                'impact': 'Higher rates promote information exchange between solutions'
            }
        },
        'constraint_weights': {
            'hard_constraint_weight': {
                'description': 'Weight for hard constraint violations',
                'type': 'float',
                'min_value': 0.0,
                'max_value': 10.0,
                'default': 1000.0,
                'recommended_range': [500.0, 2000.0],
                'impact': 'Higher weights ensure hard constraints are prioritized'
            },
            'soft_constraint_weight': {
                'description': 'Weight for soft constraint satisfaction',
                'type': 'float',
                'min_value': 0.0,
                'max_value': 10.0,
                'default': 1.0,
                'recommended_range': [0.5, 2.0],
                'impact': 'Balances preference satisfaction with constraint compliance'
            }
        },
        'optimization_flags': {
            'use_preferences': {
                'description': 'Include lecturer and student preferences in optimization',
                'type': 'boolean',
                'default': True,
                'impact': 'Improves user satisfaction but may increase conflicts'
            },
            'optimize_workload': {
                'description': 'Optimize lecturer workload distribution',
                'type': 'boolean',
                'default': True,
                'impact': 'Ensures fair distribution of teaching load'
            },
            'optimize_room_usage': {
                'description': 'Optimize room utilization efficiency',
                'type': 'boolean',
                'default': True,
                'impact': 'Maximizes efficient use of available rooms'
            }
        }
    }
    
    return Response(parameters)
