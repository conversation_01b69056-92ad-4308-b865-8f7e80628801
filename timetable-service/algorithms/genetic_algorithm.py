"""
Genetic Algorithm implementation for timetable generation.
"""
import random
import numpy as np
from deap import base, creator, tools, algorithms
from django.conf import settings
from core.models import Course, Room, TimeSlot, User
from timetables.models import TimetableEntry, TimetableConstraint
import logging

logger = logging.getLogger(__name__)


class TimetableGeneticAlgorithm:
    """
    Genetic Algorithm for timetable generation using DEAP library.
    """
    
    def __init__(self, timetable, parameters=None):
        self.timetable = timetable
        self.parameters = parameters or {}
        
        # Algorithm parameters
        self.population_size = self.parameters.get('population_size', settings.GENETIC_ALGORITHM_POPULATION_SIZE)
        self.generations = self.parameters.get('generations', settings.GENETIC_ALGORITHM_GENERATIONS)
        self.mutation_rate = self.parameters.get('mutation_rate', settings.GENETIC_ALGORITHM_MUTATION_RATE)
        self.crossover_rate = self.parameters.get('crossover_rate', settings.GENETIC_ALGORITHM_CROSSOVER_RATE)
        
        # Initialize data
        self.courses = []
        self.rooms = []
        self.time_slots = []
        self.lecturers = []
        self.constraints = []
        
        # DEAP setup
        self.toolbox = base.Toolbox()
        self.setup_deap()
        
    def setup_deap(self):
        """Setup DEAP genetic algorithm components."""
        # Create fitness and individual classes
        creator.create("FitnessMax", base.Fitness, weights=(1.0,))
        creator.create("Individual", list, fitness=creator.FitnessMax)
        
        # Register genetic operators
        self.toolbox.register("individual", self.create_individual)
        self.toolbox.register("population", tools.initRepeat, list, self.toolbox.individual)
        self.toolbox.register("evaluate", self.evaluate_fitness)
        self.toolbox.register("mate", self.crossover)
        self.toolbox.register("mutate", self.mutate)
        self.toolbox.register("select", tools.selTournament, tournsize=3)
    
    def load_data(self):
        """Load courses, rooms, time slots, and lecturers for the timetable."""
        # Get courses for the academic year and semester
        self.courses = list(Course.objects.filter(
            academic_year=self.timetable.academic_year,
            semester=self.timetable.semester,
            is_active=True
        ))
        
        # Get available rooms
        self.rooms = list(Room.objects.filter(is_available=True, is_active=True))
        
        # Get available time slots
        self.time_slots = list(TimeSlot.objects.filter(
            is_active=True,
            is_break_time=False,
            is_lunch_time=False
        ))
        
        # Get lecturers
        self.lecturers = list(User.objects.filter(
            role='lecturer',
            is_active=True
        ))
        
        # Load constraints
        self.constraints = list(TimetableConstraint.objects.filter(
            is_enabled=True,
            department=self.timetable.department
        ))
        
        logger.info(f"Loaded {len(self.courses)} courses, {len(self.rooms)} rooms, "
                   f"{len(self.time_slots)} time slots, {len(self.lecturers)} lecturers")
    
    def create_individual(self):
        """Create a random individual (timetable solution)."""
        individual = []
        
        for course in self.courses:
            # For each session of the course
            for session in range(course.sessions_per_week):
                gene = {
                    'course_id': course.id,
                    'session_number': session,
                    'room_id': random.choice(self.rooms).id if self.rooms else None,
                    'time_slot_id': random.choice(self.time_slots).id if self.time_slots else None,
                    'lecturer_id': random.choice([l for l in self.lecturers if l.department == course.department.name]).id 
                                  if self.lecturers else None
                }
                individual.append(gene)
        
        return creator.Individual(individual)
    
    def evaluate_fitness(self, individual):
        """Evaluate the fitness of an individual timetable."""
        fitness_score = 0.0
        penalty = 0.0
        
        # Convert individual to timetable entries for evaluation
        entries = self.individual_to_entries(individual)
        
        # Check hard constraints
        hard_constraint_violations = self.check_hard_constraints(entries)
        penalty += hard_constraint_violations * 1000  # Heavy penalty for hard constraint violations
        
        # Check soft constraints
        soft_constraint_score = self.check_soft_constraints(entries)
        fitness_score += soft_constraint_score
        
        # Additional fitness factors
        fitness_score += self.calculate_preference_satisfaction(entries)
        fitness_score += self.calculate_workload_distribution(entries)
        fitness_score += self.calculate_room_utilization(entries)
        
        # Final fitness (higher is better)
        final_fitness = max(0, fitness_score - penalty)
        return (final_fitness,)
    
    def individual_to_entries(self, individual):
        """Convert individual representation to timetable entries."""
        entries = []
        for gene in individual:
            if all(gene[key] for key in ['course_id', 'room_id', 'time_slot_id', 'lecturer_id']):
                entries.append({
                    'course': Course.objects.get(id=gene['course_id']),
                    'room': Room.objects.get(id=gene['room_id']),
                    'time_slot': TimeSlot.objects.get(id=gene['time_slot_id']),
                    'lecturer': User.objects.get(id=gene['lecturer_id']),
                    'session_number': gene['session_number']
                })
        return entries
    
    def check_hard_constraints(self, entries):
        """Check hard constraints and return violation count."""
        violations = 0
        
        # Room conflicts (same room, same time)
        room_time_combinations = {}
        for entry in entries:
            key = (entry['room'].id, entry['time_slot'].id)
            if key in room_time_combinations:
                violations += 1
            else:
                room_time_combinations[key] = entry
        
        # Lecturer conflicts (same lecturer, same time)
        lecturer_time_combinations = {}
        for entry in entries:
            key = (entry['lecturer'].id, entry['time_slot'].id)
            if key in lecturer_time_combinations:
                violations += 1
            else:
                lecturer_time_combinations[key] = entry
        
        # Room capacity constraints
        for entry in entries:
            if entry['course'].max_students > entry['room'].capacity:
                violations += 1
        
        # Course-room type compatibility
        for entry in entries:
            course_type = entry['course'].course_type
            room_type = entry['room'].room_type
            
            # Define compatibility rules
            if course_type == 'lab' and room_type not in ['laboratory', 'computer_lab']:
                violations += 1
            elif course_type == 'lecture' and room_type == 'laboratory':
                violations += 1
        
        return violations
    
    def check_soft_constraints(self, entries):
        """Check soft constraints and return satisfaction score."""
        score = 0.0
        
        # Time preference satisfaction (placeholder - would integrate with preference service)
        # This would check lecturer and student time preferences
        
        # Avoid back-to-back classes for lecturers
        lecturer_schedule = {}
        for entry in entries:
            lecturer_id = entry['lecturer'].id
            time_slot = entry['time_slot']
            
            if lecturer_id not in lecturer_schedule:
                lecturer_schedule[lecturer_id] = []
            lecturer_schedule[lecturer_id].append(time_slot)
        
        # Check for reasonable gaps between classes
        for lecturer_id, slots in lecturer_schedule.items():
            slots.sort(key=lambda x: (x.day_of_week, x.start_time))
            for i in range(len(slots) - 1):
                current_slot = slots[i]
                next_slot = slots[i + 1]
                
                # Same day check
                if current_slot.day_of_week == next_slot.day_of_week:
                    # Calculate time difference
                    time_diff = (next_slot.start_time.hour * 60 + next_slot.start_time.minute) - \
                               (current_slot.end_time.hour * 60 + current_slot.end_time.minute)
                    
                    if time_diff < 30:  # Less than 30 minutes gap
                        score -= 10
                    elif time_diff > 180:  # More than 3 hours gap
                        score -= 5
                    else:
                        score += 5  # Good gap
        
        return score
    
    def calculate_preference_satisfaction(self, entries):
        """Calculate how well the timetable satisfies preferences."""
        # This would integrate with the preference service
        # For now, return a placeholder score
        return 0.0
    
    def calculate_workload_distribution(self, entries):
        """Calculate workload distribution score."""
        lecturer_workload = {}
        
        for entry in entries:
            lecturer_id = entry['lecturer'].id
            if lecturer_id not in lecturer_workload:
                lecturer_workload[lecturer_id] = 0
            lecturer_workload[lecturer_id] += entry['course'].duration_hours
        
        if not lecturer_workload:
            return 0.0
        
        # Calculate standard deviation of workload
        workloads = list(lecturer_workload.values())
        mean_workload = np.mean(workloads)
        std_workload = np.std(workloads)
        
        # Lower standard deviation is better (more even distribution)
        return max(0, 100 - std_workload * 10)
    
    def calculate_room_utilization(self, entries):
        """Calculate room utilization efficiency."""
        room_usage = {}
        
        for entry in entries:
            room_id = entry['room'].id
            if room_id not in room_usage:
                room_usage[room_id] = 0
            room_usage[room_id] += 1
        
        if not room_usage:
            return 0.0
        
        # Prefer balanced room usage
        usage_values = list(room_usage.values())
        mean_usage = np.mean(usage_values)
        std_usage = np.std(usage_values)
        
        return max(0, 50 - std_usage * 5)
    
    def crossover(self, ind1, ind2):
        """Crossover operation for two individuals."""
        # Single-point crossover
        if len(ind1) > 1 and len(ind2) > 1:
            point = random.randint(1, min(len(ind1), len(ind2)) - 1)
            ind1[point:], ind2[point:] = ind2[point:], ind1[point:]
        
        return ind1, ind2
    
    def mutate(self, individual):
        """Mutation operation for an individual."""
        if not individual:
            return (individual,)
        
        # Random gene mutation
        for i in range(len(individual)):
            if random.random() < self.mutation_rate:
                gene = individual[i]
                
                # Mutate one aspect of the gene
                mutation_type = random.choice(['room', 'time_slot', 'lecturer'])
                
                if mutation_type == 'room' and self.rooms:
                    gene['room_id'] = random.choice(self.rooms).id
                elif mutation_type == 'time_slot' and self.time_slots:
                    gene['time_slot_id'] = random.choice(self.time_slots).id
                elif mutation_type == 'lecturer' and self.lecturers:
                    # Try to keep lecturer from same department
                    course = Course.objects.get(id=gene['course_id'])
                    dept_lecturers = [l for l in self.lecturers if l.department == course.department.name]
                    if dept_lecturers:
                        gene['lecturer_id'] = random.choice(dept_lecturers).id
                    else:
                        gene['lecturer_id'] = random.choice(self.lecturers).id
        
        return (individual,)
    
    def run(self, progress_callback=None):
        """Run the genetic algorithm."""
        logger.info(f"Starting genetic algorithm with {self.population_size} individuals for {self.generations} generations")
        
        # Load data
        self.load_data()
        
        if not self.courses:
            raise ValueError("No courses found for the specified academic year and semester")
        
        # Create initial population
        population = self.toolbox.population(n=self.population_size)
        
        # Statistics
        stats = tools.Statistics(lambda ind: ind.fitness.values)
        stats.register("avg", np.mean)
        stats.register("std", np.std)
        stats.register("min", np.min)
        stats.register("max", np.max)
        
        # Hall of Fame to keep track of best individuals
        hof = tools.HallOfFame(1)
        
        # Run the algorithm
        population, logbook = algorithms.eaSimple(
            population, self.toolbox,
            cxpb=self.crossover_rate,
            mutpb=self.mutation_rate,
            ngen=self.generations,
            stats=stats,
            halloffame=hof,
            verbose=True
        )
        
        # Get the best solution
        best_individual = hof[0]
        best_fitness = best_individual.fitness.values[0]
        
        logger.info(f"Best fitness achieved: {best_fitness}")
        
        return {
            'best_individual': best_individual,
            'best_fitness': best_fitness,
            'population': population,
            'logbook': logbook,
            'hall_of_fame': hof
        }
