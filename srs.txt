PROJECT NEEDS
VPS Hosting: Hostinger KVM8 vps or any other better option but it should be a vps server.

USERS
1. Administration
2. Teachers
3. Students

TECHNOLOGIES
1. Front-end: html, tailwindcss, React.js
2. Back-end: Django/Python for the APIs, Jwt for authentication
3. Database: MongoDB
4. Micro-service: Docker and Kubernetes
5. Deployment: hostinger KVM8 vps

ALGORITHM
Metaheuristic Algorithm specifically the Genetic Algorithm

MICROSERVICES
    1. Authentication Service 
    2. User Management Service 
    3. Preference Collection Service 
    4. Timetable Generation Service 
    5. Notification Service 
    6. Data Analytics Service 
    7. API Gateway 

MONITORING 
1. Prometheus
2. Grafana

ACTOR FEATURES
1. Administrator
-Create/block/deleteLecturer
-Create/block/delete Student
-Create Semester(duration, periods, courses, lecturers(have to be mapped to courses), departments, classrooms). This will also account for holidays/exceptions.
-Generate timetable
-Edit timetable manually (Also manually through drag and drop) (With conflict detection (e.g., overlapping classes in the same room))
-Timetable export (pdf, excel, ics calender)
-Timetable sorting/filter
-Send generated timetable to lecturer and student for preferences
-Set automatic push notifications and email reminders, to remind lecturers and students to check generated timetable and provide new references if not satisfied.
-Holistic Data View (This is important for administrators to understand the input data driving the timetable generation. Consider features like filtering and sorting)
2. Lecturer
-login
-provide preferences
-edit preferences
-view generated timetable and give new preferences
-View timetable by courses(sorting functionality)
-View assigned courses
-Give new preferences if not satisfied
-Receive reminders (directly in the application and also through their emails)
3. Student
-login
-provide preferences
-edit preferences
-view generated timetable (sent from admin)(view timetable by: department/course or other filters)
-Give new preferences if not satisfied
-Receive reminders (directly in the application and also through their emails)
