#!/bin/bash

# Deployment script for Timetable Management System
# This script helps with building and deploying the microservices

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DOCKER_REGISTRY="timetable"
NAMESPACE="timetable-system"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Help function
show_help() {
    echo "Timetable Management System Deployment Script"
    echo ""
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  build           Build Docker images"
    echo "  deploy          Deploy to Kubernetes"
    echo "  dev             Start development environment"
    echo "  test            Run tests"
    echo "  clean           Clean up resources"
    echo "  logs            Show service logs"
    echo "  status          Show deployment status"
    echo ""
    echo "Options:"
    echo "  --service       Specify service (auth-service, user-service, etc.)"
    echo "  --env           Environment (dev, staging, prod)"
    echo "  --help          Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 build --service auth-service"
    echo "  $0 deploy --env dev"
    echo "  $0 dev"
    echo "  $0 logs --service auth-service"
}

# Build Docker images
build_images() {
    local service=$1
    
    if [ "$service" = "auth-service" ] || [ -z "$service" ]; then
        log_info "Building auth-service Docker image..."
        docker build -t ${DOCKER_REGISTRY}/auth-service:latest ./auth-service/
        log_success "Auth service image built successfully"
    fi

    if [ "$service" = "user-service" ] || [ -z "$service" ]; then
        log_info "Building user-service Docker image..."
        docker build -t ${DOCKER_REGISTRY}/user-service:latest ./user-service/
        log_success "User service image built successfully"
    fi

    if [ "$service" = "preference-service" ] || [ -z "$service" ]; then
        log_info "Building preference-service Docker image..."
        docker build -t ${DOCKER_REGISTRY}/preference-service:latest ./preference-service/
        log_success "Preference service image built successfully"
    fi
}

# Deploy to Kubernetes
deploy_k8s() {
    local env=${1:-dev}
    
    log_info "Deploying to Kubernetes (environment: $env)..."
    
    # Create namespace
    log_info "Creating namespace..."
    kubectl apply -f k8s/namespace/namespace.yaml
    
    # Deploy MongoDB and Redis
    log_info "Deploying databases..."
    kubectl apply -f k8s/mongodb/
    
    # Wait for databases to be ready
    log_info "Waiting for databases to be ready..."
    kubectl wait --for=condition=ready pod -l app=mongodb -n $NAMESPACE --timeout=300s
    kubectl wait --for=condition=ready pod -l app=redis -n $NAMESPACE --timeout=300s
    
    # Deploy auth service
    log_info "Deploying auth service..."
    kubectl apply -f k8s/auth-service/

    # Wait for auth service to be ready
    log_info "Waiting for auth service to be ready..."
    kubectl wait --for=condition=ready pod -l app=auth-service -n $NAMESPACE --timeout=300s

    # Deploy user service
    log_info "Deploying user service..."
    kubectl apply -f k8s/user-service/

    # Wait for user service to be ready
    log_info "Waiting for user service to be ready..."
    kubectl wait --for=condition=ready pod -l app=user-service -n $NAMESPACE --timeout=300s

    # Deploy preference service
    log_info "Deploying preference service..."
    kubectl apply -f k8s/preference-service/

    # Wait for preference service to be ready
    log_info "Waiting for preference service to be ready..."
    kubectl wait --for=condition=ready pod -l app=preference-service -n $NAMESPACE --timeout=300s
    
    # Deploy API Gateway
    log_info "Deploying API Gateway..."
    kubectl apply -f k8s/api-gateway/
    
    # Wait for API Gateway to be ready
    log_info "Waiting for API Gateway to be ready..."
    kubectl wait --for=condition=ready pod -l app=api-gateway -n $NAMESPACE --timeout=300s
    
    log_success "Deployment completed successfully!"
    
    # Show access information
    show_access_info
}

# Start development environment
start_dev() {
    log_info "Starting development environment..."
    
    # Copy environment file
    if [ ! -f .env ]; then
        log_info "Creating .env file from template..."
        cp .env.example .env
        log_warning "Please update .env file with your configuration"
    fi
    
    # Start services with Docker Compose
    log_info "Starting services with Docker Compose..."
    docker-compose up -d
    
    # Wait for services to be ready
    log_info "Waiting for services to start..."
    sleep 30
    
    # Run migrations and setup initial data
    log_info "Running auth service migrations..."
    docker-compose exec auth-service python manage.py migrate

    log_info "Running user service migrations..."
    docker-compose exec user-service python manage.py migrate

    log_info "Running preference service migrations..."
    docker-compose exec preference-service python manage.py migrate

    log_info "Setting up initial data..."
    docker-compose exec auth-service python manage.py setup_initial_data
    
    log_success "Development environment started successfully!"
    
    # Show access information
    echo ""
    echo "Access URLs:"
    echo "  API Gateway: http://localhost:8000"
    echo "  Auth Service: http://localhost:8001"
    echo "  User Service: http://localhost:8002"
    echo "  Preference Service: http://localhost:8003"
    echo "  API Documentation: http://localhost:8000/api/docs/"
    echo "  MongoDB: mongodb://localhost:27017"
    echo "  Redis: redis://localhost:6379"
}

# Run tests
run_tests() {
    local service=$1
    
    if [ "$service" = "auth-service" ] || [ -z "$service" ]; then
        log_info "Running auth-service tests..."
        docker-compose exec auth-service python manage.py test
    fi

    if [ "$service" = "user-service" ] || [ -z "$service" ]; then
        log_info "Running user-service tests..."
        docker-compose exec user-service python manage.py test
    fi

    if [ "$service" = "preference-service" ] || [ -z "$service" ]; then
        log_info "Running preference-service tests..."
        docker-compose exec preference-service python manage.py test
    fi
}

# Clean up resources
cleanup() {
    log_info "Cleaning up resources..."
    
    # Stop Docker Compose
    docker-compose down -v
    
    # Clean up Kubernetes resources
    kubectl delete namespace $NAMESPACE --ignore-not-found=true
    
    # Remove Docker images
    docker rmi ${DOCKER_REGISTRY}/auth-service:latest 2>/dev/null || true
    docker rmi ${DOCKER_REGISTRY}/user-service:latest 2>/dev/null || true
    docker rmi ${DOCKER_REGISTRY}/preference-service:latest 2>/dev/null || true
    
    log_success "Cleanup completed"
}

# Show service logs
show_logs() {
    local service=$1
    
    if [ -z "$service" ]; then
        log_error "Please specify a service with --service option"
        exit 1
    fi
    
    # Check if running in Docker Compose or Kubernetes
    if docker-compose ps | grep -q "$service"; then
        log_info "Showing Docker Compose logs for $service..."
        docker-compose logs -f "$service"
    else
        log_info "Showing Kubernetes logs for $service..."
        kubectl logs -f deployment/$service -n $NAMESPACE
    fi
}

# Show deployment status
show_status() {
    log_info "Deployment Status:"
    echo ""
    
    # Check Docker Compose
    echo "Docker Compose Services:"
    docker-compose ps
    echo ""
    
    # Check Kubernetes
    echo "Kubernetes Pods:"
    kubectl get pods -n $NAMESPACE 2>/dev/null || echo "Kubernetes not deployed"
    echo ""
    
    echo "Kubernetes Services:"
    kubectl get services -n $NAMESPACE 2>/dev/null || echo "Kubernetes not deployed"
}

# Show access information
show_access_info() {
    echo ""
    log_success "Deployment completed! Access information:"
    echo ""
    
    # Get API Gateway external IP
    EXTERNAL_IP=$(kubectl get service api-gateway -n $NAMESPACE -o jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>/dev/null || echo "localhost")
    
    echo "Access URLs:"
    echo "  API Gateway: http://$EXTERNAL_IP"
    echo "  API Documentation: http://$EXTERNAL_IP/api/docs/"
    echo "  Health Check: http://$EXTERNAL_IP/api/health/"
    echo "  User Management: http://$EXTERNAL_IP/api/users/"
    echo "  Profile Management: http://$EXTERNAL_IP/api/profiles/"
    echo ""
    echo "Default Superuser:"
    echo "  Email: <EMAIL>"
    echo "  Password: admin123"
    echo "  (Please change after first login)"
}

# Main script logic
main() {
    local command=$1
    shift
    
    # Parse options
    local service=""
    local env="dev"
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --service)
                service="$2"
                shift 2
                ;;
            --env)
                env="$2"
                shift 2
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # Execute command
    case $command in
        build)
            build_images "$service"
            ;;
        deploy)
            build_images "$service"
            deploy_k8s "$env"
            ;;
        dev)
            start_dev
            ;;
        test)
            run_tests "$service"
            ;;
        clean)
            cleanup
            ;;
        logs)
            show_logs "$service"
            ;;
        status)
            show_status
            ;;
        help|--help)
            show_help
            ;;
        *)
            log_error "Unknown command: $command"
            show_help
            exit 1
            ;;
    esac
}

# Check if no arguments provided
if [ $# -eq 0 ]; then
    show_help
    exit 1
fi

# Run main function
main "$@"
