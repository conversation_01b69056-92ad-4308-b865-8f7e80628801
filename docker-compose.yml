version: '3.8'

services:
  # MongoDB Database
  mongodb:
    image: mongo:6.0
    container_name: timetable_mongodb
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: timetable_system
    volumes:
      - mongodb_data:/data/db
      - ./init-mongo.js:/docker-entrypoint-initdb.d/init-mongo.js:ro
    networks:
      - timetable_network

  # Redis for caching and Celery
  redis:
    image: redis:7-alpine
    container_name: timetable_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - timetable_network

  # Authentication Service
  auth-service:
    build:
      context: ./auth-service
      dockerfile: Dockerfile
    container_name: timetable_auth_service
    restart: unless-stopped
    ports:
      - "8001:8001"
    environment:
      - MONGODB_URI=***************************************************************************
      - REDIS_URL=redis://redis:6379/0
      - DEBUG=True
      - SECRET_KEY=auth-service-secret-key-dev
      - ALLOWED_HOSTS=localhost,127.0.0.1,auth-service
    volumes:
      - ./auth-service:/app
      - auth_static:/app/staticfiles
      - auth_media:/app/media
    depends_on:
      - mongodb
      - redis
    networks:
      - timetable_network
    command: >
      sh -c "python manage.py migrate &&
             python manage.py collectstatic --noinput &&
             python manage.py runserver 0.0.0.0:8001"

  # User Management Service
  user-service:
    build:
      context: ./user-service
      dockerfile: Dockerfile
    container_name: timetable_user_service
    restart: unless-stopped
    ports:
      - "8002:8002"
    environment:
      - MONGODB_URI=***************************************************************************
      - REDIS_URL=redis://redis:6379/0
      - AUTH_SERVICE_URL=http://auth-service:8001
      - DEBUG=True
      - SECRET_KEY=user-service-secret-key-dev
      - JWT_SECRET_KEY=jwt-secret-key-development
      - AUTH_SERVICE_INTERNAL_TOKEN=internal-service-token
      - ALLOWED_HOSTS=localhost,127.0.0.1,user-service
    volumes:
      - ./user-service:/app
      - user_static:/app/staticfiles
      - user_media:/app/media
    depends_on:
      - mongodb
      - redis
      - auth-service
    networks:
      - timetable_network
    command: >
      sh -c "python manage.py migrate &&
             python manage.py collectstatic --noinput &&
             python manage.py runserver 0.0.0.0:8002"

  # Preference Collection Service
  preference-service:
    build:
      context: ./preference-service
      dockerfile: Dockerfile
    container_name: timetable_preference_service
    restart: unless-stopped
    ports:
      - "8003:8003"
    environment:
      - MONGODB_URI=***************************************************************************
      - REDIS_URL=redis://redis:6379/0
      - USER_SERVICE_URL=http://user-service:8002
      - AUTH_SERVICE_URL=http://auth-service:8001
      - TIMETABLE_SERVICE_URL=http://timetable-service:8004
      - DEBUG=True
      - SECRET_KEY=preference-service-secret-key-dev
      - JWT_SECRET_KEY=jwt-secret-key-development
      - INTERNAL_SERVICE_TOKEN=internal-service-token
      - ALLOWED_HOSTS=localhost,127.0.0.1,preference-service
      - PREFERENCE_VALIDATION_ENABLED=True
      - PREFERENCE_CONFLICT_DETECTION=True
      - PREFERENCE_AUTO_SAVE_INTERVAL=30
      - DEFAULT_TIME_SLOT_DURATION=60
      - WORKING_HOURS_START=08:00
      - WORKING_HOURS_END=18:00
      - WORKING_DAYS=monday,tuesday,wednesday,thursday,friday
    volumes:
      - ./preference-service:/app
      - preference_static:/app/staticfiles
      - preference_media:/app/media
    depends_on:
      - mongodb
      - redis
      - auth-service
      - user-service
    networks:
      - timetable_network
    command: >
      sh -c "python manage.py migrate &&
             python manage.py collectstatic --noinput &&
             daphne -b 0.0.0.0 -p 8003 preference_service.asgi:application"

  # Preference Service Celery Worker
  preference-celery-worker:
    build:
      context: ./preference-service
      dockerfile: Dockerfile
    container_name: timetable_preference_celery_worker
    restart: unless-stopped
    environment:
      - MONGODB_URI=***************************************************************************
      - REDIS_URL=redis://redis:6379/0
      - USER_SERVICE_URL=http://user-service:8002
      - AUTH_SERVICE_URL=http://auth-service:8001
      - TIMETABLE_SERVICE_URL=http://timetable-service:8004
      - DEBUG=True
      - SECRET_KEY=preference-service-secret-key-dev
      - JWT_SECRET_KEY=jwt-secret-key-development
      - INTERNAL_SERVICE_TOKEN=internal-service-token
    volumes:
      - ./preference-service:/app
    depends_on:
      - mongodb
      - redis
      - preference-service
    networks:
      - timetable_network
    command: celery -A preference_service worker --loglevel=info

  # Preference Service Celery Beat
  preference-celery-beat:
    build:
      context: ./preference-service
      dockerfile: Dockerfile
    container_name: timetable_preference_celery_beat
    restart: unless-stopped
    environment:
      - MONGODB_URI=***************************************************************************
      - REDIS_URL=redis://redis:6379/0
      - USER_SERVICE_URL=http://user-service:8002
      - AUTH_SERVICE_URL=http://auth-service:8001
      - TIMETABLE_SERVICE_URL=http://timetable-service:8004
      - DEBUG=True
      - SECRET_KEY=preference-service-secret-key-dev
      - JWT_SECRET_KEY=jwt-secret-key-development
      - INTERNAL_SERVICE_TOKEN=internal-service-token
    volumes:
      - ./preference-service:/app
    depends_on:
      - mongodb
      - redis
      - preference-service
    networks:
      - timetable_network
    command: celery -A preference_service beat --loglevel=info

  # API Gateway (NGINX)
  api-gateway:
    image: nginx:alpine
    container_name: timetable_api_gateway
    restart: unless-stopped
    ports:
      - "8000:80"
    volumes:
      - ./api-gateway/nginx.conf:/etc/nginx/nginx.conf:ro
      - auth_static:/var/www/auth/static:ro
    depends_on:
      - auth-service
      - user-service
      - preference-service
    networks:
      - timetable_network

volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local
  auth_static:
    driver: local
  auth_media:
    driver: local
  user_static:
    driver: local
  user_media:
    driver: local
  preference_static:
    driver: local
  preference_media:
    driver: local

networks:
  timetable_network:
    driver: bridge
