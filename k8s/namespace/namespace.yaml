apiVersion: v1
kind: Namespace
metadata:
  name: timetable-system
  labels:
    name: timetable-system
    app: timetable-management
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: timetable-config
  namespace: timetable-system
data:
  MONGODB_DB_NAME: "timetable_system"
  FRONTEND_URL: "http://localhost:3000"
  CORS_ALLOWED_ORIGINS: "http://localhost:3000,http://127.0.0.1:3000"
  JWT_ACCESS_TOKEN_LIFETIME: "60"
  JWT_REFRESH_TOKEN_LIFETIME: "7"
  JWT_ALGORITHM: "HS256"
  EMAIL_BACKEND: "django.core.mail.backends.console.EmailBackend"
  EMAIL_HOST: "smtp.gmail.com"
  EMAIL_PORT: "587"
  EMAIL_USE_TLS: "True"
  DEFAULT_FROM_EMAIL: "<EMAIL>"
  LOG_LEVEL: "INFO"
---
apiVersion: v1
kind: Secret
metadata:
  name: timetable-secrets
  namespace: timetable-system
type: Opaque
data:
  # Base64 encoded secrets (replace with actual values)
  MONGODB_URI: ****************************************************************************************************
  AUTH_SECRET_KEY: YXV0aC1zZXJ2aWNlLXNlY3JldC1rZXktcHJvZHVjdGlvbg==
  USER_SECRET_KEY: dXNlci1zZXJ2aWNlLXNlY3JldC1rZXktcHJvZHVjdGlvbg==
  PREFERENCE_SECRET_KEY: cHJlZmVyZW5jZS1zZXJ2aWNlLXNlY3JldC1rZXktcHJvZHVjdGlvbg==
  JWT_SECRET_KEY: and0LXNlY3JldC1rZXktcHJvZHVjdGlvbg==
  REDIS_URL: cmVkaXM6Ly9yZWRpczozNjM3OS8w
  AUTH_SERVICE_INTERNAL_TOKEN: aW50ZXJuYWwtc2VydmljZS10b2tlbg==
  EMAIL_HOST_USER: ****************************
  EMAIL_HOST_PASSWORD: eW91ci1hcHAtcGFzc3dvcmQ=
