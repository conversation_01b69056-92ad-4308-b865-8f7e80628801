apiVersion: apps/v1
kind: Deployment
metadata:
  name: preference-service
  namespace: timetable-system
  labels:
    app: preference-service
    component: microservice
spec:
  replicas: 2
  selector:
    matchLabels:
      app: preference-service
  template:
    metadata:
      labels:
        app: preference-service
        component: microservice
    spec:
      containers:
      - name: preference-service
        image: timetable/preference-service:latest
        ports:
        - containerPort: 8003
        env:
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: MONGODB_URI
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: REDIS_URL
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: PREFERENCE_SECRET_KEY
        - name: JWT_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: JWT_SECRET_KEY
        - name: INTERNAL_SERVICE_TOKEN
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: INTERNAL_SERVICE_TOKEN
        - name: DEBUG
          value: "False"
        - name: ALLOWED_HOSTS
          value: "localhost,127.0.0.1,preference-service,api-gateway"
        - name: USER_SERVICE_URL
          value: "http://user-service:8002"
        - name: AUTH_SERVICE_URL
          value: "http://auth-service:8001"
        - name: TIMETABLE_SERVICE_URL
          value: "http://timetable-service:8004"
        - name: PREFERENCE_VALIDATION_ENABLED
          value: "True"
        - name: PREFERENCE_CONFLICT_DETECTION
          value: "True"
        - name: PREFERENCE_AUTO_SAVE_INTERVAL
          value: "30"
        - name: DEFAULT_TIME_SLOT_DURATION
          value: "60"
        - name: WORKING_HOURS_START
          value: "08:00"
        - name: WORKING_HOURS_END
          value: "18:00"
        - name: WORKING_DAYS
          value: "monday,tuesday,wednesday,thursday,friday"
        envFrom:
        - configMapRef:
            name: timetable-config
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /api/health/
            port: 8003
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /api/health/
            port: 8003
          initialDelaySeconds: 30
          periodSeconds: 10
        volumeMounts:
        - name: static-files
          mountPath: /app/staticfiles
        - name: media-files
          mountPath: /app/media
      initContainers:
      - name: migrate
        image: timetable/preference-service:latest
        command: ['python', 'manage.py', 'migrate']
        env:
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: MONGODB_URI
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: PREFERENCE_SECRET_KEY
        envFrom:
        - configMapRef:
            name: timetable-config
      - name: collectstatic
        image: timetable/preference-service:latest
        command: ['python', 'manage.py', 'collectstatic', '--noinput']
        env:
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: MONGODB_URI
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: PREFERENCE_SECRET_KEY
        envFrom:
        - configMapRef:
            name: timetable-config
        volumeMounts:
        - name: static-files
          mountPath: /app/staticfiles
      volumes:
      - name: static-files
        persistentVolumeClaim:
          claimName: preference-static-pvc
      - name: media-files
        persistentVolumeClaim:
          claimName: preference-media-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: preference-service
  namespace: timetable-system
  labels:
    app: preference-service
    component: microservice
spec:
  ports:
  - port: 8003
    targetPort: 8003
    protocol: TCP
  selector:
    app: preference-service
  type: ClusterIP
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: preference-static-pvc
  namespace: timetable-system
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 1Gi
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: preference-media-pvc
  namespace: timetable-system
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 2Gi
