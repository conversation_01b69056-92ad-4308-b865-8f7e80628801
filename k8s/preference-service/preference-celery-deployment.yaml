apiVersion: apps/v1
kind: Deployment
metadata:
  name: preference-celery-worker
  namespace: timetable-system
  labels:
    app: preference-celery-worker
    component: worker
spec:
  replicas: 2
  selector:
    matchLabels:
      app: preference-celery-worker
  template:
    metadata:
      labels:
        app: preference-celery-worker
        component: worker
    spec:
      containers:
      - name: preference-celery-worker
        image: timetable/preference-service:latest
        command: ['celery', '-A', 'preference_service', 'worker', '--loglevel=info']
        env:
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: MONGODB_URI
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: REDIS_URL
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: PREFERENCE_SECRET_KEY
        - name: JWT_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: JWT_SECRET_KEY
        - name: INTERNAL_SERVICE_TOKEN
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: INTERNAL_SERVICE_TOKEN
        - name: DEBUG
          value: "False"
        - name: USER_SERVICE_URL
          value: "http://user-service:8002"
        - name: AUTH_SERVICE_URL
          value: "http://auth-service:8001"
        - name: TIMETABLE_SERVICE_URL
          value: "http://timetable-service:8004"
        envFrom:
        - configMapRef:
            name: timetable-config
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "250m"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: preference-celery-beat
  namespace: timetable-system
  labels:
    app: preference-celery-beat
    component: scheduler
spec:
  replicas: 1
  selector:
    matchLabels:
      app: preference-celery-beat
  template:
    metadata:
      labels:
        app: preference-celery-beat
        component: scheduler
    spec:
      containers:
      - name: preference-celery-beat
        image: timetable/preference-service:latest
        command: ['celery', '-A', 'preference_service', 'beat', '--loglevel=info']
        env:
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: MONGODB_URI
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: REDIS_URL
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: PREFERENCE_SECRET_KEY
        - name: JWT_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: JWT_SECRET_KEY
        - name: INTERNAL_SERVICE_TOKEN
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: INTERNAL_SERVICE_TOKEN
        - name: DEBUG
          value: "False"
        - name: USER_SERVICE_URL
          value: "http://user-service:8002"
        - name: AUTH_SERVICE_URL
          value: "http://auth-service:8001"
        - name: TIMETABLE_SERVICE_URL
          value: "http://timetable-service:8004"
        envFrom:
        - configMapRef:
            name: timetable-config
        resources:
          requests:
            memory: "128Mi"
            cpu: "50m"
          limits:
            memory: "256Mi"
            cpu: "100m"
