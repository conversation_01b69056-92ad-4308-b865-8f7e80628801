apiVersion: apps/v1
kind: Deployment
metadata:
  name: user-service
  namespace: timetable-system
  labels:
    app: user-service
    component: microservice
spec:
  replicas: 2
  selector:
    matchLabels:
      app: user-service
  template:
    metadata:
      labels:
        app: user-service
        component: microservice
    spec:
      containers:
      - name: user-service
        image: timetable/user-service:latest
        ports:
        - containerPort: 8002
        env:
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: MONGODB_URI
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: REDIS_URL
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: USER_SECRET_KEY
        - name: JWT_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: JWT_SECRET_KEY
        - name: AUTH_SERVICE_INTERNAL_TOKEN
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: AUTH_SERVICE_INTERNAL_TOKEN
        - name: DEBUG
          value: "False"
        - name: ALLOWED_HOSTS
          value: "localhost,127.0.0.1,user-service,api-gateway"
        - name: AUTH_SERVICE_URL
          value: "http://auth-service:8001"
        envFrom:
        - configMapRef:
            name: timetable-config
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /api/health/
            port: 8002
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /api/health/
            port: 8002
          initialDelaySeconds: 30
          periodSeconds: 10
        volumeMounts:
        - name: static-files
          mountPath: /app/staticfiles
        - name: media-files
          mountPath: /app/media
      initContainers:
      - name: migrate
        image: timetable/user-service:latest
        command: ['python', 'manage.py', 'migrate']
        env:
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: MONGODB_URI
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: USER_SECRET_KEY
        envFrom:
        - configMapRef:
            name: timetable-config
      - name: collectstatic
        image: timetable/user-service:latest
        command: ['python', 'manage.py', 'collectstatic', '--noinput']
        env:
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: MONGODB_URI
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: USER_SECRET_KEY
        envFrom:
        - configMapRef:
            name: timetable-config
        volumeMounts:
        - name: static-files
          mountPath: /app/staticfiles
      volumes:
      - name: static-files
        persistentVolumeClaim:
          claimName: user-static-pvc
      - name: media-files
        persistentVolumeClaim:
          claimName: user-media-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: user-service
  namespace: timetable-system
  labels:
    app: user-service
    component: microservice
spec:
  ports:
  - port: 8002
    targetPort: 8002
    protocol: TCP
  selector:
    app: user-service
  type: ClusterIP
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: user-static-pvc
  namespace: timetable-system
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 1Gi
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: user-media-pvc
  namespace: timetable-system
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 2Gi
