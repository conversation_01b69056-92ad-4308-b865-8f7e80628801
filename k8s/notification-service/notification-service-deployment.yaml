apiVersion: apps/v1
kind: Deployment
metadata:
  name: notification-service
  namespace: timetable-system
  labels:
    app: notification-service
    component: microservice
spec:
  replicas: 2
  selector:
    matchLabels:
      app: notification-service
  template:
    metadata:
      labels:
        app: notification-service
        component: microservice
    spec:
      containers:
      - name: notification-service
        image: timetable/notification-service:latest
        ports:
        - containerPort: 8005
        env:
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: MONGODB_URI
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: REDIS_URL
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: NOTIFICATION_SECRET_KEY
        - name: JWT_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: JWT_SECRET_KEY
        - name: INTERNAL_SERVICE_TOKEN
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: INTERNAL_SERVICE_TOKEN
        - name: EMAIL_HOST_PASSWORD
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: EMAIL_HOST_PASSWORD
        - name: SENDGRID_API_KEY
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: SENDGRID_API_KEY
        - name: TWILIO_AUTH_TOKEN
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: TWILIO_AUTH_TOKEN
        - name: FCM_SERVER_KEY
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: FCM_SERVER_KEY
        - name: DEBUG
          value: "False"
        - name: ALLOWED_HOSTS
          value: "localhost,127.0.0.1,notification-service,api-gateway"
        - name: USER_SERVICE_URL
          value: "http://user-service:8002"
        - name: AUTH_SERVICE_URL
          value: "http://auth-service:8001"
        - name: PREFERENCE_SERVICE_URL
          value: "http://preference-service:8003"
        - name: TIMETABLE_SERVICE_URL
          value: "http://timetable-service:8004"
        - name: SITE_URL
          value: "https://timetable.example.com"
        envFrom:
        - configMapRef:
            name: notification-config
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /api/health/
            port: 8005
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /api/health/
            port: 8005
          initialDelaySeconds: 30
          periodSeconds: 10
        volumeMounts:
        - name: static-files
          mountPath: /app/staticfiles
        - name: media-files
          mountPath: /app/media
        - name: logs
          mountPath: /app/logs
      initContainers:
      - name: migrate
        image: timetable/notification-service:latest
        command: ['python', 'manage.py', 'migrate']
        env:
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: MONGODB_URI
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: NOTIFICATION_SECRET_KEY
        envFrom:
        - configMapRef:
            name: notification-config
      - name: collectstatic
        image: timetable/notification-service:latest
        command: ['python', 'manage.py', 'collectstatic', '--noinput']
        env:
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: MONGODB_URI
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: NOTIFICATION_SECRET_KEY
        envFrom:
        - configMapRef:
            name: notification-config
        volumeMounts:
        - name: static-files
          mountPath: /app/staticfiles
      volumes:
      - name: static-files
        persistentVolumeClaim:
          claimName: notification-static-pvc
      - name: media-files
        persistentVolumeClaim:
          claimName: notification-media-pvc
      - name: logs
        persistentVolumeClaim:
          claimName: notification-logs-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: notification-service
  namespace: timetable-system
  labels:
    app: notification-service
    component: microservice
spec:
  ports:
  - port: 8005
    targetPort: 8005
    protocol: TCP
  selector:
    app: notification-service
  type: ClusterIP
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: notification-static-pvc
  namespace: timetable-system
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 1Gi
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: notification-media-pvc
  namespace: timetable-system
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 2Gi
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: notification-logs-pvc
  namespace: timetable-system
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 1Gi
