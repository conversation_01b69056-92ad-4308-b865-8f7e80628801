apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-config
  namespace: timetable-system
data:
  nginx.conf: |
    events {
        worker_connections 1024;
    }

    http {
        include       /etc/nginx/mime.types;
        default_type  application/octet-stream;

        # Logging
        log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                        '$status $body_bytes_sent "$http_referer" '
                        '"$http_user_agent" "$http_x_forwarded_for"';

        access_log /var/log/nginx/access.log main;
        error_log /var/log/nginx/error.log warn;

        # Basic settings
        sendfile on;
        tcp_nopush on;
        tcp_nodelay on;
        keepalive_timeout 65;
        types_hash_max_size 2048;

        # Gzip compression
        gzip on;
        gzip_vary on;
        gzip_min_length 1024;
        gzip_proxied any;
        gzip_comp_level 6;
        gzip_types
            text/plain
            text/css
            text/xml
            text/javascript
            application/json
            application/javascript
            application/xml+rss
            application/atom+xml
            image/svg+xml;

        # Rate limiting
        limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
        limit_req_zone $binary_remote_addr zone=auth:10m rate=5r/s;

        # Upstream servers
        upstream auth_service {
            server auth-service:8001;
        }

        upstream user_service {
            server user-service:8002;
        }

        upstream preference_service {
            server preference-service:8003;
        }

        # Main server block
        server {
            listen 80;
            server_name _;

            # Security headers
            add_header X-Frame-Options DENY;
            add_header X-Content-Type-Options nosniff;
            add_header X-XSS-Protection "1; mode=block";
            add_header Referrer-Policy "strict-origin-when-cross-origin";

            # CORS headers
            add_header Access-Control-Allow-Origin "*";
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
            add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization";

            # Handle preflight requests
            location / {
                if ($request_method = 'OPTIONS') {
                    add_header Access-Control-Allow-Origin "*";
                    add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
                    add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization";
                    add_header Access-Control-Max-Age 1728000;
                    add_header Content-Type "text/plain; charset=utf-8";
                    add_header Content-Length 0;
                    return 204;
                }
            }

            # Health check endpoint
            location /health {
                access_log off;
                return 200 "healthy\n";
                add_header Content-Type text/plain;
            }

            # Authentication service routes
            location /api/auth/ {
                limit_req zone=auth burst=10 nodelay;
                
                proxy_pass http://auth_service;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                
                # Timeout settings
                proxy_connect_timeout 30s;
                proxy_send_timeout 30s;
                proxy_read_timeout 30s;
            }

            # User management routes (User Management Service)
            location /api/users/ {
                limit_req zone=api burst=20 nodelay;

                proxy_pass http://user_service;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;

                # Timeout settings
                proxy_connect_timeout 30s;
                proxy_send_timeout 30s;
                proxy_read_timeout 30s;
            }

            # Profile management routes (User Management Service)
            location /api/profiles/ {
                limit_req zone=api burst=20 nodelay;

                proxy_pass http://user_service;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;

                # Timeout settings
                proxy_connect_timeout 30s;
                proxy_send_timeout 30s;
                proxy_read_timeout 30s;
            }

            # Preference collection routes (Preference Collection Service)
            location /api/preferences/ {
                limit_req zone=api burst=20 nodelay;

                proxy_pass http://preference_service;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;

                # Timeout settings
                proxy_connect_timeout 30s;
                proxy_send_timeout 30s;
                proxy_read_timeout 30s;
            }

            # Preference templates routes (Preference Collection Service)
            location /api/templates/ {
                limit_req zone=api burst=20 nodelay;

                proxy_pass http://preference_service;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;

                # Timeout settings
                proxy_connect_timeout 30s;
                proxy_send_timeout 30s;
                proxy_read_timeout 30s;
            }

            # Preference validation routes (Preference Collection Service)
            location /api/validation/ {
                limit_req zone=api burst=20 nodelay;

                proxy_pass http://preference_service;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;

                # Timeout settings
                proxy_connect_timeout 30s;
                proxy_send_timeout 30s;
                proxy_read_timeout 30s;
            }

            # WebSocket routes for real-time updates (Preference Collection Service)
            location /ws/ {
                proxy_pass http://preference_service;
                proxy_http_version 1.1;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection "upgrade";
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;

                # WebSocket specific settings
                proxy_read_timeout 86400;
                proxy_send_timeout 86400;
            }

            # Core service routes
            location /api/health/ {
                proxy_pass http://auth_service;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }

            location /api/info/ {
                proxy_pass http://auth_service;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }

            # API documentation routes
            location /api/docs/ {
                proxy_pass http://auth_service;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }

            location /api/redoc/ {
                proxy_pass http://auth_service;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }

            location /api/schema/ {
                proxy_pass http://auth_service;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }

            # Static files
            location /static/ {
                alias /var/www/static/;
                expires 1y;
                add_header Cache-Control "public, immutable";
            }

            # Default route
            location = / {
                return 200 '{"service": "Timetable Management API Gateway", "version": "1.0.0", "endpoints": {"/api/auth/": "Authentication Service", "/api/users/": "User Management", "/api/docs/": "API Documentation"}}';
                add_header Content-Type application/json;
            }

            # 404 for unknown routes
            location ~ ^/api/ {
                return 404 '{"error": "Endpoint not found", "message": "The requested API endpoint does not exist"}';
                add_header Content-Type application/json;
            }
        }
    }
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: api-gateway
  namespace: timetable-system
  labels:
    app: api-gateway
    component: gateway
spec:
  replicas: 2
  selector:
    matchLabels:
      app: api-gateway
  template:
    metadata:
      labels:
        app: api-gateway
        component: gateway
    spec:
      containers:
        - name: nginx
          image: nginx:alpine
          ports:
            - containerPort: 80
          volumeMounts:
            - name: nginx-config
              mountPath: /etc/nginx/nginx.conf
              subPath: nginx.conf
            - name: static-files
              mountPath: /var/www/static
          resources:
            requests:
              memory: "128Mi"
              cpu: "100m"
            limits:
              memory: "256Mi"
              cpu: "200m"
          livenessProbe:
            httpGet:
              path: /health
              port: 80
            initialDelaySeconds: 30
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /health
              port: 80
            initialDelaySeconds: 5
            periodSeconds: 5
      volumes:
        - name: nginx-config
          configMap:
            name: nginx-config
        - name: static-files
          persistentVolumeClaim:
            claimName: auth-static-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: api-gateway
  namespace: timetable-system
  labels:
    app: api-gateway
    component: gateway
spec:
  ports:
    - port: 80
      targetPort: 80
      protocol: TCP
  selector:
    app: api-gateway
  type: LoadBalancer
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: timetable-ingress
  namespace: timetable-system
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, POST, PUT, DELETE, OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-headers: "Origin, X-Requested-With, Content-Type, Accept, Authorization"
spec:
  rules:
    - host: timetable.local
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: api-gateway
                port:
                  number: 80
