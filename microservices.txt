1. Authentication Service
Technologies: 
- Backend: Django (Django REST Framework)  
- Authentication: JWT (Simple JWT for Django)  
- Database: MongoDB (for storing user credentials, tokens, and session data)  
- Containerization: Docker  
- Orchestration: Kubernetes  

Functionality: 
- User registration & login (JWT-based)  
- Role-based access control (<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Lecturer, Student)  
- Token generation, validation, and refresh  
- Password reset & email verification  
- Session management


2. User Management Service 
Technologies:
- Back-end: Django  
- Database: MongoDB (stores user profiles, roles, and permissions)  
- API Communication: REST (interacts with Auth Service)  
- Containerization: Docker  
- Orchestration: Kubernetes

Functionality:  
- CRUD operations for Ad<PERSON>, Lecturers, Students (Superadmin can only manage <PERSON>mins)  
- Assign roles & permissions  
- Block/unblock users  
- Fetch user details for other services (e.g., Timetable Generation)


3. Preference Collection Service
Technologies:
- Backend: Django  
- Database: MongoDB (stores preferences for lecturers & students)  
- Real-time Updates: WebSockets (optional, for live preference updates)  
- Containerization: Docker  
- Orchestration: Kubernetes  

Functionality:  
- Collect & store **Lecturer preferences** (available time slots, preferred courses, constraints)  
- Collect & store **Student preferences** (course selections, time constraints)  
- Allow editing of preferences  
- Validate preferences   
- Send preferences to **Timetable Generation Service**  


4. Timetable Generation Service 
Technologies:
- Backend: Django (with Celery for async task handling)  
- Algorithm: Genetic Algorithm (Python libraries: DEAP or PyGAD)  
- Database: MongoDB (stores generated timetables)  
- Conflict Detection: Custom logic (checks room/lecturer/student overlaps)  
- Containerization: Docker  
- Orchestration: Kubernetes  

Functionality:  
- Generate timetables using **Genetic Algorithm** (optimizes for constraints & preferences)  
- Conflict detection (overlapping classes, room clashes)  
- Manual timetable adjustments (drag-and-drop via frontend)  
- Export timetables (PDF, Excel, iCal)  
- Send generated timetables to **Notification Service**  

---

5. Notification Service
Technologies:
- Backend: Django (with Celery for async email/sms tasks)  
- Email: SMTP (Django Email Backend) / SendGrid  
- Real-time Notifications: WebSockets (optional)  
- Database: MongoDB (logs notifications)  
- Containerization: Docker  
- Orchestration: Kubernetes  

Functionality:  
- Send **email reminders** to lecturers/students for preferences & timetable updates  
- In-app notifications (via WebSockets or polling)  
- Push notifications (if mobile app is integrated later)  
- Log notification history  


6. Data Analytics Service
Technologies:  
- Backend: Django (with Pandas for data processing)  
- Database: MongoDB (stores analytics data)  
- Visualization: Chart.js (for frontend) / Matplotlib (for backend reports)  
- Containerization: Docker  
- Orchestration: Kubernetes  

Functionality:  
- Track timetable efficiency (lecturer/student satisfaction metrics)  
- Generate reports on preference trends  
- Provide **Admin Dashboard** with insights (e.g., most conflicting courses)  


7. API Gateway 
Technologies:
- Gateway: NGINX / Kong / Django API Gateway  
- Load Balancing: Kubernetes Ingress  
- Rate Limiting & Caching: Redis (optional)  
- Containerization: Docker  
- Orchestration: Kubernetes  

Functionality:  
- Routes requests to appropriate microservices  
- Handles authentication (JWT validation)  
- Rate limiting & request logging  
- Aggregates responses for frontend  


Frontend (React.js + TailwindCSS)
Admin Panel:
  - User management UI  
  - Timetable generation & editing (drag-and-drop)  
  - Data analytics dashboard  
Lecturer Panel:  
  - Preference submission  
  - Timetable view (with filters)  
Student Panel: 
  - Preference submission  
  - Timetable view (filter by department/course)  


Deployment (Hostinger KVM8 VPS) 
- **Docker Containers** for each microservice  
- **Kubernetes** for orchestration (scaling, load balancing)  
- **NGINX** as reverse proxy  
- **MongoDB** as primary database (replica sets for high availability)  



Summary of Key Technologies per Microservice  
| Microservice               | Key Technologies                          |
|----------------------------|------------------------------------------|
| **Authentication**         | Django, JWT, MongoDB                     |
| **User Management**        | Django, MongoDB                          |
| **Preference Collection**  | Django, MongoDB, WebSockets (optional)   |
| **Timetable Generation**   | Django, Celery, Genetic Algorithm (DEAP) |
| **Notification**           | Django, Celery, SMTP/SendGrid            |
| **Data Analytics**         | Django, Pandas, Chart.js                 |
| **API Gateway**            | NGINX/Kong, Kubernetes Ingress           |

