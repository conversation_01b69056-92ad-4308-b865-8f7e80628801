# Database Configuration
MONGODB_URI=mongodb://localhost:27017/
MONGODB_DB_NAME=timetable_system

# Authentication Service
AUTH_SERVICE_PORT=8001
AUTH_SECRET_KEY=your-secret-key-here
AUTH_DEBUG=True
AUTH_ALLOWED_HOSTS=localhost,127.0.0.1

# JWT Configuration
JWT_SECRET_KEY=your-jwt-secret-key-here
JWT_ACCESS_TOKEN_LIFETIME=60  # minutes
JWT_REFRESH_TOKEN_LIFETIME=7  # days
JWT_ALGORITHM=HS256

# User Management Service
USER_SERVICE_PORT=8002
USER_SECRET_KEY=your-user-service-secret-key
USER_DEBUG=True
USER_ALLOWED_HOSTS=localhost,127.0.0.1

# Preference Collection Service
PREFERENCE_SERVICE_PORT=8003
PREFERENCE_SECRET_KEY=your-preference-service-secret-key
PREFERENCE_DEBUG=True
PREFERENCE_ALLOWED_HOSTS=localhost,127.0.0.1

# Timetable Generation Service
TIMETABLE_SERVICE_PORT=8004
TIMETABLE_SECRET_KEY=your-timetable-service-secret-key
TIMETABLE_DEBUG=True
TIMETABLE_ALLOWED_HOSTS=localhost,127.0.0.1

# Notification Service
NOTIFICATION_SERVICE_PORT=8005
NOTIFICATION_SECRET_KEY=your-notification-service-secret-key
NOTIFICATION_DEBUG=True
NOTIFICATION_ALLOWED_HOSTS=localhost,127.0.0.1

# Analytics Service
ANALYTICS_SERVICE_PORT=8006
ANALYTICS_SECRET_KEY=your-analytics-service-secret-key
ANALYTICS_DEBUG=True
ANALYTICS_ALLOWED_HOSTS=localhost,127.0.0.1

# API Gateway
API_GATEWAY_PORT=8000
API_GATEWAY_SECRET_KEY=your-api-gateway-secret-key
API_GATEWAY_DEBUG=True
API_GATEWAY_ALLOWED_HOSTS=localhost,127.0.0.1

# Email Configuration (for notifications)
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password

# Redis Configuration (for caching and Celery)
REDIS_URL=redis://localhost:6379/0

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# Frontend Configuration
FRONTEND_URL=http://localhost:3000
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# Security
SECURE_SSL_REDIRECT=False
SECURE_HSTS_SECONDS=0
SECURE_HSTS_INCLUDE_SUBDOMAINS=False
SECURE_HSTS_PRELOAD=False

# Logging
LOG_LEVEL=INFO
LOG_FILE_PATH=logs/

# Docker Configuration
COMPOSE_PROJECT_NAME=timetable_system

# Kubernetes Configuration
NAMESPACE=timetable-system
CLUSTER_NAME=timetable-cluster
