"""
Basic tests for the authentication service.
"""
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
from users.models import User, UserProfile
from core.models import Role
import json


class AuthenticationTestCase(APITestCase):
    """Test cases for authentication endpoints."""

    def setUp(self):
        """Set up test data."""
        # Create roles
        self.student_role = Role.objects.create(
            name='student',
            display_name='Student',
            description='Student role',
            permissions=['manage_own_preferences', 'view_own_timetable']
        )
        
        self.lecturer_role = Role.objects.create(
            name='lecturer',
            display_name='Lecturer',
            description='Lecturer role',
            permissions=['manage_own_preferences', 'view_own_timetable', 'view_assigned_courses']
        )

    def test_user_registration(self):
        """Test user registration endpoint."""
        url = reverse('authentication:register')
        data = {
            'email': '<EMAIL>',
            'username': 'testuser',
            'first_name': 'Test',
            'last_name': 'User',
            'password': 'testpassword123',
            'password_confirm': 'testpassword123',
            'role_name': 'student'
        }
        
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn('tokens', response.data)
        self.assertIn('user', response.data)
        
        # Check if user was created
        user = User.objects.get(email='<EMAIL>')
        self.assertEqual(user.username, 'testuser')
        self.assertEqual(user.role.name, 'student')

    def test_user_login(self):
        """Test user login endpoint."""
        # Create a test user
        user = User.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            password='testpassword123',
            first_name='Test',
            last_name='User',
            role=self.student_role
        )
        user.is_verified = True
        user.save()
        
        url = reverse('authentication:login')
        data = {
            'email': '<EMAIL>',
            'password': 'testpassword123'
        }
        
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('tokens', response.data)
        self.assertIn('user', response.data)

    def test_invalid_login(self):
        """Test login with invalid credentials."""
        url = reverse('authentication:login')
        data = {
            'email': '<EMAIL>',
            'password': 'wrongpassword'
        }
        
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_health_check(self):
        """Test health check endpoint."""
        url = reverse('core:health-check')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['status'], 'healthy')

    def test_service_info(self):
        """Test service info endpoint."""
        url = reverse('core:service-info')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('service', response.data)

    def test_roles_list(self):
        """Test roles list endpoint."""
        url = reverse('authentication:roles-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 2)  # student and lecturer roles


class UserModelTestCase(TestCase):
    """Test cases for User model."""

    def setUp(self):
        """Set up test data."""
        self.role = Role.objects.create(
            name='student',
            display_name='Student',
            description='Student role',
            permissions=['manage_own_preferences']
        )

    def test_user_creation(self):
        """Test user creation."""
        user = User.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            password='testpassword123',
            first_name='Test',
            last_name='User',
            role=self.role
        )
        
        self.assertEqual(user.email, '<EMAIL>')
        self.assertEqual(user.username, 'testuser')
        self.assertEqual(user.get_full_name(), 'Test User')
        self.assertTrue(user.has_role('student'))
        self.assertTrue(user.has_permission_for('manage_own_preferences'))

    def test_user_profile_creation(self):
        """Test user profile creation."""
        user = User.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            password='testpassword123',
            role=self.role
        )
        
        profile = UserProfile.objects.create(
            user=user,
            student_id='STU001',
            department='Computer Science',
            faculty='Engineering'
        )
        
        self.assertEqual(profile.user, user)
        self.assertEqual(profile.student_id, 'STU001')
        self.assertEqual(profile.department, 'Computer Science')


class RoleModelTestCase(TestCase):
    """Test cases for Role model."""

    def test_role_creation(self):
        """Test role creation."""
        role = Role.objects.create(
            name='admin',
            display_name='Administrator',
            description='Admin role',
            permissions=['*']
        )
        
        self.assertEqual(role.name, 'admin')
        self.assertEqual(role.display_name, 'Administrator')
        self.assertTrue(role.has_permission('any_permission'))
        self.assertTrue(role.has_permission('*'))

    def test_role_permissions(self):
        """Test role permissions."""
        role = Role.objects.create(
            name='lecturer',
            display_name='Lecturer',
            description='Lecturer role',
            permissions=['manage_own_preferences', 'view_own_timetable']
        )
        
        self.assertTrue(role.has_permission('manage_own_preferences'))
        self.assertTrue(role.has_permission('view_own_timetable'))
        self.assertFalse(role.has_permission('manage_users'))
