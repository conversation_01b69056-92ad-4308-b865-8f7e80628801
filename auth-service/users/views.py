"""
User management views.
"""
from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from core.permissions import IsAdmin, IsSuperAdmin
from .models import User
import logging

logger = logging.getLogger(__name__)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated, IsAdmin])
def user_list_view(request):
    """
    Get list of users (admin only).
    """
    users = User.objects.filter(is_active=True).select_related('role')
    
    user_data = []
    for user in users:
        user_data.append({
            'id': str(user.id),
            'email': user.email,
            'username': user.username,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'role': user.role.display_name if user.role else None,
            'is_verified': user.is_verified,
            'is_blocked': user.is_blocked,
            'date_joined': user.date_joined,
            'last_login': user.last_login,
        })
    
    return Response({
        'users': user_data,
        'count': len(user_data)
    }, status=status.HTTP_200_OK)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated, IsAdmin])
def user_detail_view(request, user_id):
    """
    Get user details (admin only).
    """
    try:
        user = User.objects.select_related('role', 'profile').get(id=user_id, is_active=True)
        
        user_data = {
            'id': str(user.id),
            'email': user.email,
            'username': user.username,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'phone_number': user.phone_number,
            'role': user.role.display_name if user.role else None,
            'is_verified': user.is_verified,
            'is_blocked': user.is_blocked,
            'date_joined': user.date_joined,
            'last_login': user.last_login,
            'password_changed_at': user.password_changed_at,
        }
        
        # Add profile information if available
        if hasattr(user, 'profile'):
            profile = user.profile
            user_data['profile'] = {
                'employee_id': profile.employee_id,
                'student_id': profile.student_id,
                'department': profile.department,
                'faculty': profile.faculty,
                'year_of_study': profile.year_of_study,
                'specialization': profile.specialization,
                'address': profile.address,
                'emergency_contact': profile.emergency_contact,
                'timezone': profile.timezone,
                'language': profile.language,
                'bio': profile.bio,
                'website': profile.website,
            }
        
        return Response(user_data, status=status.HTTP_200_OK)
        
    except User.DoesNotExist:
        return Response({
            'error': 'User not found'
        }, status=status.HTTP_404_NOT_FOUND)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated, IsAdmin])
def block_user_view(request, user_id):
    """
    Block a user (admin only).
    """
    try:
        user = User.objects.get(id=user_id, is_active=True)
        
        # Prevent blocking superadmins
        if user.role and user.role.name == 'superadmin':
            return Response({
                'error': 'Cannot block superadmin users'
            }, status=status.HTTP_403_FORBIDDEN)
        
        # Prevent non-superadmins from blocking admins
        if (user.role and user.role.name == 'admin' and 
            request.user.role and request.user.role.name != 'superadmin'):
            return Response({
                'error': 'Only superadmins can block admin users'
            }, status=status.HTTP_403_FORBIDDEN)
        
        user.block_user()
        
        logger.info(f"User {user.email} blocked by {request.user.email}")
        return Response({
            'message': f'User {user.email} has been blocked'
        }, status=status.HTTP_200_OK)
        
    except User.DoesNotExist:
        return Response({
            'error': 'User not found'
        }, status=status.HTTP_404_NOT_FOUND)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated, IsAdmin])
def unblock_user_view(request, user_id):
    """
    Unblock a user (admin only).
    """
    try:
        user = User.objects.get(id=user_id)
        
        # Prevent non-superadmins from unblocking admins
        if (user.role and user.role.name == 'admin' and 
            request.user.role and request.user.role.name != 'superadmin'):
            return Response({
                'error': 'Only superadmins can unblock admin users'
            }, status=status.HTTP_403_FORBIDDEN)
        
        user.unblock_user()
        
        logger.info(f"User {user.email} unblocked by {request.user.email}")
        return Response({
            'message': f'User {user.email} has been unblocked'
        }, status=status.HTTP_200_OK)
        
    except User.DoesNotExist:
        return Response({
            'error': 'User not found'
        }, status=status.HTTP_404_NOT_FOUND)
