"""
Django admin configuration for users app.
"""
from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.utils.html import format_html
from .models import User, UserProfile, PasswordResetToken, EmailVerificationToken


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    """
    Admin interface for User model.
    """
    list_display = [
        'email', 'username', 'first_name', 'last_name', 
        'role', 'is_verified', 'is_blocked', 'is_active', 'date_joined'
    ]
    list_filter = [
        'role', 'is_verified', 'is_blocked', 'is_active', 
        'is_staff', 'is_superuser', 'date_joined'
    ]
    search_fields = ['email', 'username', 'first_name', 'last_name']
    ordering = ['-date_joined']
    readonly_fields = ['id', 'date_joined', 'last_login', 'password_changed_at']
    
    fieldsets = (
        (None, {
            'fields': ('email', 'username', 'password')
        }),
        ('Personal info', {
            'fields': ('first_name', 'last_name', 'phone_number', 'profile_picture')
        }),
        ('Role & Permissions', {
            'fields': ('role', 'is_staff', 'is_superuser', 'groups', 'user_permissions')
        }),
        ('Status', {
            'fields': ('is_active', 'is_verified', 'is_blocked')
        }),
        ('Important dates', {
            'fields': ('date_joined', 'last_login', 'password_changed_at')
        }),
        ('System', {
            'fields': ('id',),
            'classes': ('collapse',)
        }),
    )
    
    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('email', 'username', 'first_name', 'last_name', 'role', 'password1', 'password2'),
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('role')

    def role(self, obj):
        if obj.role:
            return obj.role.display_name
        return '-'
    role.short_description = 'Role'

    actions = ['block_users', 'unblock_users', 'verify_emails']

    def block_users(self, request, queryset):
        """Block selected users."""
        count = 0
        for user in queryset:
            if not user.is_blocked and user.role and user.role.name != 'superadmin':
                user.block_user()
                count += 1
        self.message_user(request, f'{count} users were blocked.')
    block_users.short_description = 'Block selected users'

    def unblock_users(self, request, queryset):
        """Unblock selected users."""
        count = 0
        for user in queryset:
            if user.is_blocked:
                user.unblock_user()
                count += 1
        self.message_user(request, f'{count} users were unblocked.')
    unblock_users.short_description = 'Unblock selected users'

    def verify_emails(self, request, queryset):
        """Verify emails for selected users."""
        count = 0
        for user in queryset:
            if not user.is_verified:
                user.verify_email()
                count += 1
        self.message_user(request, f'{count} users were verified.')
    verify_emails.short_description = 'Verify emails for selected users'


@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    """
    Admin interface for UserProfile model.
    """
    list_display = [
        'user', 'employee_id', 'student_id', 'department', 
        'faculty', 'year_of_study', 'created_at'
    ]
    list_filter = ['department', 'faculty', 'year_of_study', 'created_at']
    search_fields = [
        'user__email', 'user__first_name', 'user__last_name',
        'employee_id', 'student_id', 'department', 'faculty'
    ]
    readonly_fields = ['id', 'created_at', 'updated_at']
    
    fieldsets = (
        ('User', {
            'fields': ('user',)
        }),
        ('Academic Information', {
            'fields': ('employee_id', 'student_id', 'department', 'faculty', 'year_of_study', 'specialization')
        }),
        ('Contact Information', {
            'fields': ('address', 'emergency_contact')
        }),
        ('Preferences', {
            'fields': ('timezone', 'language', 'notification_preferences')
        }),
        ('Additional Information', {
            'fields': ('bio', 'website', 'social_links')
        }),
        ('System', {
            'fields': ('id', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')


@admin.register(PasswordResetToken)
class PasswordResetTokenAdmin(admin.ModelAdmin):
    """
    Admin interface for PasswordResetToken model.
    """
    list_display = ['user', 'token_short', 'expires_at', 'is_used', 'is_expired_status', 'created_at']
    list_filter = ['is_used', 'expires_at', 'created_at']
    search_fields = ['user__email', 'token']
    readonly_fields = ['id', 'token', 'created_at', 'updated_at']
    
    def token_short(self, obj):
        return str(obj.token)[:8] + '...'
    token_short.short_description = 'Token'

    def is_expired_status(self, obj):
        if obj.is_expired():
            return format_html('<span style="color: red;">Expired</span>')
        return format_html('<span style="color: green;">Valid</span>')
    is_expired_status.short_description = 'Status'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')


@admin.register(EmailVerificationToken)
class EmailVerificationTokenAdmin(admin.ModelAdmin):
    """
    Admin interface for EmailVerificationToken model.
    """
    list_display = ['user', 'token_short', 'expires_at', 'is_used', 'is_expired_status', 'created_at']
    list_filter = ['is_used', 'expires_at', 'created_at']
    search_fields = ['user__email', 'token']
    readonly_fields = ['id', 'token', 'created_at', 'updated_at']
    
    def token_short(self, obj):
        return str(obj.token)[:8] + '...'
    token_short.short_description = 'Token'

    def is_expired_status(self, obj):
        if obj.is_expired():
            return format_html('<span style="color: red;">Expired</span>')
        return format_html('<span style="color: green;">Valid</span>')
    is_expired_status.short_description = 'Status'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')
