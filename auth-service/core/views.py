"""
Core views for shared functionality.
"""
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from django.http import JsonResponse
from django.conf import settings
import logging

logger = logging.getLogger(__name__)


@api_view(['GET'])
@permission_classes([AllowAny])
def health_check(request):
    """
    Health check endpoint for monitoring and load balancers.
    """
    try:
        # You can add more health checks here (database, redis, etc.)
        return Response({
            'status': 'healthy',
            'service': 'auth-service',
            'version': '1.0.0',
            'debug': settings.DEBUG,
        }, status=status.HTTP_200_OK)
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return Response({
            'status': 'unhealthy',
            'service': 'auth-service',
            'error': str(e),
        }, status=status.HTTP_503_SERVICE_UNAVAILABLE)


@api_view(['GET'])
@permission_classes([AllowAny])
def service_info(request):
    """
    Service information endpoint.
    """
    return Response({
        'service': 'Authentication Service',
        'description': 'JWT-based authentication and authorization service',
        'version': '1.0.0',
        'features': [
            'User registration and login',
            'JWT token generation and validation',
            'Role-based access control',
            'Password reset and email verification',
            'Session management',
        ],
        'endpoints': {
            'auth': '/api/auth/',
            'users': '/api/users/',
            'docs': '/api/docs/',
            'health': '/api/health/',
        }
    }, status=status.HTTP_200_OK)
