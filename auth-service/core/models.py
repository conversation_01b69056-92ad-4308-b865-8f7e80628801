"""
Core models for shared functionality across the authentication service.
"""
from django.db import models
from django.utils import timezone
import uuid


class BaseModel(models.Model):
    """
    Abstract base model with common fields for all models.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        abstract = True
        ordering = ['-created_at']

    def soft_delete(self):
        """Soft delete by setting is_active to False"""
        self.is_active = False
        self.save()

    def restore(self):
        """Restore soft deleted object"""
        self.is_active = True
        self.save()


class Role(BaseModel):
    """
    Role model for role-based access control.
    """
    ROLE_CHOICES = [
        ('superadmin', 'Super Administrator'),
        ('admin', 'Administrator'),
        ('lecturer', 'Lecturer'),
        ('student', 'Student'),
    ]

    name = models.Char<PERSON><PERSON>(max_length=50, choices=ROLE_CHOICES, unique=True)
    display_name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    permissions = models.JSONField(default=list, help_text="List of permissions for this role")

    class Meta:
        db_table = 'roles'
        verbose_name = 'Role'
        verbose_name_plural = 'Roles'

    def __str__(self):
        return self.display_name

    def has_permission(self, permission):
        """Check if role has a specific permission"""
        return permission in self.permissions or '*' in self.permissions


class Permission(BaseModel):
    """
    Permission model for granular access control.
    """
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    resource = models.CharField(max_length=50, help_text="Resource this permission applies to")
    action = models.CharField(max_length=50, help_text="Action this permission allows")

    class Meta:
        db_table = 'permissions'
        verbose_name = 'Permission'
        verbose_name_plural = 'Permissions'

    def __str__(self):
        return f"{self.resource}:{self.action}"
