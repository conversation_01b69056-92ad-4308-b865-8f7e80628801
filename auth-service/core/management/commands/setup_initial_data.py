"""
Management command to set up initial data for the authentication service.
"""
from django.core.management.base import BaseCommand
from django.db import transaction
from core.models import Role, Permission
from users.models import User, UserProfile
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Set up initial data including roles, permissions, and superuser'

    def add_arguments(self, parser):
        parser.add_argument(
            '--superuser-email',
            type=str,
            default='<EMAIL>',
            help='Email for the superuser account'
        )
        parser.add_argument(
            '--superuser-password',
            type=str,
            default='admin123',
            help='Password for the superuser account'
        )

    def handle(self, *args, **options):
        """Execute the command."""
        self.stdout.write(self.style.SUCCESS('Setting up initial data...'))
        
        try:
            with transaction.atomic():
                # Create permissions
                self.create_permissions()
                
                # Create roles
                self.create_roles()
                
                # Create superuser
                self.create_superuser(
                    options['superuser_email'],
                    options['superuser_password']
                )
                
            self.stdout.write(
                self.style.SUCCESS('Successfully set up initial data!')
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error setting up initial data: {str(e)}')
            )
            logger.error(f'Error setting up initial data: {str(e)}')

    def create_permissions(self):
        """Create default permissions."""
        permissions_data = [
            {
                'name': '*',
                'description': 'All permissions',
                'resource': 'system',
                'action': 'all'
            },
            {
                'name': 'manage_users',
                'description': 'Create, update, delete users',
                'resource': 'users',
                'action': 'manage'
            },
            {
                'name': 'generate_timetables',
                'description': 'Generate new timetables',
                'resource': 'timetables',
                'action': 'generate'
            },
            {
                'name': 'edit_timetables',
                'description': 'Manually edit timetables',
                'resource': 'timetables',
                'action': 'edit'
            },
            {
                'name': 'view_analytics',
                'description': 'View system analytics',
                'resource': 'analytics',
                'action': 'view'
            },
            {
                'name': 'send_notifications',
                'description': 'Send notifications to users',
                'resource': 'notifications',
                'action': 'send'
            },
            {
                'name': 'manage_own_preferences',
                'description': 'Manage own preferences',
                'resource': 'preferences',
                'action': 'manage_own'
            },
            {
                'name': 'view_own_timetable',
                'description': 'View own timetable',
                'resource': 'timetables',
                'action': 'view_own'
            },
            {
                'name': 'view_assigned_courses',
                'description': 'View assigned courses',
                'resource': 'courses',
                'action': 'view_assigned'
            },
            {
                'name': 'view_course_timetables',
                'description': 'View course timetables',
                'resource': 'timetables',
                'action': 'view_courses'
            }
        ]
        
        for perm_data in permissions_data:
            permission, created = Permission.objects.get_or_create(
                name=perm_data['name'],
                defaults=perm_data
            )
            if created:
                self.stdout.write(f'Created permission: {permission.name}')
            else:
                self.stdout.write(f'Permission already exists: {permission.name}')

    def create_roles(self):
        """Create default roles."""
        roles_data = [
            {
                'name': 'superadmin',
                'display_name': 'Super Administrator',
                'description': 'Full system access and admin management',
                'permissions': ['*']
            },
            {
                'name': 'admin',
                'display_name': 'Administrator',
                'description': 'Manages lecturers, students, and timetables',
                'permissions': [
                    'manage_users',
                    'generate_timetables',
                    'edit_timetables',
                    'view_analytics',
                    'send_notifications'
                ]
            },
            {
                'name': 'lecturer',
                'display_name': 'Lecturer',
                'description': 'Provides preferences and views timetables',
                'permissions': [
                    'manage_own_preferences',
                    'view_own_timetable',
                    'view_assigned_courses'
                ]
            },
            {
                'name': 'student',
                'display_name': 'Student',
                'description': 'Provides preferences and views timetables',
                'permissions': [
                    'manage_own_preferences',
                    'view_own_timetable',
                    'view_course_timetables'
                ]
            }
        ]
        
        for role_data in roles_data:
            role, created = Role.objects.get_or_create(
                name=role_data['name'],
                defaults=role_data
            )
            if created:
                self.stdout.write(f'Created role: {role.display_name}')
            else:
                self.stdout.write(f'Role already exists: {role.display_name}')

    def create_superuser(self, email, password):
        """Create superuser account."""
        try:
            # Check if superuser already exists
            if User.objects.filter(email=email).exists():
                self.stdout.write(f'Superuser with email {email} already exists')
                return
            
            # Get superadmin role
            superadmin_role = Role.objects.get(name='superadmin')
            
            # Create superuser
            user = User.objects.create_superuser(
                email=email,
                password=password,
                username='superadmin',
                first_name='Super',
                last_name='Administrator',
                role=superadmin_role
            )
            user.is_verified = True
            user.save()
            
            # Create user profile
            UserProfile.objects.create(
                user=user,
                department='Administration',
                faculty='System Administration'
            )
            
            self.stdout.write(
                self.style.SUCCESS(f'Created superuser: {email}')
            )
            self.stdout.write(
                self.style.WARNING(f'Superuser password: {password}')
            )
            self.stdout.write(
                self.style.WARNING('Please change the password after first login!')
            )
            
        except Role.DoesNotExist:
            self.stdout.write(
                self.style.ERROR('Superadmin role not found. Please create roles first.')
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error creating superuser: {str(e)}')
            )
