"""
Custom permission classes for role-based access control.
"""
from rest_framework import permissions
from rest_framework.exceptions import PermissionDenied


class IsOwnerOrReadOnly(permissions.BasePermission):
    """
    Custom permission to only allow owners of an object to edit it.
    """

    def has_object_permission(self, request, view, obj):
        # Read permissions are allowed for any request,
        # so we'll always allow GET, HEAD or OPTIONS requests.
        if request.method in permissions.SAFE_METHODS:
            return True

        # Write permissions are only allowed to the owner of the object.
        return obj.user == request.user


class HasRolePermission(permissions.BasePermission):
    """
    Custom permission to check if user has required role.
    """
    required_roles = []

    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False

        if hasattr(view, 'required_roles'):
            required_roles = view.required_roles
        else:
            required_roles = self.required_roles

        if not required_roles:
            return True

        user_role = getattr(request.user, 'role', None)
        if not user_role:
            return False

        return user_role.name in required_roles


class HasPermission(permissions.BasePermission):
    """
    Custom permission to check if user has specific permission.
    """
    required_permission = None

    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False

        required_permission = getattr(view, 'required_permission', self.required_permission)
        if not required_permission:
            return True

        user_role = getattr(request.user, 'role', None)
        if not user_role:
            return False

        return user_role.has_permission(required_permission)


class IsSuperAdmin(permissions.BasePermission):
    """
    Permission class to check if user is a super admin.
    """

    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False

        user_role = getattr(request.user, 'role', None)
        return user_role and user_role.name == 'superadmin'


class IsAdmin(permissions.BasePermission):
    """
    Permission class to check if user is an admin or super admin.
    """

    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False

        user_role = getattr(request.user, 'role', None)
        return user_role and user_role.name in ['admin', 'superadmin']


class IsLecturer(permissions.BasePermission):
    """
    Permission class to check if user is a lecturer.
    """

    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False

        user_role = getattr(request.user, 'role', None)
        return user_role and user_role.name == 'lecturer'


class IsStudent(permissions.BasePermission):
    """
    Permission class to check if user is a student.
    """

    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False

        user_role = getattr(request.user, 'role', None)
        return user_role and user_role.name == 'student'
