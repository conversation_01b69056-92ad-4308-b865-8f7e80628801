"""
Authentication URLs configuration.
"""
from django.urls import path
from rest_framework_simplejwt.views import TokenRefreshView
from . import views

app_name = 'authentication'

urlpatterns = [
    # Authentication endpoints
    path('register/', views.UserRegistrationView.as_view(), name='register'),
    path('login/', views.UserLoginView.as_view(), name='login'),
    path('logout/', views.logout_view, name='logout'),
    path('token/refresh/', TokenRefreshView.as_view(), name='token-refresh'),
    path('token/validate/', views.validate_token_view, name='token-validate'),
    
    # Password management
    path('password/change/', views.change_password_view, name='password-change'),
    path('password/reset/request/', views.password_reset_request_view, name='password-reset-request'),
    path('password/reset/confirm/', views.password_reset_confirm_view, name='password-reset-confirm'),
    
    # Email verification
    path('email/verify/', views.verify_email_view, name='email-verify'),
    
    # User profile
    path('profile/', views.user_profile_view, name='user-profile'),
    
    # Roles
    path('roles/', views.roles_list_view, name='roles-list'),
]
