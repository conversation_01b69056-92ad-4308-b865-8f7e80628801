"""
Celery tasks for authentication-related operations.
"""
from celery import shared_task
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.conf import settings
from django.utils import timezone
from datetime import timedelta
from users.models import User, EmailVerificationToken
import logging

logger = logging.getLogger(__name__)


@shared_task
def send_verification_email(user_id):
    """
    Send email verification link to user.
    """
    try:
        user = User.objects.get(id=user_id)
        
        # Create verification token
        expires_at = timezone.now() + timedelta(hours=24)
        verification_token = EmailVerificationToken.objects.create(
            user=user,
            expires_at=expires_at
        )
        
        # Prepare email content
        subject = 'Verify Your Email - Timetable Management System'
        verification_url = f"{settings.FRONTEND_URL}/verify-email?token={verification_token.token}"
        
        # HTML email content
        html_message = render_to_string('emails/email_verification.html', {
            'user': user,
            'verification_url': verification_url,
            'expires_hours': 24,
        })
        
        # Plain text fallback
        plain_message = f"""
        Hi {user.get_full_name()},
        
        Please verify your email address by clicking the link below:
        {verification_url}
        
        This link will expire in 24 hours.
        
        If you didn't create an account, please ignore this email.
        
        Best regards,
        Timetable Management System Team
        """
        
        # Send email
        send_mail(
            subject=subject,
            message=plain_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[user.email],
            html_message=html_message,
            fail_silently=False,
        )
        
        logger.info(f"Verification email sent to: {user.email}")
        return f"Verification email sent to {user.email}"
        
    except User.DoesNotExist:
        logger.error(f"User with id {user_id} not found")
        return f"User with id {user_id} not found"
    except Exception as e:
        logger.error(f"Failed to send verification email: {str(e)}")
        return f"Failed to send verification email: {str(e)}"


@shared_task
def send_password_reset_email(user_id, reset_token):
    """
    Send password reset link to user.
    """
    try:
        user = User.objects.get(id=user_id)
        
        # Prepare email content
        subject = 'Password Reset - Timetable Management System'
        reset_url = f"{settings.FRONTEND_URL}/reset-password?token={reset_token}"
        
        # HTML email content
        html_message = render_to_string('emails/password_reset.html', {
            'user': user,
            'reset_url': reset_url,
            'expires_hours': 24,
        })
        
        # Plain text fallback
        plain_message = f"""
        Hi {user.get_full_name()},
        
        You requested a password reset for your account. Click the link below to reset your password:
        {reset_url}
        
        This link will expire in 24 hours.
        
        If you didn't request this reset, please ignore this email.
        
        Best regards,
        Timetable Management System Team
        """
        
        # Send email
        send_mail(
            subject=subject,
            message=plain_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[user.email],
            html_message=html_message,
            fail_silently=False,
        )
        
        logger.info(f"Password reset email sent to: {user.email}")
        return f"Password reset email sent to {user.email}"
        
    except User.DoesNotExist:
        logger.error(f"User with id {user_id} not found")
        return f"User with id {user_id} not found"
    except Exception as e:
        logger.error(f"Failed to send password reset email: {str(e)}")
        return f"Failed to send password reset email: {str(e)}"


@shared_task
def send_welcome_email(user_id):
    """
    Send welcome email to new user.
    """
    try:
        user = User.objects.get(id=user_id)
        
        # Prepare email content
        subject = 'Welcome to Timetable Management System'
        
        # HTML email content
        html_message = render_to_string('emails/welcome.html', {
            'user': user,
            'login_url': f"{settings.FRONTEND_URL}/login",
        })
        
        # Plain text fallback
        plain_message = f"""
        Hi {user.get_full_name()},
        
        Welcome to the Timetable Management System!
        
        Your account has been successfully created. You can now log in and start using the system.
        
        Login URL: {settings.FRONTEND_URL}/login
        
        If you have any questions, please contact our support team.
        
        Best regards,
        Timetable Management System Team
        """
        
        # Send email
        send_mail(
            subject=subject,
            message=plain_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[user.email],
            html_message=html_message,
            fail_silently=False,
        )
        
        logger.info(f"Welcome email sent to: {user.email}")
        return f"Welcome email sent to {user.email}"
        
    except User.DoesNotExist:
        logger.error(f"User with id {user_id} not found")
        return f"User with id {user_id} not found"
    except Exception as e:
        logger.error(f"Failed to send welcome email: {str(e)}")
        return f"Failed to send welcome email: {str(e)}"


@shared_task
def cleanup_expired_tokens():
    """
    Clean up expired tokens from the database.
    """
    try:
        from users.models import PasswordResetToken, EmailVerificationToken
        
        now = timezone.now()
        
        # Delete expired password reset tokens
        expired_reset_tokens = PasswordResetToken.objects.filter(
            expires_at__lt=now,
            is_active=True
        )
        reset_count = expired_reset_tokens.count()
        expired_reset_tokens.update(is_active=False)
        
        # Delete expired email verification tokens
        expired_verification_tokens = EmailVerificationToken.objects.filter(
            expires_at__lt=now,
            is_active=True
        )
        verification_count = expired_verification_tokens.count()
        expired_verification_tokens.update(is_active=False)
        
        logger.info(f"Cleaned up {reset_count} expired reset tokens and {verification_count} expired verification tokens")
        return f"Cleaned up {reset_count} reset tokens and {verification_count} verification tokens"
        
    except Exception as e:
        logger.error(f"Failed to cleanup expired tokens: {str(e)}")
        return f"Failed to cleanup expired tokens: {str(e)}"
