"""
Serializers for authentication endpoints.
"""
from rest_framework import serializers
from django.contrib.auth import authenticate
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from users.models import User, UserProfile
from core.models import Role
import logging

logger = logging.getLogger(__name__)


class UserRegistrationSerializer(serializers.ModelSerializer):
    """
    Serializer for user registration.
    """
    password = serializers.CharField(write_only=True, validators=[validate_password])
    password_confirm = serializers.CharField(write_only=True)
    role_name = serializers.Char<PERSON>ield(write_only=True)

    class Meta:
        model = User
        fields = [
            'email', 'username', 'first_name', 'last_name',
            'phone_number', 'password', 'password_confirm', 'role_name'
        ]

    def validate(self, attrs):
        """Validate password confirmation and role."""
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError("Passwords don't match.")
        
        # Validate role exists
        role_name = attrs.get('role_name')
        if role_name:
            try:
                role = Role.objects.get(name=role_name, is_active=True)
                attrs['role'] = role
            except Role.DoesNotExist:
                raise serializers.ValidationError(f"Role '{role_name}' does not exist.")
        
        return attrs

    def create(self, validated_data):
        """Create a new user."""
        validated_data.pop('password_confirm')
        role = validated_data.pop('role', None)
        validated_data.pop('role_name', None)
        
        user = User.objects.create_user(**validated_data)
        if role:
            user.role = role
            user.save()
        
        # Create user profile
        UserProfile.objects.create(user=user)
        
        logger.info(f"New user registered: {user.email}")
        return user


class UserLoginSerializer(serializers.Serializer):
    """
    Serializer for user login.
    """
    email = serializers.EmailField()
    password = serializers.CharField(write_only=True)

    def validate(self, attrs):
        """Validate user credentials."""
        email = attrs.get('email')
        password = attrs.get('password')

        if email and password:
            user = authenticate(username=email, password=password)
            
            if not user:
                raise serializers.ValidationError('Invalid email or password.')
            
            if not user.is_active:
                raise serializers.ValidationError('User account is disabled.')
            
            if user.is_blocked:
                raise serializers.ValidationError('User account is blocked.')
            
            attrs['user'] = user
            return attrs
        else:
            raise serializers.ValidationError('Must include email and password.')


class PasswordChangeSerializer(serializers.Serializer):
    """
    Serializer for password change.
    """
    old_password = serializers.CharField(write_only=True)
    new_password = serializers.CharField(write_only=True, validators=[validate_password])
    new_password_confirm = serializers.CharField(write_only=True)

    def validate(self, attrs):
        """Validate password change."""
        if attrs['new_password'] != attrs['new_password_confirm']:
            raise serializers.ValidationError("New passwords don't match.")
        return attrs

    def validate_old_password(self, value):
        """Validate old password."""
        user = self.context['request'].user
        if not user.check_password(value):
            raise serializers.ValidationError('Old password is incorrect.')
        return value


class PasswordResetRequestSerializer(serializers.Serializer):
    """
    Serializer for password reset request.
    """
    email = serializers.EmailField()

    def validate_email(self, value):
        """Validate email exists."""
        try:
            user = User.objects.get(email=value, is_active=True)
            return value
        except User.DoesNotExist:
            # Don't reveal if email exists or not for security
            return value


class PasswordResetConfirmSerializer(serializers.Serializer):
    """
    Serializer for password reset confirmation.
    """
    token = serializers.UUIDField()
    new_password = serializers.CharField(write_only=True, validators=[validate_password])
    new_password_confirm = serializers.CharField(write_only=True)

    def validate(self, attrs):
        """Validate password reset."""
        if attrs['new_password'] != attrs['new_password_confirm']:
            raise serializers.ValidationError("Passwords don't match.")
        return attrs


class EmailVerificationSerializer(serializers.Serializer):
    """
    Serializer for email verification.
    """
    token = serializers.UUIDField()


class UserProfileSerializer(serializers.ModelSerializer):
    """
    Serializer for user profile information.
    """
    email = serializers.EmailField(source='user.email', read_only=True)
    username = serializers.CharField(source='user.username', read_only=True)
    first_name = serializers.CharField(source='user.first_name', read_only=True)
    last_name = serializers.CharField(source='user.last_name', read_only=True)
    role = serializers.CharField(source='user.role.display_name', read_only=True)
    is_verified = serializers.BooleanField(source='user.is_verified', read_only=True)

    class Meta:
        model = UserProfile
        fields = [
            'email', 'username', 'first_name', 'last_name', 'role',
            'is_verified', 'employee_id', 'student_id', 'department',
            'faculty', 'year_of_study', 'specialization', 'address',
            'emergency_contact', 'timezone', 'language', 'bio',
            'website', 'notification_preferences'
        ]


class TokenValidationSerializer(serializers.Serializer):
    """
    Serializer for token validation.
    """
    token = serializers.CharField()

    def validate_token(self, value):
        """Validate JWT token."""
        from rest_framework_simplejwt.tokens import AccessToken
        from rest_framework_simplejwt.exceptions import InvalidToken, TokenError
        
        try:
            AccessToken(value)
            return value
        except (InvalidToken, TokenError):
            raise serializers.ValidationError('Invalid token.')


class RoleSerializer(serializers.ModelSerializer):
    """
    Serializer for role information.
    """
    class Meta:
        model = Role
        fields = ['id', 'name', 'display_name', 'description', 'permissions']
        read_only_fields = ['id']
