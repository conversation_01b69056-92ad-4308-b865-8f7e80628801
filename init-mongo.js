// MongoDB initialization script
// This script runs when the MongoDB container starts for the first time

// Switch to the timetable_system database
db = db.getSiblingDB('timetable_system');

// Create collections with initial indexes
db.createCollection('users');
db.createCollection('user_profiles');
db.createCollection('roles');
db.createCollection('permissions');
db.createCollection('preferences');
db.createCollection('timetables');
db.createCollection('notifications');
db.createCollection('analytics');

// Create indexes for better performance
db.users.createIndex({ "email": 1 }, { unique: true });
db.users.createIndex({ "username": 1 }, { unique: true });
db.users.createIndex({ "role": 1 });
db.users.createIndex({ "is_active": 1 });
db.users.createIndex({ "created_at": 1 });

db.user_profiles.createIndex({ "user_id": 1 }, { unique: true });
db.user_profiles.createIndex({ "department": 1 });
db.user_profiles.createIndex({ "employee_id": 1 }, { unique: true, sparse: true });
db.user_profiles.createIndex({ "student_id": 1 }, { unique: true, sparse: true });

db.roles.createIndex({ "name": 1 }, { unique: true });
db.permissions.createIndex({ "name": 1 }, { unique: true });

db.preferences.createIndex({ "user_id": 1 });
db.preferences.createIndex({ "preference_type": 1 });
db.preferences.createIndex({ "created_at": 1 });

db.timetables.createIndex({ "semester": 1 });
db.timetables.createIndex({ "department": 1 });
db.timetables.createIndex({ "created_at": 1 });
db.timetables.createIndex({ "status": 1 });

db.notifications.createIndex({ "user_id": 1 });
db.notifications.createIndex({ "notification_type": 1 });
db.notifications.createIndex({ "is_read": 1 });
db.notifications.createIndex({ "created_at": 1 });

db.analytics.createIndex({ "metric_type": 1 });
db.analytics.createIndex({ "date": 1 });
db.analytics.createIndex({ "department": 1 });

// Insert default roles
db.roles.insertMany([
    {
        name: "superadmin",
        display_name: "Super Administrator",
        description: "Full system access and admin management",
        permissions: ["*"],
        created_at: new Date(),
        updated_at: new Date()
    },
    {
        name: "admin",
        display_name: "Administrator",
        description: "Manages lecturers, students, and timetables",
        permissions: [
            "manage_users",
            "generate_timetables",
            "edit_timetables",
            "view_analytics",
            "send_notifications"
        ],
        created_at: new Date(),
        updated_at: new Date()
    },
    {
        name: "lecturer",
        display_name: "Lecturer",
        description: "Provides preferences and views timetables",
        permissions: [
            "manage_own_preferences",
            "view_own_timetable",
            "view_assigned_courses"
        ],
        created_at: new Date(),
        updated_at: new Date()
    },
    {
        name: "student",
        display_name: "Student",
        description: "Provides preferences and views timetables",
        permissions: [
            "manage_own_preferences",
            "view_own_timetable",
            "view_course_timetables"
        ],
        created_at: new Date(),
        updated_at: new Date()
    }
]);

// Insert default permissions
db.permissions.insertMany([
    { name: "*", description: "All permissions", created_at: new Date() },
    { name: "manage_users", description: "Create, update, delete users", created_at: new Date() },
    { name: "generate_timetables", description: "Generate new timetables", created_at: new Date() },
    { name: "edit_timetables", description: "Manually edit timetables", created_at: new Date() },
    { name: "view_analytics", description: "View system analytics", created_at: new Date() },
    { name: "send_notifications", description: "Send notifications to users", created_at: new Date() },
    { name: "manage_own_preferences", description: "Manage own preferences", created_at: new Date() },
    { name: "view_own_timetable", description: "View own timetable", created_at: new Date() },
    { name: "view_assigned_courses", description: "View assigned courses", created_at: new Date() },
    { name: "view_course_timetables", description: "View course timetables", created_at: new Date() }
]);

print("MongoDB initialization completed successfully!");
print("Created collections: users, user_profiles, roles, permissions, preferences, timetables, notifications, analytics");
print("Created indexes for better performance");
print("Inserted default roles and permissions");
