# User Management Service

The User Management Service is a comprehensive microservice for managing users, profiles, departments, and courses in the Timetable Management System. It provides CRUD operations with role-based access control, bulk operations, and detailed audit logging.

## Features

### 👥 User Management
- **CRUD Operations**: Create, read, update, and delete users with proper role restrictions
- **Role-Based Access Control**: <PERSON>admin can manage all roles, Admin can manage lecturers and students
- **User Status Management**: Block/unblock users, verify email addresses
- **Activity Tracking**: Comprehensive logging of all user-related activities
- **Inter-Service Communication**: Seamless integration with the Authentication Service

### 📊 Bulk Operations
- **User Import**: Import users from CSV/Excel files with validation
- **User Export**: Export user data to CSV format with filtering options
- **Bulk Updates**: Perform bulk operations on multiple users (block, unblock, role changes)
- **Progress Tracking**: Real-time progress monitoring for long-running operations
- **Error Handling**: Detailed error reporting and recovery mechanisms

### 👤 Profile Management
- **Extended Profiles**: Comprehensive user profiles with academic information
- **Role-Specific Fields**: Different profile fields for lecturers and students
- **Profile Completion**: Automatic calculation of profile completion percentage
- **Change Tracking**: Full audit trail of profile modifications
- **Academic Information**: Employee IDs, student IDs, departments, qualifications, etc.

### 🏢 Department & Course Management
- **Department Management**: Create and manage academic departments
- **Course Management**: Manage courses with lecturer assignments and student enrollments
- **Statistics Tracking**: Automatic calculation of department and course statistics
- **Relationship Management**: Handle complex relationships between users, departments, and courses

### 📈 Analytics & Reporting
- **User Statistics**: Comprehensive statistics on user distribution and activity
- **Profile Analytics**: Profile completion rates and demographic analysis
- **Audit Logs**: Detailed audit trails for compliance and security
- **Real-time Monitoring**: Health checks and performance monitoring

## Architecture

### Technology Stack
- **Framework**: Django REST Framework
- **Database**: MongoDB (shared with Authentication Service)
- **Task Queue**: Celery with Redis
- **Authentication**: JWT tokens from Authentication Service
- **API Documentation**: Swagger/OpenAPI

### Key Components

#### Models
- **User**: Local representation of users synced from auth service
- **UserProfile**: Extended profile information with academic details
- **Department**: Academic department management
- **Course**: Course management with lecturer and student assignments
- **UserActivity**: Activity tracking and audit logging
- **BulkOperation**: Bulk operation management and progress tracking

#### Services
- **User Management**: CRUD operations with role-based permissions
- **Profile Management**: Extended profile handling with change tracking
- **Bulk Operations**: Asynchronous processing of bulk operations
- **Inter-Service Communication**: Seamless integration with auth service

#### Security
- **JWT Authentication**: Token-based authentication from auth service
- **Role-Based Permissions**: Granular permission system
- **Audit Logging**: Comprehensive audit trails
- **Input Validation**: Strict validation of all inputs

## API Endpoints

### User Management
```
GET    /api/users/                    # List users (admin only)
POST   /api/users/                    # Create user (admin only)
GET    /api/users/{id}/               # Get user details
PATCH  /api/users/{id}/               # Update user
DELETE /api/users/{id}/               # Delete user
POST   /api/users/{id}/block/         # Block user
POST   /api/users/{id}/unblock/       # Unblock user
POST   /api/users/{id}/verify/        # Verify user email
GET    /api/users/{id}/activities/    # Get user activities
```

### Bulk Operations
```
POST   /api/users/bulk-update/        # Bulk update users
POST   /api/users/import-users/       # Import users from file
GET    /api/users/export/             # Export users to file
GET    /api/users/bulk-operations/    # List bulk operations
```

### Profile Management
```
GET    /api/profiles/me/              # Get current user's profile
GET    /api/profiles/                 # List profiles (admin only)
GET    /api/profiles/{id}/            # Get profile details
PATCH  /api/profiles/{id}/            # Update profile
GET    /api/profiles/{id}/history/    # Get profile change history
POST   /api/profiles/bulk-update/     # Bulk update profiles
```

### Department & Course Management
```
GET    /api/profiles/departments/     # List departments
POST   /api/profiles/departments/     # Create department
GET    /api/profiles/courses/         # List courses
POST   /api/profiles/courses/         # Create course
```

## Role-Based Access Control

### Superadmin
- Full access to all operations
- Can manage admin users
- Cannot create other superadmin users

### Admin
- Can manage lecturers and students
- Cannot manage other admins or superadmins
- Full access to bulk operations and analytics

### Lecturer
- Can view and update own profile
- Limited access to course information
- Cannot manage other users

### Student
- Can view and update own profile
- Limited access to course enrollment information
- Cannot manage other users

## Development

### Setup
```bash
# Install dependencies
pip install -r requirements.txt

# Run migrations
python manage.py migrate

# Create superuser
python manage.py createsuperuser

# Run development server
python manage.py runserver 0.0.0.0:8002
```

### Testing
```bash
# Run all tests
python manage.py test

# Run specific test modules
python manage.py test users.tests
python manage.py test profiles.tests

# Run with coverage
coverage run --source='.' manage.py test
coverage report
```

### Celery Workers
```bash
# Start Celery worker
celery -A user_service worker --loglevel=info

# Start Celery beat scheduler
celery -A user_service beat --loglevel=info

# Monitor Celery tasks
celery -A user_service flower
```

## Configuration

### Environment Variables
- `MONGODB_URI`: MongoDB connection string
- `REDIS_URL`: Redis connection string
- `AUTH_SERVICE_URL`: Authentication service URL
- `AUTH_SERVICE_INTERNAL_TOKEN`: Internal service token
- `JWT_SECRET_KEY`: JWT signing key
- `SECRET_KEY`: Django secret key

### Inter-Service Communication
The service communicates with the Authentication Service for:
- User creation and updates
- Token validation
- Role synchronization
- User status changes

## Monitoring & Logging

### Health Checks
- `/api/health/`: Service health status
- Database connectivity checks
- Redis connectivity checks

### Metrics
- User registration rates
- Profile completion rates
- Bulk operation success rates
- API response times

### Logging
- All user operations are logged
- Audit trails for compliance
- Error tracking and alerting
- Performance monitoring

## Security Considerations

1. **Authentication**: All endpoints require valid JWT tokens
2. **Authorization**: Role-based access control for all operations
3. **Input Validation**: Strict validation of all inputs
4. **Audit Logging**: Comprehensive audit trails
5. **Data Protection**: Sensitive data handling and encryption
6. **Rate Limiting**: API rate limiting to prevent abuse

## Future Enhancements

1. **Advanced Analytics**: More detailed user behavior analytics
2. **Notification System**: Integration with notification service
3. **File Management**: Enhanced file upload and management
4. **API Versioning**: Support for multiple API versions
5. **Caching**: Redis-based caching for improved performance
6. **Search**: Advanced search and filtering capabilities
