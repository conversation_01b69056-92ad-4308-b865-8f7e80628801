"""
Django admin configuration for core app.
"""
from django.contrib import admin
from core.models import Role, Permission


@admin.register(Role)
class RoleAdmin(admin.ModelAdmin):
    """
    Admin interface for Role model.
    """
    list_display = ['name', 'display_name', 'description', 'is_active', 'created_at']
    list_filter = ['name', 'is_active', 'created_at']
    search_fields = ['name', 'display_name', 'description']
    readonly_fields = ['id', 'created_at', 'updated_at']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'display_name', 'description')
        }),
        ('Permissions', {
            'fields': ('permissions',)
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
        ('System', {
            'fields': ('id', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_readonly_fields(self, request, obj=None):
        # Make name field readonly for existing objects
        if obj:
            return self.readonly_fields + ('name',)
        return self.readonly_fields

    def has_add_permission(self, request):
        """Roles are synced from auth service."""
        return False

    def has_delete_permission(self, request, obj=None):
        """Roles are synced from auth service."""
        return False


@admin.register(Permission)
class PermissionAdmin(admin.ModelAdmin):
    """
    Admin interface for Permission model.
    """
    list_display = ['name', 'resource', 'action', 'description', 'is_active', 'created_at']
    list_filter = ['resource', 'action', 'is_active', 'created_at']
    search_fields = ['name', 'resource', 'action', 'description']
    readonly_fields = ['id', 'created_at', 'updated_at']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'description')
        }),
        ('Permission Details', {
            'fields': ('resource', 'action')
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
        ('System', {
            'fields': ('id', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_readonly_fields(self, request, obj=None):
        # Make name field readonly for existing objects
        if obj:
            return self.readonly_fields + ('name',)
        return self.readonly_fields

    def has_add_permission(self, request):
        """Permissions are synced from auth service."""
        return False

    def has_delete_permission(self, request, obj=None):
        """Permissions are synced from auth service."""
        return False
