"""
Custom authentication classes for inter-service communication.
"""
import requests
from django.conf import settings
from django.contrib.auth.models import AnonymousUser
from rest_framework import authentication, exceptions
from rest_framework_simplejwt.authentication import J<PERSON>TAuthentication as BaseJWTAuthentication
from rest_framework_simplejwt.exceptions import InvalidToken, TokenError
from rest_framework_simplejwt.tokens import AccessToken
from users.models import User
import logging

logger = logging.getLogger(__name__)


class JWTAuthentication(BaseJWTAuthentication):
    """
    Custom JWT authentication that validates tokens with the auth service.
    """

    def authenticate(self, request):
        """
        Authenticate the request and return a two-tuple of (user, token).
        """
        header = self.get_header(request)
        if header is None:
            return None

        raw_token = self.get_raw_token(header)
        if raw_token is None:
            return None

        try:
            # Validate token with auth service
            validated_token = self.get_validated_token(raw_token)
            user = self.get_user(validated_token)
            return (user, validated_token)
        except TokenError:
            return None

    def get_validated_token(self, raw_token):
        """
        Validate the token with the auth service.
        """
        try:
            # First try to validate locally
            token = AccessToken(raw_token)
            
            # Then validate with auth service for additional security
            auth_service_url = f"{settings.AUTH_SERVICE_URL}/api/auth/token/validate/"
            response = requests.post(
                auth_service_url,
                json={'token': str(raw_token)},
                timeout=5
            )
            
            if response.status_code != 200:
                raise InvalidToken('Token validation failed with auth service')
            
            return token
            
        except requests.RequestException as e:
            logger.warning(f"Auth service validation failed: {e}")
            # Fall back to local validation only
            return AccessToken(raw_token)
        except (InvalidToken, TokenError) as e:
            raise InvalidToken(f'Token is invalid or expired: {e}')

    def get_user(self, validated_token):
        """
        Get or create user from token claims.
        """
        try:
            user_id = validated_token.get('user_id')
            if not user_id:
                raise InvalidToken('Token contained no recognizable user identification')

            # Try to get user from local database
            try:
                user = User.objects.get(id=user_id, is_active=True)
                return user
            except User.DoesNotExist:
                # Fetch user data from auth service
                user_data = self.fetch_user_from_auth_service(user_id)
                if user_data:
                    user = User.objects.create_from_auth_service(user_data)
                    return user
                else:
                    raise InvalidToken('User not found')

        except Exception as e:
            logger.error(f"Error getting user from token: {e}")
            raise InvalidToken('Invalid user in token')

    def fetch_user_from_auth_service(self, user_id):
        """
        Fetch user data from the auth service.
        """
        try:
            auth_service_url = f"{settings.AUTH_SERVICE_URL}/api/users/{user_id}/"
            headers = {
                'Authorization': f'Bearer {settings.AUTH_SERVICE_INTERNAL_TOKEN}'
            }
            response = requests.get(auth_service_url, headers=headers, timeout=5)
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.warning(f"Failed to fetch user {user_id} from auth service: {response.status_code}")
                return None
                
        except requests.RequestException as e:
            logger.error(f"Error fetching user from auth service: {e}")
            return None


class InternalServiceAuthentication(authentication.BaseAuthentication):
    """
    Authentication for internal service-to-service communication.
    """

    def authenticate(self, request):
        """
        Authenticate internal service requests.
        """
        auth_header = request.META.get('HTTP_AUTHORIZATION')
        if not auth_header:
            return None

        try:
            auth_type, token = auth_header.split(' ', 1)
            if auth_type.lower() != 'internal':
                return None

            if token == settings.AUTH_SERVICE_INTERNAL_TOKEN:
                # Create a special internal user
                internal_user = InternalServiceUser()
                return (internal_user, token)
            else:
                raise exceptions.AuthenticationFailed('Invalid internal service token')

        except ValueError:
            raise exceptions.AuthenticationFailed('Invalid authorization header format')


class InternalServiceUser:
    """
    Special user class for internal service authentication.
    """
    
    def __init__(self):
        self.id = 'internal-service'
        self.is_authenticated = True
        self.is_active = True
        self.is_staff = True
        self.is_superuser = True
        self.username = 'internal-service'
        self.email = '<EMAIL>'

    def has_perm(self, perm, obj=None):
        return True

    def has_perms(self, perm_list, obj=None):
        return True

    def has_module_perms(self, module):
        return True

    def __str__(self):
        return 'Internal Service User'
