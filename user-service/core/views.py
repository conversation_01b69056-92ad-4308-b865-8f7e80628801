"""
Core views for shared functionality.
"""
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from django.http import JsonResponse
from django.conf import settings
from core.models import AuditLog
from users.models import User
from django.db.models import Count, Q
from django.utils import timezone
from datetime import timedelta
import logging

logger = logging.getLogger(__name__)


@api_view(['GET'])
@permission_classes([AllowAny])
def health_check(request):
    """
    Health check endpoint for monitoring and load balancers.
    """
    try:
        # Check database connectivity
        user_count = User.objects.count()
        
        return Response({
            'status': 'healthy',
            'service': 'user-management-service',
            'version': '1.0.0',
            'debug': settings.DEBUG,
            'database': 'connected',
            'user_count': user_count,
            'timestamp': timezone.now().isoformat(),
        }, status=status.HTTP_200_OK)
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return Response({
            'status': 'unhealthy',
            'service': 'user-management-service',
            'error': str(e),
            'timestamp': timezone.now().isoformat(),
        }, status=status.HTTP_503_SERVICE_UNAVAILABLE)


@api_view(['GET'])
@permission_classes([AllowAny])
def service_info(request):
    """
    Service information endpoint.
    """
    return Response({
        'service': 'User Management Service',
        'description': 'User management and administration service for the Timetable Management System',
        'version': '1.0.0',
        'features': [
            'User CRUD operations with role-based access control',
            'User profile management',
            'Role assignment and permission management',
            'User blocking/unblocking functionality',
            'Bulk user operations',
            'User statistics and analytics',
            'Audit logging for all operations',
        ],
        'endpoints': {
            'users': '/api/users/',
            'profiles': '/api/profiles/',
            'statistics': '/api/statistics/',
            'audit': '/api/audit/',
            'docs': '/api/docs/',
            'health': '/api/health/',
        },
        'authentication': 'JWT tokens from auth service',
        'permissions': 'Role-based access control',
    }, status=status.HTTP_200_OK)


@api_view(['GET'])
@permission_classes([AllowAny])
def statistics_view(request):
    """
    Get user statistics and analytics.
    """
    try:
        # Basic user statistics
        total_users = User.objects.filter(is_active=True).count()
        
        # Users by role
        users_by_role = User.objects.filter(is_active=True).values(
            'role__name', 'role__display_name'
        ).annotate(count=Count('id'))
        
        # Users by status
        verified_users = User.objects.filter(is_active=True, is_verified=True).count()
        blocked_users = User.objects.filter(is_blocked=True).count()
        
        # Recent activity (last 30 days)
        thirty_days_ago = timezone.now() - timedelta(days=30)
        recent_registrations = User.objects.filter(
            date_joined__gte=thirty_days_ago,
            is_active=True
        ).count()
        
        # Recent audit logs
        recent_activities = AuditLog.objects.filter(
            created_at__gte=thirty_days_ago
        ).values('action').annotate(count=Count('id'))
        
        return Response({
            'total_users': total_users,
            'users_by_role': list(users_by_role),
            'verified_users': verified_users,
            'blocked_users': blocked_users,
            'recent_registrations': recent_registrations,
            'recent_activities': list(recent_activities),
            'generated_at': timezone.now().isoformat(),
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Error generating statistics: {str(e)}")
        return Response({
            'error': 'Failed to generate statistics',
            'message': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def audit_logs_view(request):
    """
    Get audit logs (admin only).
    """
    from core.permissions import IsAdmin
    
    # Check permissions
    if not IsAdmin().has_permission(request, None):
        return Response({
            'error': 'Permission denied'
        }, status=status.HTTP_403_FORBIDDEN)
    
    try:
        # Get query parameters
        user_id = request.GET.get('user_id')
        action = request.GET.get('action')
        days = int(request.GET.get('days', 30))
        
        # Build query
        queryset = AuditLog.objects.all()
        
        if user_id:
            queryset = queryset.filter(user_id=user_id)
        
        if action:
            queryset = queryset.filter(action=action)
        
        # Filter by date range
        start_date = timezone.now() - timedelta(days=days)
        queryset = queryset.filter(created_at__gte=start_date)
        
        # Order by most recent
        queryset = queryset.order_by('-created_at')
        
        # Paginate
        page_size = int(request.GET.get('page_size', 50))
        page = int(request.GET.get('page', 1))
        start = (page - 1) * page_size
        end = start + page_size
        
        logs = queryset[start:end]
        total_count = queryset.count()
        
        # Serialize logs
        logs_data = []
        for log in logs:
            logs_data.append({
                'id': str(log.id),
                'user_id': str(log.user_id),
                'actor_id': str(log.actor_id),
                'action': log.action,
                'resource': log.resource,
                'details': log.details,
                'ip_address': log.ip_address,
                'created_at': log.created_at.isoformat(),
            })
        
        return Response({
            'logs': logs_data,
            'total_count': total_count,
            'page': page,
            'page_size': page_size,
            'has_next': end < total_count,
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Error fetching audit logs: {str(e)}")
        return Response({
            'error': 'Failed to fetch audit logs',
            'message': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
