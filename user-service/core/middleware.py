"""
Custom middleware for the user management service.
"""
import logging
from django.utils.deprecation import MiddlewareMixin
from django.contrib.auth.models import AnonymousUser
from core.authentication import JWTAuthentication
from core.models import AuditLog

logger = logging.getLogger(__name__)


class JWTAuthenticationMiddleware(MiddlewareMixin):
    """
    Middleware to handle JWT authentication for API requests.
    """

    def process_request(self, request):
        """
        Process the request and authenticate if JW<PERSON> token is present.
        """
        # Skip authentication for certain paths
        skip_paths = [
            '/admin/',
            '/api/docs/',
            '/api/redoc/',
            '/api/schema/',
            '/api/health/',
        ]
        
        if any(request.path.startswith(path) for path in skip_paths):
            return None

        # Try JWT authentication
        jwt_auth = JWTAuthentication()
        try:
            auth_result = jwt_auth.authenticate(request)
            if auth_result:
                user, token = auth_result
                request.user = user
                request.auth = token
            else:
                request.user = AnonymousUser()
                request.auth = None
        except Exception as e:
            logger.warning(f"JWT authentication failed: {e}")
            request.user = AnonymousUser()
            request.auth = None

        return None


class AuditLogMiddleware(MiddlewareMixin):
    """
    Middleware to log user management operations for audit purposes.
    """

    def process_response(self, request, response):
        """
        Log operations that modify user data.
        """
        # Only log for authenticated users and successful operations
        if (not hasattr(request, 'user') or 
            not request.user.is_authenticated or 
            response.status_code >= 400):
            return response

        # Only log for specific methods and paths
        if request.method not in ['POST', 'PUT', 'PATCH', 'DELETE']:
            return response

        audit_paths = [
            '/api/users/',
            '/api/profiles/',
        ]

        if not any(request.path.startswith(path) for path in audit_paths):
            return response

        try:
            # Extract operation details
            action = self.get_action_from_request(request)
            resource = self.get_resource_from_path(request.path)
            
            # Get client IP
            ip_address = self.get_client_ip(request)
            
            # Get user agent
            user_agent = request.META.get('HTTP_USER_AGENT', '')

            # Create audit log entry
            AuditLog.objects.create(
                user_id=getattr(request, 'target_user_id', None) or request.user.id,
                actor_id=request.user.id,
                action=action,
                resource=resource,
                details={
                    'method': request.method,
                    'path': request.path,
                    'status_code': response.status_code,
                    'query_params': dict(request.GET),
                },
                ip_address=ip_address,
                user_agent=user_agent[:500]  # Limit length
            )

        except Exception as e:
            logger.error(f"Failed to create audit log: {e}")

        return response

    def get_action_from_request(self, request):
        """
        Determine the action based on the request method and path.
        """
        method_action_map = {
            'POST': 'create',
            'PUT': 'update',
            'PATCH': 'update',
            'DELETE': 'delete',
        }
        
        action = method_action_map.get(request.method, 'unknown')
        
        # Check for specific actions in the path
        if 'block' in request.path:
            action = 'block'
        elif 'unblock' in request.path:
            action = 'unblock'
        elif 'role' in request.path:
            action = 'role_change'
        elif 'bulk' in request.path:
            action = 'bulk_operation'
            
        return action

    def get_resource_from_path(self, path):
        """
        Extract the resource type from the request path.
        """
        if '/users/' in path:
            return 'user'
        elif '/profiles/' in path:
            return 'profile'
        else:
            return 'unknown'

    def get_client_ip(self, request):
        """
        Get the client IP address from the request.
        """
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
