"""
Celery tasks for user management operations.
"""
from celery import shared_task
from django.conf import settings
from django.utils import timezone
from django.core.files.base import ContentFile
from users.models import User, BulkOperation, UserActivity
from core.models import Role
import requests
import logging
import csv
import io
import pandas as pd
from datetime import timedelta

logger = logging.getLogger(__name__)


@shared_task
def create_user_in_auth_service(user_data, created_by):
    """
    Create user in the auth service.
    """
    try:
        auth_service_url = f"{settings.AUTH_SERVICE_URL}/api/auth/register/"
        headers = {
            'Authorization': f'Internal {settings.AUTH_SERVICE_INTERNAL_TOKEN}',
            'Content-Type': 'application/json'
        }
        
        response = requests.post(
            auth_service_url,
            json=user_data,
            headers=headers,
            timeout=30
        )
        
        if response.status_code == 201:
            auth_user_data = response.json()
            # Sync the created user to local database
            user = User.objects.sync_from_auth_service(auth_user_data['user'])
            
            logger.info(f"User {user.email} created successfully in auth service")
            return f"User {user.email} created successfully"
        else:
            logger.error(f"Failed to create user in auth service: {response.status_code} - {response.text}")
            return f"Failed to create user: {response.text}"
            
    except requests.RequestException as e:
        logger.error(f"Error creating user in auth service: {str(e)}")
        return f"Error creating user: {str(e)}"
    except Exception as e:
        logger.error(f"Unexpected error creating user: {str(e)}")
        return f"Unexpected error: {str(e)}"


@shared_task
def update_user_in_auth_service(user_id, user_data, updated_by):
    """
    Update user in the auth service.
    """
    try:
        auth_service_url = f"{settings.AUTH_SERVICE_URL}/api/users/{user_id}/"
        headers = {
            'Authorization': f'Internal {settings.AUTH_SERVICE_INTERNAL_TOKEN}',
            'Content-Type': 'application/json'
        }
        
        response = requests.patch(
            auth_service_url,
            json=user_data,
            headers=headers,
            timeout=30
        )
        
        if response.status_code == 200:
            auth_user_data = response.json()
            # Sync the updated user to local database
            user = User.objects.sync_from_auth_service(auth_user_data)
            
            logger.info(f"User {user.email} updated successfully in auth service")
            return f"User {user.email} updated successfully"
        else:
            logger.error(f"Failed to update user in auth service: {response.status_code} - {response.text}")
            return f"Failed to update user: {response.text}"
            
    except requests.RequestException as e:
        logger.error(f"Error updating user in auth service: {str(e)}")
        return f"Error updating user: {str(e)}"
    except Exception as e:
        logger.error(f"Unexpected error updating user: {str(e)}")
        return f"Unexpected error: {str(e)}"


@shared_task
def delete_user_in_auth_service(user_id, deleted_by):
    """
    Delete user in the auth service.
    """
    try:
        auth_service_url = f"{settings.AUTH_SERVICE_URL}/api/users/{user_id}/"
        headers = {
            'Authorization': f'Internal {settings.AUTH_SERVICE_INTERNAL_TOKEN}'
        }
        
        response = requests.delete(
            auth_service_url,
            headers=headers,
            timeout=30
        )
        
        if response.status_code == 204:
            logger.info(f"User {user_id} deleted successfully in auth service")
            return f"User {user_id} deleted successfully"
        else:
            logger.error(f"Failed to delete user in auth service: {response.status_code} - {response.text}")
            return f"Failed to delete user: {response.text}"
            
    except requests.RequestException as e:
        logger.error(f"Error deleting user in auth service: {str(e)}")
        return f"Error deleting user: {str(e)}"
    except Exception as e:
        logger.error(f"Unexpected error deleting user: {str(e)}")
        return f"Unexpected error: {str(e)}"


@shared_task
def sync_user_data_from_auth_service():
    """
    Sync user data from auth service periodically.
    """
    try:
        auth_service_url = f"{settings.AUTH_SERVICE_URL}/api/users/"
        headers = {
            'Authorization': f'Internal {settings.AUTH_SERVICE_INTERNAL_TOKEN}'
        }
        
        # Get users that need syncing (not synced in last hour)
        one_hour_ago = timezone.now() - timedelta(hours=1)
        users_to_sync = User.objects.filter(last_synced_at__lt=one_hour_ago)[:100]
        
        synced_count = 0
        for user in users_to_sync:
            try:
                response = requests.get(
                    f"{auth_service_url}{user.id}/",
                    headers=headers,
                    timeout=10
                )
                
                if response.status_code == 200:
                    auth_user_data = response.json()
                    User.objects.sync_from_auth_service(auth_user_data)
                    synced_count += 1
                    
            except Exception as e:
                logger.warning(f"Failed to sync user {user.id}: {str(e)}")
                continue
        
        logger.info(f"Synced {synced_count} users from auth service")
        return f"Synced {synced_count} users"
        
    except Exception as e:
        logger.error(f"Error syncing users from auth service: {str(e)}")
        return f"Error syncing users: {str(e)}"


@shared_task
def process_bulk_operation(operation_id):
    """
    Process bulk operations asynchronously.
    """
    try:
        operation = BulkOperation.objects.get(id=operation_id)
        operation.status = 'processing'
        operation.started_at = timezone.now()
        operation.save()
        
        if operation.operation_type == 'import':
            result = process_user_import(operation)
        elif operation.operation_type == 'export':
            result = process_user_export(operation)
        elif operation.operation_type == 'bulk_update':
            result = process_bulk_update(operation)
        else:
            result = f"Unknown operation type: {operation.operation_type}"
        
        operation.status = 'completed'
        operation.completed_at = timezone.now()
        operation.save()
        
        logger.info(f"Bulk operation {operation_id} completed: {result}")
        return result
        
    except BulkOperation.DoesNotExist:
        logger.error(f"Bulk operation {operation_id} not found")
        return f"Operation {operation_id} not found"
    except Exception as e:
        try:
            operation = BulkOperation.objects.get(id=operation_id)
            operation.status = 'failed'
            operation.completed_at = timezone.now()
            operation.error_details.append(str(e))
            operation.save()
        except:
            pass
        
        logger.error(f"Error processing bulk operation {operation_id}: {str(e)}")
        return f"Error processing operation: {str(e)}"


def process_user_import(operation):
    """
    Process user import from file.
    """
    try:
        file_path = operation.input_file.path
        role_name = operation.operation_params.get('role_name')
        send_welcome_email = operation.operation_params.get('send_welcome_email', True)
        
        # Get role
        try:
            role = Role.objects.get(name=role_name, is_active=True)
        except Role.DoesNotExist:
            raise Exception(f"Role '{role_name}' not found")
        
        # Read file
        if file_path.endswith('.csv'):
            df = pd.read_csv(file_path)
        else:
            df = pd.read_excel(file_path)
        
        operation.total_records = len(df)
        operation.save()
        
        successful_imports = []
        failed_imports = []
        
        for index, row in df.iterrows():
            try:
                user_data = {
                    'email': row['email'],
                    'username': row.get('username', row['email'].split('@')[0]),
                    'first_name': row.get('first_name', ''),
                    'last_name': row.get('last_name', ''),
                    'phone_number': row.get('phone_number', ''),
                    'role_name': role_name,
                    'password': row.get('password', 'TempPassword123!'),
                    'password_confirm': row.get('password', 'TempPassword123!'),
                }
                
                # Create user in auth service
                result = create_user_in_auth_service(user_data, str(operation.initiated_by.id))
                successful_imports.append({
                    'row': index + 1,
                    'email': user_data['email'],
                    'result': result
                })
                operation.successful_records += 1
                
            except Exception as e:
                failed_imports.append({
                    'row': index + 1,
                    'email': row.get('email', 'Unknown'),
                    'error': str(e)
                })
                operation.failed_records += 1
            
            operation.processed_records += 1
            operation.save()
        
        # Create error file if there are failures
        if failed_imports:
            error_content = "Row,Email,Error\n"
            for failure in failed_imports:
                error_content += f"{failure['row']},{failure['email']},{failure['error']}\n"
            
            error_file = ContentFile(error_content.encode('utf-8'))
            operation.error_file.save(
                f"import_errors_{operation.id}.csv",
                error_file
            )
        
        return f"Import completed: {operation.successful_records} successful, {operation.failed_records} failed"
        
    except Exception as e:
        raise Exception(f"Import failed: {str(e)}")


def process_user_export(operation):
    """
    Process user export to file.
    """
    try:
        # Get users based on filters
        queryset = User.objects.filter(is_active=True).select_related('role')
        
        # Apply filters from operation params
        filters = operation.operation_params
        if filters.get('role_name'):
            queryset = queryset.filter(role__name=filters['role_name'])
        if filters.get('is_verified'):
            queryset = queryset.filter(is_verified=filters['is_verified'] == 'true')
        if filters.get('is_blocked'):
            queryset = queryset.filter(is_blocked=filters['is_blocked'] == 'true')
        
        # Create CSV content
        output = io.StringIO()
        writer = csv.writer(output)
        
        # Write header
        writer.writerow([
            'ID', 'Email', 'Username', 'First Name', 'Last Name',
            'Phone Number', 'Role', 'Is Verified', 'Is Blocked',
            'Date Joined', 'Last Login'
        ])
        
        # Write data
        for user in queryset:
            writer.writerow([
                str(user.id),
                user.email,
                user.username,
                user.first_name,
                user.last_name,
                user.phone_number,
                user.role.display_name if user.role else '',
                user.is_verified,
                user.is_blocked,
                user.date_joined.isoformat() if user.date_joined else '',
                user.last_login.isoformat() if user.last_login else '',
            ])
        
        # Save output file
        output_content = output.getvalue()
        output_file = ContentFile(output_content.encode('utf-8'))
        operation.output_file.save(
            f"user_export_{operation.id}.csv",
            output_file
        )
        
        operation.total_records = queryset.count()
        operation.processed_records = operation.total_records
        operation.successful_records = operation.total_records
        operation.save()
        
        return f"Export completed: {operation.total_records} users exported"
        
    except Exception as e:
        raise Exception(f"Export failed: {str(e)}")


def process_bulk_update(operation):
    """
    Process bulk update operations.
    """
    try:
        params = operation.operation_params
        user_ids = params.get('user_ids', [])
        action = params.get('action')
        role_name = params.get('role_name')
        
        operation.total_records = len(user_ids)
        operation.save()
        
        successful_updates = 0
        failed_updates = 0
        
        for user_id in user_ids:
            try:
                user = User.objects.get(id=user_id, is_active=True)
                
                if action == 'block':
                    user.block_user()
                    update_user_in_auth_service.delay(user_id, {'is_blocked': True}, str(operation.initiated_by.id))
                elif action == 'unblock':
                    user.unblock_user()
                    update_user_in_auth_service.delay(user_id, {'is_blocked': False}, str(operation.initiated_by.id))
                elif action == 'verify':
                    user.verify_email()
                    update_user_in_auth_service.delay(user_id, {'is_verified': True}, str(operation.initiated_by.id))
                elif action == 'update_role' and role_name:
                    role = Role.objects.get(name=role_name, is_active=True)
                    user.role = role
                    user.save()
                    update_user_in_auth_service.delay(user_id, {'role_name': role_name}, str(operation.initiated_by.id))
                elif action == 'delete':
                    user.soft_delete()
                    delete_user_in_auth_service.delay(user_id, str(operation.initiated_by.id))
                
                successful_updates += 1
                
            except Exception as e:
                logger.warning(f"Failed to update user {user_id}: {str(e)}")
                failed_updates += 1
            
            operation.processed_records += 1
            operation.save()
        
        operation.successful_records = successful_updates
        operation.failed_records = failed_updates
        operation.save()
        
        return f"Bulk update completed: {successful_updates} successful, {failed_updates} failed"
        
    except Exception as e:
        raise Exception(f"Bulk update failed: {str(e)}")


@shared_task
def cleanup_inactive_users():
    """
    Clean up inactive users and old records.
    """
    try:
        # Clean up old user activities (older than 90 days)
        ninety_days_ago = timezone.now() - timedelta(days=90)
        deleted_activities = UserActivity.objects.filter(created_at__lt=ninety_days_ago).delete()
        
        # Clean up old bulk operations (older than 30 days)
        thirty_days_ago = timezone.now() - timedelta(days=30)
        deleted_operations = BulkOperation.objects.filter(
            created_at__lt=thirty_days_ago,
            status__in=['completed', 'failed', 'cancelled']
        ).delete()
        
        logger.info(f"Cleanup completed: {deleted_activities[0]} activities, {deleted_operations[0]} operations")
        return f"Cleanup completed: {deleted_activities[0]} activities, {deleted_operations[0]} operations"
        
    except Exception as e:
        logger.error(f"Error during cleanup: {str(e)}")
        return f"Cleanup failed: {str(e)}"
