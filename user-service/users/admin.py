"""
Django admin configuration for users app.
"""
from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from users.models import User, UserActivity, BulkOperation
from core.models import AuditLog


@admin.register(User)
class UserAdmin(admin.ModelAdmin):
    """
    Admin interface for User model.
    """
    list_display = [
        'email', 'username', 'first_name', 'last_name', 
        'role_display', 'is_verified', 'is_blocked', 'is_active', 
        'date_joined', 'last_synced_at'
    ]
    list_filter = [
        'role', 'is_verified', 'is_blocked', 'is_active', 
        'date_joined', 'last_synced_at'
    ]
    search_fields = ['email', 'username', 'first_name', 'last_name']
    ordering = ['-date_joined']
    readonly_fields = [
        'id', 'date_joined', 'last_login', 'password_changed_at',
        'last_synced_at', 'sync_version'
    ]
    
    fieldsets = (
        (None, {
            'fields': ('email', 'username')
        }),
        ('Personal info', {
            'fields': ('first_name', 'last_name', 'phone_number')
        }),
        ('Role & Permissions', {
            'fields': ('role',)
        }),
        ('Status', {
            'fields': ('is_active', 'is_verified', 'is_blocked')
        }),
        ('Important dates', {
            'fields': ('date_joined', 'last_login', 'password_changed_at')
        }),
        ('Sync Information', {
            'fields': ('last_synced_at', 'sync_version'),
            'classes': ('collapse',)
        }),
        ('System', {
            'fields': ('id',),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('role')

    def role_display(self, obj):
        if obj.role:
            return obj.role.display_name
        return '-'
    role_display.short_description = 'Role'

    def has_add_permission(self, request):
        """Disable add permission as users are managed through auth service."""
        return False

    def has_delete_permission(self, request, obj=None):
        """Disable delete permission as users are managed through auth service."""
        return False

    actions = ['sync_with_auth_service', 'block_users', 'unblock_users']

    def sync_with_auth_service(self, request, queryset):
        """Sync selected users with auth service."""
        from users.tasks import sync_user_data_from_auth_service
        sync_user_data_from_auth_service.delay()
        self.message_user(request, 'User sync initiated.')
    sync_with_auth_service.short_description = 'Sync with auth service'

    def block_users(self, request, queryset):
        """Block selected users."""
        count = 0
        for user in queryset:
            if not user.is_blocked and user.role and user.role.name != 'superadmin':
                user.block_user()
                count += 1
        self.message_user(request, f'{count} users were blocked.')
    block_users.short_description = 'Block selected users'

    def unblock_users(self, request, queryset):
        """Unblock selected users."""
        count = 0
        for user in queryset:
            if user.is_blocked:
                user.unblock_user()
                count += 1
        self.message_user(request, f'{count} users were unblocked.')
    unblock_users.short_description = 'Unblock selected users'


@admin.register(UserActivity)
class UserActivityAdmin(admin.ModelAdmin):
    """
    Admin interface for UserActivity model.
    """
    list_display = [
        'user_email', 'activity_type', 'description', 
        'ip_address', 'created_at'
    ]
    list_filter = ['activity_type', 'created_at']
    search_fields = ['user__email', 'activity_type', 'description']
    readonly_fields = ['id', 'created_at', 'updated_at']
    ordering = ['-created_at']

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')

    def user_email(self, obj):
        return obj.user.email
    user_email.short_description = 'User Email'

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        return request.user.is_superuser


@admin.register(BulkOperation)
class BulkOperationAdmin(admin.ModelAdmin):
    """
    Admin interface for BulkOperation model.
    """
    list_display = [
        'operation_type', 'status', 'initiated_by_email',
        'total_records', 'successful_records', 'failed_records',
        'progress_display', 'created_at', 'completed_at'
    ]
    list_filter = ['operation_type', 'status', 'created_at']
    search_fields = ['initiated_by__email', 'operation_type']
    readonly_fields = [
        'id', 'created_at', 'updated_at', 'started_at', 'completed_at',
        'progress_percentage'
    ]
    ordering = ['-created_at']

    fieldsets = (
        ('Operation Details', {
            'fields': ('operation_type', 'status', 'initiated_by')
        }),
        ('Progress', {
            'fields': (
                'total_records', 'processed_records', 
                'successful_records', 'failed_records', 'progress_percentage'
            )
        }),
        ('Files', {
            'fields': ('input_file', 'output_file', 'error_file')
        }),
        ('Metadata', {
            'fields': ('operation_params', 'error_details'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'started_at', 'completed_at'),
            'classes': ('collapse',)
        }),
        ('System', {
            'fields': ('id',),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('initiated_by')

    def initiated_by_email(self, obj):
        return obj.initiated_by.email
    initiated_by_email.short_description = 'Initiated By'

    def progress_display(self, obj):
        percentage = obj.progress_percentage
        if percentage == 100:
            color = 'green'
        elif percentage > 50:
            color = 'orange'
        else:
            color = 'red'
        
        return format_html(
            '<span style="color: {};">{:.1f}%</span>',
            color,
            percentage
        )
    progress_display.short_description = 'Progress'

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        return request.user.is_superuser


@admin.register(AuditLog)
class AuditLogAdmin(admin.ModelAdmin):
    """
    Admin interface for AuditLog model.
    """
    list_display = [
        'user_id_short', 'actor_id_short', 'action', 
        'resource', 'ip_address', 'created_at'
    ]
    list_filter = ['action', 'resource', 'created_at']
    search_fields = ['user_id', 'actor_id', 'action', 'resource']
    readonly_fields = ['id', 'created_at', 'updated_at']
    ordering = ['-created_at']

    def user_id_short(self, obj):
        return str(obj.user_id)[:8] + '...'
    user_id_short.short_description = 'User ID'

    def actor_id_short(self, obj):
        return str(obj.actor_id)[:8] + '...'
    actor_id_short.short_description = 'Actor ID'

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        return request.user.is_superuser
