"""
Filters for user management.
"""
import django_filters
from django.db.models import Q
from users.models import User, BulkOperation
from core.models import Role


class UserFilter(django_filters.FilterSet):
    """
    Filter set for User model with comprehensive filtering options.
    """
    # Basic filters
    email = django_filters.CharFilter(lookup_expr='icontains')
    username = django_filters.CharFilter(lookup_expr='icontains')
    first_name = django_filters.CharFilter(lookup_expr='icontains')
    last_name = django_filters.CharFilter(lookup_expr='icontains')
    
    # Role filters
    role = django_filters.ModelChoiceFilter(queryset=Role.objects.filter(is_active=True))
    role_name = django_filters.CharFilter(field_name='role__name', lookup_expr='exact')
    
    # Status filters
    is_verified = django_filters.BooleanFilter()
    is_blocked = django_filters.BooleanFilter()
    is_active = django_filters.BooleanFilter()
    
    # Date filters
    date_joined_after = django_filters.DateTimeFilter(field_name='date_joined', lookup_expr='gte')
    date_joined_before = django_filters.DateTimeFilter(field_name='date_joined', lookup_expr='lte')
    last_login_after = django_filters.DateTimeFilter(field_name='last_login', lookup_expr='gte')
    last_login_before = django_filters.DateTimeFilter(field_name='last_login', lookup_expr='lte')
    
    # Custom filters
    search = django_filters.CharFilter(method='filter_search')
    department = django_filters.CharFilter(method='filter_department')
    faculty = django_filters.CharFilter(method='filter_faculty')
    
    class Meta:
        model = User
        fields = [
            'email', 'username', 'first_name', 'last_name',
            'role', 'role_name', 'is_verified', 'is_blocked', 'is_active',
            'date_joined_after', 'date_joined_before',
            'last_login_after', 'last_login_before',
            'search', 'department', 'faculty'
        ]

    def filter_search(self, queryset, name, value):
        """
        Global search across multiple fields.
        """
        if not value:
            return queryset
        
        return queryset.filter(
            Q(email__icontains=value) |
            Q(username__icontains=value) |
            Q(first_name__icontains=value) |
            Q(last_name__icontains=value) |
            Q(phone_number__icontains=value)
        )

    def filter_department(self, queryset, name, value):
        """
        Filter by department from user profile.
        """
        if not value:
            return queryset
        
        return queryset.filter(profile__department__icontains=value)

    def filter_faculty(self, queryset, name, value):
        """
        Filter by faculty from user profile.
        """
        if not value:
            return queryset
        
        return queryset.filter(profile__faculty__icontains=value)


class BulkOperationFilter(django_filters.FilterSet):
    """
    Filter set for BulkOperation model.
    """
    operation_type = django_filters.ChoiceFilter(choices=BulkOperation.OPERATION_CHOICES)
    status = django_filters.ChoiceFilter(choices=BulkOperation.STATUS_CHOICES)
    
    # Date filters
    created_after = django_filters.DateTimeFilter(field_name='created_at', lookup_expr='gte')
    created_before = django_filters.DateTimeFilter(field_name='created_at', lookup_expr='lte')
    completed_after = django_filters.DateTimeFilter(field_name='completed_at', lookup_expr='gte')
    completed_before = django_filters.DateTimeFilter(field_name='completed_at', lookup_expr='lte')
    
    # User filter
    initiated_by_email = django_filters.CharFilter(field_name='initiated_by__email', lookup_expr='icontains')

    class Meta:
        model = BulkOperation
        fields = [
            'operation_type', 'status',
            'created_after', 'created_before',
            'completed_after', 'completed_before',
            'initiated_by_email'
        ]
