"""
User models for the user management service.
"""
from django.contrib.auth.models import AbstractBaseUser, PermissionsMixin
from django.db import models
from django.utils import timezone
from django.core.validators import EmailValidator
from core.models import BaseModel, Role
import uuid


class UserManager(models.Manager):
    """
    Custom user manager for the User model.
    """

    def create_from_auth_service(self, user_data):
        """Create a user from auth service data."""
        # Extract role information
        role = None
        if user_data.get('role'):
            role_name = user_data['role'].get('name') if isinstance(user_data['role'], dict) else user_data['role']
            try:
                role = Role.objects.get(name=role_name)
            except Role.DoesNotExist:
                pass

        user = self.create(
            id=user_data['id'],
            email=user_data['email'],
            username=user_data['username'],
            first_name=user_data.get('first_name', ''),
            last_name=user_data.get('last_name', ''),
            phone_number=user_data.get('phone_number', ''),
            role=role,
            is_verified=user_data.get('is_verified', False),
            is_blocked=user_data.get('is_blocked', False),
            date_joined=user_data.get('date_joined', timezone.now()),
            last_login=user_data.get('last_login'),
            password_changed_at=user_data.get('password_changed_at', timezone.now()),
        )
        return user

    def sync_from_auth_service(self, user_data):
        """Update or create user from auth service data."""
        user_id = user_data['id']
        
        try:
            user = self.get(id=user_id)
            # Update existing user
            user.email = user_data['email']
            user.username = user_data['username']
            user.first_name = user_data.get('first_name', '')
            user.last_name = user_data.get('last_name', '')
            user.phone_number = user_data.get('phone_number', '')
            user.is_verified = user_data.get('is_verified', False)
            user.is_blocked = user_data.get('is_blocked', False)
            user.last_login = user_data.get('last_login')
            
            # Update role
            if user_data.get('role'):
                role_name = user_data['role'].get('name') if isinstance(user_data['role'], dict) else user_data['role']
                try:
                    role = Role.objects.get(name=role_name)
                    user.role = role
                except Role.DoesNotExist:
                    pass
            
            user.save()
            return user
            
        except self.model.DoesNotExist:
            return self.create_from_auth_service(user_data)


class User(AbstractBaseUser, PermissionsMixin, BaseModel):
    """
    User model for the user management service.
    This is a local representation that syncs with the auth service.
    """
    email = models.EmailField(
        unique=True,
        validators=[EmailValidator()],
        help_text="User's email address"
    )
    username = models.CharField(
        max_length=150,
        unique=True,
        help_text="Unique username for the user"
    )
    first_name = models.CharField(max_length=150, blank=True)
    last_name = models.CharField(max_length=150, blank=True)
    
    # Role-based access control
    role = models.ForeignKey(
        Role,
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        help_text="User's role in the system"
    )
    
    # Status fields
    is_staff = models.BooleanField(default=False)
    is_superuser = models.BooleanField(default=False)
    is_verified = models.BooleanField(default=False, help_text="Email verification status")
    is_blocked = models.BooleanField(default=False, help_text="User blocked status")
    
    # Timestamps
    date_joined = models.DateTimeField(default=timezone.now)
    last_login = models.DateTimeField(null=True, blank=True)
    password_changed_at = models.DateTimeField(default=timezone.now)
    
    # Additional fields
    phone_number = models.CharField(max_length=20, blank=True)
    
    # Sync tracking
    last_synced_at = models.DateTimeField(auto_now=True)
    sync_version = models.IntegerField(default=1)

    objects = UserManager()

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username', 'first_name', 'last_name']

    class Meta:
        db_table = 'users'
        verbose_name = 'User'
        verbose_name_plural = 'Users'
        indexes = [
            models.Index(fields=['email']),
            models.Index(fields=['username']),
            models.Index(fields=['role']),
            models.Index(fields=['is_active']),
            models.Index(fields=['is_verified']),
            models.Index(fields=['is_blocked']),
            models.Index(fields=['last_synced_at']),
        ]

    def __str__(self):
        return f"{self.get_full_name()} ({self.email})"

    def get_full_name(self):
        """Return the user's full name."""
        return f"{self.first_name} {self.last_name}".strip()

    def get_short_name(self):
        """Return the user's short name."""
        return self.first_name

    def has_role(self, role_name):
        """Check if user has a specific role."""
        return self.role and self.role.name == role_name

    def has_permission_for(self, permission):
        """Check if user has a specific permission through their role."""
        return self.role and self.role.has_permission(permission)

    def block_user(self):
        """Block the user."""
        self.is_blocked = True
        self.is_active = False
        self.save()

    def unblock_user(self):
        """Unblock the user."""
        self.is_blocked = False
        self.is_active = True
        self.save()

    def verify_email(self):
        """Mark user's email as verified."""
        self.is_verified = True
        self.save()

    def can_be_managed_by(self, manager_user):
        """Check if this user can be managed by the given manager user."""
        if not manager_user or not manager_user.role:
            return False

        manager_role = manager_user.role.name
        target_role = self.role.name if self.role else None

        # Superadmin can manage all roles
        if manager_role == 'superadmin':
            return True

        # Admin can manage lecturers and students, but not other admins or superadmins
        if manager_role == 'admin':
            return target_role in ['lecturer', 'student']

        return False


class UserActivity(BaseModel):
    """
    Track user activity and engagement.
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='activities')
    activity_type = models.CharField(max_length=50)
    description = models.TextField(blank=True)
    metadata = models.JSONField(default=dict)
    ip_address = models.GenericIPAddressField(null=True, blank=True)

    class Meta:
        db_table = 'user_activities'
        verbose_name = 'User Activity'
        verbose_name_plural = 'User Activities'
        indexes = [
            models.Index(fields=['user', 'activity_type']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"{self.user.email} - {self.activity_type}"


class BulkOperation(BaseModel):
    """
    Track bulk operations on users.
    """
    OPERATION_CHOICES = [
        ('import', 'Import Users'),
        ('export', 'Export Users'),
        ('bulk_update', 'Bulk Update'),
        ('bulk_delete', 'Bulk Delete'),
        ('bulk_block', 'Bulk Block'),
        ('bulk_unblock', 'Bulk Unblock'),
    ]

    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ]

    operation_type = models.CharField(max_length=20, choices=OPERATION_CHOICES)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    initiated_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='bulk_operations')
    
    # Operation details
    total_records = models.IntegerField(default=0)
    processed_records = models.IntegerField(default=0)
    successful_records = models.IntegerField(default=0)
    failed_records = models.IntegerField(default=0)
    
    # Files
    input_file = models.FileField(upload_to='bulk_operations/input/', null=True, blank=True)
    output_file = models.FileField(upload_to='bulk_operations/output/', null=True, blank=True)
    error_file = models.FileField(upload_to='bulk_operations/errors/', null=True, blank=True)
    
    # Metadata
    operation_params = models.JSONField(default=dict)
    error_details = models.JSONField(default=list)
    
    # Timestamps
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'bulk_operations'
        verbose_name = 'Bulk Operation'
        verbose_name_plural = 'Bulk Operations'
        indexes = [
            models.Index(fields=['operation_type', 'status']),
            models.Index(fields=['initiated_by']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"{self.operation_type} - {self.status}"

    @property
    def progress_percentage(self):
        """Calculate progress percentage."""
        if self.total_records == 0:
            return 0
        return (self.processed_records / self.total_records) * 100
