"""
Serializers for user management endpoints.
"""
from rest_framework import serializers
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from users.models import User, UserActivity, BulkOperation
from core.models import Role
import logging

logger = logging.getLogger(__name__)


class RoleSerializer(serializers.ModelSerializer):
    """
    Serializer for role information.
    """
    class Meta:
        model = Role
        fields = ['id', 'name', 'display_name', 'description', 'permissions']
        read_only_fields = ['id']


class UserListSerializer(serializers.ModelSerializer):
    """
    Serializer for user list view (minimal information).
    """
    role = serializers.CharField(source='role.display_name', read_only=True)
    role_name = serializers.CharField(source='role.name', read_only=True)
    full_name = serializers.CharField(source='get_full_name', read_only=True)

    class Meta:
        model = User
        fields = [
            'id', 'email', 'username', 'first_name', 'last_name', 'full_name',
            'role', 'role_name', 'is_verified', 'is_blocked', 'is_active',
            'date_joined', 'last_login'
        ]
        read_only_fields = ['id', 'date_joined', 'last_login']


class UserDetailSerializer(serializers.ModelSerializer):
    """
    Serializer for detailed user information.
    """
    role = RoleSerializer(read_only=True)
    role_name = serializers.CharField(write_only=True, required=False)
    full_name = serializers.CharField(source='get_full_name', read_only=True)
    can_be_managed = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = [
            'id', 'email', 'username', 'first_name', 'last_name', 'full_name',
            'phone_number', 'role', 'role_name', 'is_verified', 'is_blocked',
            'is_active', 'date_joined', 'last_login', 'password_changed_at',
            'last_synced_at', 'sync_version', 'can_be_managed'
        ]
        read_only_fields = [
            'id', 'date_joined', 'last_login', 'password_changed_at',
            'last_synced_at', 'sync_version'
        ]

    def get_can_be_managed(self, obj):
        """Check if the current user can manage this user."""
        request = self.context.get('request')
        if not request or not request.user.is_authenticated:
            return False
        return obj.can_be_managed_by(request.user)

    def validate_role_name(self, value):
        """Validate role assignment."""
        if not value:
            return value

        try:
            role = Role.objects.get(name=value, is_active=True)
            
            # Check if the current user can assign this role
            request = self.context.get('request')
            if request and request.user.is_authenticated:
                user_role = getattr(request.user, 'role', None)
                if user_role:
                    # Superadmin can assign any role except superadmin
                    if user_role.name == 'superadmin' and value == 'superadmin':
                        raise serializers.ValidationError("Cannot assign superadmin role")
                    
                    # Admin can only assign lecturer and student roles
                    elif user_role.name == 'admin' and value not in ['lecturer', 'student']:
                        raise serializers.ValidationError("Insufficient permissions to assign this role")
            
            return value
            
        except Role.DoesNotExist:
            raise serializers.ValidationError(f"Role '{value}' does not exist")

    def update(self, instance, validated_data):
        """Update user instance."""
        role_name = validated_data.pop('role_name', None)
        
        if role_name:
            try:
                role = Role.objects.get(name=role_name, is_active=True)
                validated_data['role'] = role
            except Role.DoesNotExist:
                pass

        return super().update(instance, validated_data)


class UserCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating new users.
    """
    role_name = serializers.CharField(write_only=True)
    password = serializers.CharField(write_only=True, validators=[validate_password])
    password_confirm = serializers.CharField(write_only=True)

    class Meta:
        model = User
        fields = [
            'email', 'username', 'first_name', 'last_name',
            'phone_number', 'role_name', 'password', 'password_confirm'
        ]

    def validate(self, attrs):
        """Validate user creation data."""
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError("Passwords don't match.")
        
        # Validate role exists and can be assigned
        role_name = attrs.get('role_name')
        if role_name:
            try:
                role = Role.objects.get(name=role_name, is_active=True)
                attrs['role'] = role
                
                # Check permissions
                request = self.context.get('request')
                if request and request.user.is_authenticated:
                    user_role = getattr(request.user, 'role', None)
                    if user_role:
                        # Superadmin can create any role except superadmin
                        if user_role.name == 'superadmin' and role_name == 'superadmin':
                            raise serializers.ValidationError("Cannot create superadmin users")
                        
                        # Admin can only create lecturer and student
                        elif user_role.name == 'admin' and role_name not in ['lecturer', 'student']:
                            raise serializers.ValidationError("Insufficient permissions to create this role")
                
            except Role.DoesNotExist:
                raise serializers.ValidationError(f"Role '{role_name}' does not exist")
        
        return attrs

    def create(self, validated_data):
        """Create a new user (this will be handled by the auth service)."""
        # Remove password fields as they're handled by auth service
        validated_data.pop('password')
        validated_data.pop('password_confirm')
        validated_data.pop('role_name', None)
        
        # This serializer is mainly for validation
        # The actual creation will be done via auth service API
        return validated_data


class UserActivitySerializer(serializers.ModelSerializer):
    """
    Serializer for user activity tracking.
    """
    user_email = serializers.CharField(source='user.email', read_only=True)
    user_name = serializers.CharField(source='user.get_full_name', read_only=True)

    class Meta:
        model = UserActivity
        fields = [
            'id', 'user', 'user_email', 'user_name', 'activity_type',
            'description', 'metadata', 'ip_address', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']


class BulkOperationSerializer(serializers.ModelSerializer):
    """
    Serializer for bulk operations.
    """
    initiated_by_email = serializers.CharField(source='initiated_by.email', read_only=True)
    initiated_by_name = serializers.CharField(source='initiated_by.get_full_name', read_only=True)
    progress_percentage = serializers.ReadOnlyField()
    duration = serializers.SerializerMethodField()

    class Meta:
        model = BulkOperation
        fields = [
            'id', 'operation_type', 'status', 'initiated_by', 'initiated_by_email',
            'initiated_by_name', 'total_records', 'processed_records',
            'successful_records', 'failed_records', 'progress_percentage',
            'input_file', 'output_file', 'error_file', 'operation_params',
            'error_details', 'created_at', 'started_at', 'completed_at', 'duration'
        ]
        read_only_fields = [
            'id', 'status', 'processed_records', 'successful_records',
            'failed_records', 'output_file', 'error_file', 'error_details',
            'created_at', 'started_at', 'completed_at'
        ]

    def get_duration(self, obj):
        """Calculate operation duration."""
        if obj.started_at and obj.completed_at:
            duration = obj.completed_at - obj.started_at
            return duration.total_seconds()
        return None


class UserBulkUpdateSerializer(serializers.Serializer):
    """
    Serializer for bulk user updates.
    """
    user_ids = serializers.ListField(
        child=serializers.UUIDField(),
        min_length=1,
        max_length=1000
    )
    action = serializers.ChoiceField(choices=[
        ('block', 'Block Users'),
        ('unblock', 'Unblock Users'),
        ('verify', 'Verify Users'),
        ('update_role', 'Update Role'),
        ('delete', 'Delete Users'),
    ])
    role_name = serializers.CharField(required=False)
    
    def validate(self, attrs):
        """Validate bulk update data."""
        action = attrs.get('action')
        role_name = attrs.get('role_name')
        
        if action == 'update_role' and not role_name:
            raise serializers.ValidationError("role_name is required for update_role action")
        
        if role_name:
            try:
                Role.objects.get(name=role_name, is_active=True)
            except Role.DoesNotExist:
                raise serializers.ValidationError(f"Role '{role_name}' does not exist")
        
        return attrs


class UserImportSerializer(serializers.Serializer):
    """
    Serializer for user import operations.
    """
    file = serializers.FileField()
    role_name = serializers.CharField()
    send_welcome_email = serializers.BooleanField(default=True)
    
    def validate_file(self, value):
        """Validate uploaded file."""
        if not value.name.endswith(('.csv', '.xlsx')):
            raise serializers.ValidationError("File must be CSV or Excel format")
        
        if value.size > 10 * 1024 * 1024:  # 10MB limit
            raise serializers.ValidationError("File size must be less than 10MB")
        
        return value
    
    def validate_role_name(self, value):
        """Validate role for imported users."""
        try:
            Role.objects.get(name=value, is_active=True)
            return value
        except Role.DoesNotExist:
            raise serializers.ValidationError(f"Role '{value}' does not exist")
