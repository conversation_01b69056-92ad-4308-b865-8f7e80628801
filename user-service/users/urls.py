"""
Users URLs configuration.
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

app_name = 'users'

# Create router for viewsets
router = DefaultRouter()
router.register(r'', views.UserViewSet, basename='user')
router.register(r'bulk-operations', views.BulkOperationViewSet, basename='bulk-operation')

urlpatterns = [
    # Internal sync endpoint
    path('sync/', views.sync_user, name='sync-user'),
    
    # ViewSet URLs
    path('', include(router.urls)),
]
