"""
User management views with comprehensive CRUD operations.
"""
from rest_framework import status, generics, permissions, filters
from rest_framework.decorators import api_view, permission_classes, action
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q
from django.conf import settings
from core.permissions import (
    IsAdmin, IsSuperAdmin, CanManageUserRole, CanCreateUserWithRole,
    IsInternalService
)
from users.models import User, UserActivity, BulkOperation
from users.serializers import (
    UserListSerializer, UserDetailSerializer, UserCreateSerializer,
    UserActivitySerializer, BulkOperationSerializer, UserBulkUpdateSerializer,
    UserImportSerializer
)
from users.filters import UserFilter
from users.tasks import (
    create_user_in_auth_service, update_user_in_auth_service,
    delete_user_in_auth_service, sync_user_data_from_auth_service,
    process_bulk_operation
)
import logging

logger = logging.getLogger(__name__)


class UserViewSet(ModelViewSet):
    """
    ViewSet for user management with comprehensive CRUD operations.
    """
    queryset = User.objects.filter(is_active=True).select_related('role')
    permission_classes = [permissions.IsAuthenticated, IsAdmin]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = UserFilter
    search_fields = ['email', 'username', 'first_name', 'last_name']
    ordering_fields = ['email', 'username', 'first_name', 'last_name', 'date_joined', 'last_login']
    ordering = ['-date_joined']

    def get_serializer_class(self):
        """Return appropriate serializer based on action."""
        if self.action == 'list':
            return UserListSerializer
        elif self.action == 'create':
            return UserCreateSerializer
        else:
            return UserDetailSerializer

    def get_permissions(self):
        """Get permissions based on action."""
        if self.action == 'create':
            permission_classes = [permissions.IsAuthenticated, CanCreateUserWithRole]
        elif self.action in ['update', 'partial_update', 'destroy']:
            permission_classes = [permissions.IsAuthenticated, CanManageUserRole]
        else:
            permission_classes = [permissions.IsAuthenticated, IsAdmin]
        
        return [permission() for permission in permission_classes]

    def get_queryset(self):
        """Filter queryset based on user permissions."""
        queryset = super().get_queryset()
        
        if not self.request.user.is_authenticated:
            return queryset.none()

        user_role = getattr(self.request.user, 'role', None)
        if not user_role:
            return queryset.none()

        # Superadmin can see all users
        if user_role.name == 'superadmin':
            return queryset

        # Admin can see lecturers and students
        elif user_role.name == 'admin':
            return queryset.filter(role__name__in=['lecturer', 'student'])

        # Others can't see any users through this endpoint
        else:
            return queryset.none()

    def perform_create(self, serializer):
        """Create user via auth service."""
        validated_data = serializer.validated_data
        
        # Create user in auth service asynchronously
        create_user_in_auth_service.delay(
            user_data=validated_data,
            created_by=str(self.request.user.id)
        )

    def perform_update(self, serializer):
        """Update user via auth service."""
        instance = serializer.save()
        
        # Update user in auth service asynchronously
        update_user_in_auth_service.delay(
            user_id=str(instance.id),
            user_data=serializer.validated_data,
            updated_by=str(self.request.user.id)
        )

    def perform_destroy(self, instance):
        """Soft delete user via auth service."""
        # Delete user in auth service asynchronously
        delete_user_in_auth_service.delay(
            user_id=str(instance.id),
            deleted_by=str(self.request.user.id)
        )
        
        # Soft delete locally
        instance.soft_delete()

    @action(detail=True, methods=['post'])
    def block(self, request, pk=None):
        """Block a user."""
        user = self.get_object()
        
        # Check if user can be managed
        if not user.can_be_managed_by(request.user):
            return Response({
                'error': 'You do not have permission to block this user'
            }, status=status.HTTP_403_FORBIDDEN)

        # Prevent blocking superadmins
        if user.role and user.role.name == 'superadmin':
            return Response({
                'error': 'Cannot block superadmin users'
            }, status=status.HTTP_403_FORBIDDEN)

        user.block_user()
        
        # Update in auth service
        update_user_in_auth_service.delay(
            user_id=str(user.id),
            user_data={'is_blocked': True},
            updated_by=str(request.user.id)
        )

        # Log activity
        UserActivity.objects.create(
            user=user,
            activity_type='blocked',
            description=f'User blocked by {request.user.get_full_name()}',
            metadata={'blocked_by': str(request.user.id)},
            ip_address=self.get_client_ip(request)
        )

        return Response({
            'message': f'User {user.email} has been blocked'
        }, status=status.HTTP_200_OK)

    @action(detail=True, methods=['post'])
    def unblock(self, request, pk=None):
        """Unblock a user."""
        user = self.get_object()
        
        # Check if user can be managed
        if not user.can_be_managed_by(request.user):
            return Response({
                'error': 'You do not have permission to unblock this user'
            }, status=status.HTTP_403_FORBIDDEN)

        user.unblock_user()
        
        # Update in auth service
        update_user_in_auth_service.delay(
            user_id=str(user.id),
            user_data={'is_blocked': False},
            updated_by=str(request.user.id)
        )

        # Log activity
        UserActivity.objects.create(
            user=user,
            activity_type='unblocked',
            description=f'User unblocked by {request.user.get_full_name()}',
            metadata={'unblocked_by': str(request.user.id)},
            ip_address=self.get_client_ip(request)
        )

        return Response({
            'message': f'User {user.email} has been unblocked'
        }, status=status.HTTP_200_OK)

    @action(detail=True, methods=['post'])
    def verify(self, request, pk=None):
        """Verify a user's email."""
        user = self.get_object()
        
        # Check if user can be managed
        if not user.can_be_managed_by(request.user):
            return Response({
                'error': 'You do not have permission to verify this user'
            }, status=status.HTTP_403_FORBIDDEN)

        user.verify_email()
        
        # Update in auth service
        update_user_in_auth_service.delay(
            user_id=str(user.id),
            user_data={'is_verified': True},
            updated_by=str(request.user.id)
        )

        # Log activity
        UserActivity.objects.create(
            user=user,
            activity_type='verified',
            description=f'User verified by {request.user.get_full_name()}',
            metadata={'verified_by': str(request.user.id)},
            ip_address=self.get_client_ip(request)
        )

        return Response({
            'message': f'User {user.email} has been verified'
        }, status=status.HTTP_200_OK)

    @action(detail=True, methods=['get'])
    def activities(self, request, pk=None):
        """Get user activities."""
        user = self.get_object()
        
        activities = UserActivity.objects.filter(user=user).order_by('-created_at')
        
        # Paginate
        page = self.paginate_queryset(activities)
        if page is not None:
            serializer = UserActivitySerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = UserActivitySerializer(activities, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['post'])
    def bulk_update(self, request):
        """Perform bulk operations on users."""
        serializer = UserBulkUpdateSerializer(data=request.data)
        if serializer.is_valid():
            # Create bulk operation record
            bulk_op = BulkOperation.objects.create(
                operation_type='bulk_update',
                initiated_by=request.user,
                total_records=len(serializer.validated_data['user_ids']),
                operation_params=serializer.validated_data
            )
            
            # Process bulk operation asynchronously
            process_bulk_operation.delay(bulk_op.id)
            
            return Response({
                'message': 'Bulk operation initiated',
                'operation_id': str(bulk_op.id)
            }, status=status.HTTP_202_ACCEPTED)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'])
    def import_users(self, request):
        """Import users from file."""
        serializer = UserImportSerializer(data=request.data)
        if serializer.is_valid():
            # Create bulk operation record
            bulk_op = BulkOperation.objects.create(
                operation_type='import',
                initiated_by=request.user,
                input_file=serializer.validated_data['file'],
                operation_params={
                    'role_name': serializer.validated_data['role_name'],
                    'send_welcome_email': serializer.validated_data['send_welcome_email']
                }
            )
            
            # Process import asynchronously
            process_bulk_operation.delay(bulk_op.id)
            
            return Response({
                'message': 'User import initiated',
                'operation_id': str(bulk_op.id)
            }, status=status.HTTP_202_ACCEPTED)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'])
    def export(self, request):
        """Export users to file."""
        # Create bulk operation record
        bulk_op = BulkOperation.objects.create(
            operation_type='export',
            initiated_by=request.user,
            operation_params=dict(request.GET)
        )
        
        # Process export asynchronously
        process_bulk_operation.delay(bulk_op.id)
        
        return Response({
            'message': 'User export initiated',
            'operation_id': str(bulk_op.id)
        }, status=status.HTTP_202_ACCEPTED)

    def get_client_ip(self, request):
        """Get client IP address."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


@api_view(['POST'])
@permission_classes([IsInternalService])
def sync_user(request):
    """
    Sync user data from auth service (internal endpoint).
    """
    user_data = request.data
    
    try:
        user = User.objects.sync_from_auth_service(user_data)
        serializer = UserDetailSerializer(user)
        return Response(serializer.data, status=status.HTTP_200_OK)
    except Exception as e:
        logger.error(f"Error syncing user: {str(e)}")
        return Response({
            'error': 'Failed to sync user',
            'message': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class BulkOperationViewSet(ModelViewSet):
    """
    ViewSet for managing bulk operations.
    """
    queryset = BulkOperation.objects.all().select_related('initiated_by')
    serializer_class = BulkOperationSerializer
    permission_classes = [permissions.IsAuthenticated, IsAdmin]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['operation_type', 'status']
    ordering = ['-created_at']

    def get_queryset(self):
        """Filter queryset based on user permissions."""
        queryset = super().get_queryset()
        
        # Users can only see their own operations unless they're superadmin
        if not self.request.user.role or self.request.user.role.name != 'superadmin':
            queryset = queryset.filter(initiated_by=self.request.user)
        
        return queryset

    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        """Cancel a bulk operation."""
        operation = self.get_object()
        
        if operation.status not in ['pending', 'processing']:
            return Response({
                'error': 'Operation cannot be cancelled'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        operation.status = 'cancelled'
        operation.save()
        
        return Response({
            'message': 'Operation cancelled'
        }, status=status.HTTP_200_OK)
