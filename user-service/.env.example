# User Management Service Environment Configuration

# Django Settings
DEBUG=True
SECRET_KEY=user-service-secret-key-development
ALLOWED_HOSTS=localhost,127.0.0.1,user-service

# Database Configuration
MONGODB_URI=*****************************************************************************
MONGODB_DB_NAME=timetable_system

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# JWT Configuration
JWT_SECRET_KEY=jwt-secret-key-development
JWT_ACCESS_TOKEN_LIFETIME=60
JWT_REFRESH_TOKEN_LIFETIME=7
JWT_ALGORITHM=HS256

# Auth Service Configuration
AUTH_SERVICE_URL=http://localhost:8001
AUTH_SERVICE_INTERNAL_TOKEN=internal-service-token

# CORS Configuration
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# Email Configuration
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
DEFAULT_FROM_EMAIL=<EMAIL>

# Frontend Configuration
FRONTEND_URL=http://localhost:3000

# Logging Configuration
LOG_LEVEL=INFO
