"""
Celery configuration for user_service.
"""
import os
from celery import Celery
from django.conf import settings

# Set the default Django settings module for the 'celery' program.
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'user_service.settings')

app = Celery('user_service')

# Using a string here means the worker doesn't have to serialize
# the configuration object to child processes.
app.config_from_object('django.conf:settings', namespace='CELERY')

# Load task modules from all registered Django apps.
app.autodiscover_tasks()

# Celery beat schedule for periodic tasks
app.conf.beat_schedule = {
    'sync-user-data': {
        'task': 'users.tasks.sync_user_data_from_auth_service',
        'schedule': 300.0,  # Run every 5 minutes
    },
    'cleanup-inactive-users': {
        'task': 'users.tasks.cleanup_inactive_users',
        'schedule': 86400.0,  # Run daily
    },
}

app.conf.timezone = 'UTC'


@app.task(bind=True)
def debug_task(self):
    print(f'Request: {self.request!r}')
