"""
Comprehensive tests for the user management service.
"""
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
from unittest.mock import patch, Mock
from users.models import User, UserActivity, BulkOperation
from profiles.models import UserProfile, Department, Course
from core.models import Role, Permission
import json


class UserManagementTestCase(APITestCase):
    """Test cases for user management endpoints."""

    def setUp(self):
        """Set up test data."""
        # Create roles
        self.superadmin_role = Role.objects.create(
            name='superadmin',
            display_name='Super Administrator',
            description='Super admin role',
            permissions=['*']
        )
        
        self.admin_role = Role.objects.create(
            name='admin',
            display_name='Administrator',
            description='Admin role',
            permissions=['manage_users', 'view_analytics']
        )
        
        self.lecturer_role = Role.objects.create(
            name='lecturer',
            display_name='Lecturer',
            description='Lecturer role',
            permissions=['manage_own_preferences', 'view_own_timetable']
        )
        
        self.student_role = Role.objects.create(
            name='student',
            display_name='Student',
            description='Student role',
            permissions=['manage_own_preferences', 'view_own_timetable']
        )

        # Create test users
        self.superadmin_user = User.objects.create(
            email='<EMAIL>',
            username='superadmin',
            first_name='Super',
            last_name='Admin',
            role=self.superadmin_role,
            is_verified=True
        )

        self.admin_user = User.objects.create(
            email='<EMAIL>',
            username='admin',
            first_name='Admin',
            last_name='User',
            role=self.admin_role,
            is_verified=True
        )

        self.lecturer_user = User.objects.create(
            email='<EMAIL>',
            username='lecturer',
            first_name='Lecturer',
            last_name='User',
            role=self.lecturer_role,
            is_verified=True
        )

        self.student_user = User.objects.create(
            email='<EMAIL>',
            username='student',
            first_name='Student',
            last_name='User',
            role=self.student_role,
            is_verified=True
        )

    def test_health_check(self):
        """Test health check endpoint."""
        url = reverse('core:health-check')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['status'], 'healthy')

    def test_service_info(self):
        """Test service info endpoint."""
        url = reverse('core:service-info')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('service', response.data)

    def test_user_list_admin_access(self):
        """Test user list access for admin."""
        self.client.force_authenticate(user=self.admin_user)
        url = reverse('users:user-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_user_list_lecturer_denied(self):
        """Test user list access denied for lecturer."""
        self.client.force_authenticate(user=self.lecturer_user)
        url = reverse('users:user-list')
        response = self.client.get(url)
        # Should return empty queryset or 403
        self.assertIn(response.status_code, [status.HTTP_200_OK, status.HTTP_403_FORBIDDEN])

    def test_user_detail_access(self):
        """Test user detail access."""
        self.client.force_authenticate(user=self.admin_user)
        url = reverse('users:user-detail', kwargs={'pk': self.lecturer_user.id})
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['email'], self.lecturer_user.email)

    @patch('users.tasks.update_user_in_auth_service.delay')
    def test_block_user(self, mock_task):
        """Test blocking a user."""
        self.client.force_authenticate(user=self.admin_user)
        url = reverse('users:user-block', kwargs={'pk': self.lecturer_user.id})
        response = self.client.post(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Check user is blocked
        self.lecturer_user.refresh_from_db()
        self.assertTrue(self.lecturer_user.is_blocked)
        
        # Check task was called
        mock_task.assert_called_once()

    @patch('users.tasks.update_user_in_auth_service.delay')
    def test_unblock_user(self, mock_task):
        """Test unblocking a user."""
        # First block the user
        self.lecturer_user.block_user()
        
        self.client.force_authenticate(user=self.admin_user)
        url = reverse('users:user-unblock', kwargs={'pk': self.lecturer_user.id})
        response = self.client.post(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Check user is unblocked
        self.lecturer_user.refresh_from_db()
        self.assertFalse(self.lecturer_user.is_blocked)
        
        # Check task was called
        mock_task.assert_called_once()

    def test_cannot_block_superadmin(self):
        """Test that superadmin cannot be blocked."""
        self.client.force_authenticate(user=self.admin_user)
        url = reverse('users:user-block', kwargs={'pk': self.superadmin_user.id})
        response = self.client.post(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_bulk_update_users(self):
        """Test bulk user operations."""
        self.client.force_authenticate(user=self.admin_user)
        url = reverse('users:user-bulk-update')
        data = {
            'user_ids': [str(self.lecturer_user.id), str(self.student_user.id)],
            'action': 'block'
        }
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_202_ACCEPTED)
        self.assertIn('operation_id', response.data)

    def test_statistics_view(self):
        """Test statistics endpoint."""
        self.client.force_authenticate(user=self.admin_user)
        url = reverse('core:statistics')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('total_users', response.data)


class UserProfileTestCase(APITestCase):
    """Test cases for user profile management."""

    def setUp(self):
        """Set up test data."""
        # Create role
        self.lecturer_role = Role.objects.create(
            name='lecturer',
            display_name='Lecturer',
            description='Lecturer role',
            permissions=['manage_own_preferences']
        )

        # Create user
        self.user = User.objects.create(
            email='<EMAIL>',
            username='lecturer',
            first_name='Test',
            last_name='Lecturer',
            role=self.lecturer_role,
            is_verified=True
        )

        # Create profile
        self.profile = UserProfile.objects.create(
            user=self.user,
            department='Computer Science',
            faculty='Engineering',
            employee_id='EMP001'
        )

    def test_my_profile_access(self):
        """Test accessing own profile."""
        self.client.force_authenticate(user=self.user)
        url = reverse('profiles:my-profile')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['user_email'], self.user.email)

    def test_profile_completion_calculation(self):
        """Test profile completion calculation."""
        completion = self.profile.calculate_profile_completion()
        self.assertGreater(completion, 0)
        self.assertLessEqual(completion, 100)

    def test_profile_update_with_history(self):
        """Test profile update with change tracking."""
        self.client.force_authenticate(user=self.user)
        url = reverse('profiles:profile-detail', kwargs={'pk': self.profile.id})
        data = {
            'department': 'Mathematics',
            'change_reason': 'Department transfer'
        }
        response = self.client.patch(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check profile was updated
        self.profile.refresh_from_db()
        self.assertEqual(self.profile.department, 'Mathematics')


class DepartmentTestCase(APITestCase):
    """Test cases for department management."""

    def setUp(self):
        """Set up test data."""
        # Create admin user
        self.admin_role = Role.objects.create(
            name='admin',
            display_name='Administrator',
            description='Admin role',
            permissions=['manage_users']
        )

        self.admin_user = User.objects.create(
            email='<EMAIL>',
            username='admin',
            first_name='Admin',
            last_name='User',
            role=self.admin_role,
            is_verified=True
        )

        # Create department
        self.department = Department.objects.create(
            name='Computer Science',
            code='CS',
            description='Computer Science Department',
            faculty='Engineering'
        )

    def test_department_list(self):
        """Test department list endpoint."""
        self.client.force_authenticate(user=self.admin_user)
        url = reverse('profiles:department-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_department_statistics_update(self):
        """Test department statistics update."""
        self.client.force_authenticate(user=self.admin_user)
        url = reverse('profiles:department-update-statistics', kwargs={'pk': self.department.id})
        response = self.client.post(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)


class CourseTestCase(APITestCase):
    """Test cases for course management."""

    def setUp(self):
        """Set up test data."""
        # Create admin user
        self.admin_role = Role.objects.create(
            name='admin',
            display_name='Administrator',
            description='Admin role',
            permissions=['manage_users']
        )

        self.admin_user = User.objects.create(
            email='<EMAIL>',
            username='admin',
            first_name='Admin',
            last_name='User',
            role=self.admin_role,
            is_verified=True
        )

        # Create lecturer
        self.lecturer_role = Role.objects.create(
            name='lecturer',
            display_name='Lecturer',
            description='Lecturer role',
            permissions=['manage_own_preferences']
        )

        self.lecturer = User.objects.create(
            email='<EMAIL>',
            username='lecturer',
            first_name='Test',
            last_name='Lecturer',
            role=self.lecturer_role,
            is_verified=True
        )

        # Create department and course
        self.department = Department.objects.create(
            name='Computer Science',
            code='CS',
            faculty='Engineering'
        )

        self.course = Course.objects.create(
            code='CS101',
            name='Introduction to Programming',
            department=self.department,
            credits=3,
            semester='Fall',
            year_level=1
        )

    def test_course_list(self):
        """Test course list endpoint."""
        self.client.force_authenticate(user=self.admin_user)
        url = reverse('profiles:course-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_assign_lecturer_to_course(self):
        """Test assigning lecturer to course."""
        self.client.force_authenticate(user=self.admin_user)
        url = reverse('profiles:course-assign-lecturer', kwargs={'pk': self.course.id})
        data = {'lecturer_id': str(self.lecturer.id)}
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check lecturer was assigned
        self.assertTrue(self.course.lecturers.filter(id=self.lecturer.id).exists())


class ModelTestCase(TestCase):
    """Test cases for model functionality."""

    def setUp(self):
        """Set up test data."""
        self.role = Role.objects.create(
            name='lecturer',
            display_name='Lecturer',
            description='Lecturer role',
            permissions=['manage_own_preferences', 'view_own_timetable']
        )

    def test_role_has_permission(self):
        """Test role permission checking."""
        self.assertTrue(self.role.has_permission('manage_own_preferences'))
        self.assertFalse(self.role.has_permission('manage_users'))

    def test_user_creation_from_auth_service(self):
        """Test user creation from auth service data."""
        user_data = {
            'id': '123e4567-e89b-12d3-a456-426614174000',
            'email': '<EMAIL>',
            'username': 'testuser',
            'first_name': 'Test',
            'last_name': 'User',
            'role': {'name': 'lecturer'},
            'is_verified': True,
            'is_blocked': False,
            'date_joined': '2023-01-01T00:00:00Z'
        }

        user = User.objects.create_from_auth_service(user_data)
        self.assertEqual(user.email, '<EMAIL>')
        self.assertEqual(user.role, self.role)

    def test_user_can_be_managed_by(self):
        """Test user management permissions."""
        # Create users
        admin_role = Role.objects.create(name='admin', display_name='Admin', permissions=['manage_users'])
        admin_user = User.objects.create(email='<EMAIL>', username='admin', role=admin_role)
        
        lecturer_user = User.objects.create(email='<EMAIL>', username='lecturer', role=self.role)
        
        # Admin can manage lecturer
        self.assertTrue(lecturer_user.can_be_managed_by(admin_user))
        
        # Lecturer cannot manage admin
        self.assertFalse(admin_user.can_be_managed_by(lecturer_user))

    def test_bulk_operation_progress(self):
        """Test bulk operation progress calculation."""
        user = User.objects.create(email='<EMAIL>', username='test', role=self.role)
        
        bulk_op = BulkOperation.objects.create(
            operation_type='import',
            initiated_by=user,
            total_records=100,
            processed_records=50
        )
        
        self.assertEqual(bulk_op.progress_percentage, 50.0)
