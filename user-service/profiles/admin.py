"""
Django admin configuration for profiles app.
"""
from django.contrib import admin
from django.utils.html import format_html
from profiles.models import UserProfile, Department, Course, ProfileHistory


@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    """
    Admin interface for UserProfile model.
    """
    list_display = [
        'user_email', 'user_name', 'user_role', 'department', 'faculty',
        'completion_display', 'created_at'
    ]
    list_filter = [
        'user__role', 'department', 'faculty', 'year_of_study',
        'profile_completion_percentage', 'created_at'
    ]
    search_fields = [
        'user__email', 'user__first_name', 'user__last_name',
        'employee_id', 'student_id', 'department', 'faculty'
    ]
    readonly_fields = ['id', 'created_at', 'updated_at', 'profile_completion_percentage']
    
    fieldsets = (
        ('User Information', {
            'fields': ('user',)
        }),
        ('Academic Information', {
            'fields': (
                'employee_id', 'student_id', 'department', 'faculty',
                'year_of_study', 'specialization'
            )
        }),
        ('Contact Information', {
            'fields': (
                'address', 'emergency_contact', 'emergency_contact_name',
                'emergency_contact_relationship'
            )
        }),
        ('Lecturer Details', {
            'fields': ('qualification', 'experience_years', 'research_interests'),
            'classes': ('collapse',)
        }),
        ('Student Details', {
            'fields': ('enrollment_year', 'expected_graduation', 'gpa'),
            'classes': ('collapse',)
        }),
        ('Preferences', {
            'fields': ('timezone', 'language', 'notification_preferences')
        }),
        ('Additional Information', {
            'fields': ('bio', 'website', 'social_links', 'avatar')
        }),
        ('System', {
            'fields': ('id', 'profile_completion_percentage', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user', 'user__role')

    def user_email(self, obj):
        return obj.user.email
    user_email.short_description = 'Email'

    def user_name(self, obj):
        return obj.user.get_full_name()
    user_name.short_description = 'Name'

    def user_role(self, obj):
        return obj.user.role.display_name if obj.user.role else '-'
    user_role.short_description = 'Role'

    def completion_display(self, obj):
        percentage = obj.profile_completion_percentage
        if percentage >= 80:
            color = 'green'
        elif percentage >= 50:
            color = 'orange'
        else:
            color = 'red'
        
        return format_html(
            '<span style="color: {};">{:.0f}%</span>',
            color,
            percentage
        )
    completion_display.short_description = 'Completion'

    actions = ['calculate_completion']

    def calculate_completion(self, request, queryset):
        """Recalculate profile completion for selected profiles."""
        count = 0
        for profile in queryset:
            profile.calculate_profile_completion()
            count += 1
        self.message_user(request, f'Recalculated completion for {count} profiles.')
    calculate_completion.short_description = 'Recalculate completion percentage'


@admin.register(Department)
class DepartmentAdmin(admin.ModelAdmin):
    """
    Admin interface for Department model.
    """
    list_display = [
        'name', 'code', 'faculty', 'head_name', 'total_lecturers',
        'total_students', 'created_at'
    ]
    list_filter = ['faculty', 'created_at']
    search_fields = ['name', 'code', 'description', 'faculty']
    readonly_fields = ['id', 'total_lecturers', 'total_students', 'created_at', 'updated_at']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'code', 'description', 'faculty')
        }),
        ('Management', {
            'fields': ('head_of_department',)
        }),
        ('Contact Information', {
            'fields': ('email', 'phone', 'office_location')
        }),
        ('Statistics', {
            'fields': ('total_lecturers', 'total_students'),
            'classes': ('collapse',)
        }),
        ('System', {
            'fields': ('id', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('head_of_department')

    def head_name(self, obj):
        return obj.head_of_department.get_full_name() if obj.head_of_department else '-'
    head_name.short_description = 'Head of Department'

    actions = ['update_statistics']

    def update_statistics(self, request, queryset):
        """Update statistics for selected departments."""
        count = 0
        for department in queryset:
            department.update_statistics()
            count += 1
        self.message_user(request, f'Updated statistics for {count} departments.')
    update_statistics.short_description = 'Update statistics'


@admin.register(Course)
class CourseAdmin(admin.ModelAdmin):
    """
    Admin interface for Course model.
    """
    list_display = [
        'code', 'name', 'department_name', 'credits', 'semester',
        'year_level', 'lecturer_count', 'student_count', 'created_at'
    ]
    list_filter = ['department', 'semester', 'year_level', 'credits', 'created_at']
    search_fields = ['code', 'name', 'description']
    readonly_fields = ['id', 'created_at', 'updated_at']
    filter_horizontal = ['prerequisites', 'lecturers', 'enrolled_students']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('code', 'name', 'description', 'department')
        }),
        ('Course Details', {
            'fields': ('credits', 'semester', 'year_level')
        }),
        ('Prerequisites', {
            'fields': ('prerequisites',)
        }),
        ('Assignments', {
            'fields': ('lecturers', 'enrolled_students')
        }),
        ('System', {
            'fields': ('id', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('department').prefetch_related(
            'lecturers', 'enrolled_students'
        )

    def department_name(self, obj):
        return obj.department.name
    department_name.short_description = 'Department'

    def lecturer_count(self, obj):
        return obj.lecturers.count()
    lecturer_count.short_description = 'Lecturers'

    def student_count(self, obj):
        return obj.enrolled_students.count()
    student_count.short_description = 'Students'


@admin.register(ProfileHistory)
class ProfileHistoryAdmin(admin.ModelAdmin):
    """
    Admin interface for ProfileHistory model.
    """
    list_display = [
        'profile_user', 'field_name', 'changed_by_name',
        'change_reason', 'created_at'
    ]
    list_filter = ['field_name', 'created_at']
    search_fields = [
        'profile__user__email', 'changed_by__email',
        'field_name', 'change_reason'
    ]
    readonly_fields = ['id', 'created_at', 'updated_at']
    
    fieldsets = (
        ('Change Information', {
            'fields': ('profile', 'changed_by', 'field_name', 'change_reason')
        }),
        ('Values', {
            'fields': ('old_value', 'new_value')
        }),
        ('System', {
            'fields': ('id', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'profile__user', 'changed_by'
        )

    def profile_user(self, obj):
        return obj.profile.user.email
    profile_user.short_description = 'Profile User'

    def changed_by_name(self, obj):
        return obj.changed_by.get_full_name()
    changed_by_name.short_description = 'Changed By'

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        return request.user.is_superuser
