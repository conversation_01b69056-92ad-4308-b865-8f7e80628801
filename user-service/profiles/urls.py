"""
Profiles URLs configuration.
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

app_name = 'profiles'

# Create router for viewsets
router = DefaultRouter()
router.register(r'', views.UserProfileViewSet, basename='profile')
router.register(r'departments', views.DepartmentViewSet, basename='department')
router.register(r'courses', views.CourseViewSet, basename='course')

urlpatterns = [
    # My profile endpoint
    path('me/', views.my_profile, name='my-profile'),
    
    # ViewSet URLs
    path('', include(router.urls)),
]
