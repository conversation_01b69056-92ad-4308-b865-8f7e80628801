"""
Serializers for profile management endpoints.
"""
from rest_framework import serializers
from profiles.models import UserProfile, Department, Course, ProfileHistory
from users.models import User


class UserProfileSerializer(serializers.ModelSerializer):
    """
    Serializer for user profile information.
    """
    user_email = serializers.CharField(source='user.email', read_only=True)
    user_name = serializers.CharField(source='user.get_full_name', read_only=True)
    user_role = serializers.CharField(source='user.role.display_name', read_only=True)
    academic_info = serializers.SerializerMethodField()
    completion_percentage = serializers.ReadOnlyField(source='profile_completion_percentage')

    class Meta:
        model = UserProfile
        fields = [
            'id', 'user', 'user_email', 'user_name', 'user_role',
            'employee_id', 'student_id', 'department', 'faculty',
            'year_of_study', 'specialization', 'address', 'emergency_contact',
            'emergency_contact_name', 'emergency_contact_relationship',
            'qualification', 'experience_years', 'research_interests',
            'enrollment_year', 'expected_graduation', 'gpa',
            'timezone', 'language', 'notification_preferences',
            'bio', 'website', 'social_links', 'avatar',
            'completion_percentage', 'academic_info',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def get_academic_info(self, obj):
        """Get role-specific academic information."""
        return obj.get_academic_info()

    def validate(self, attrs):
        """Validate profile data based on user role."""
        user = attrs.get('user') or self.instance.user
        
        if user and user.role:
            role_name = user.role.name
            
            # Validate lecturer-specific fields
            if role_name == 'lecturer':
                if not attrs.get('employee_id') and not (self.instance and self.instance.employee_id):
                    raise serializers.ValidationError("Employee ID is required for lecturers")
            
            # Validate student-specific fields
            elif role_name == 'student':
                if not attrs.get('student_id') and not (self.instance and self.instance.student_id):
                    raise serializers.ValidationError("Student ID is required for students")
                
                year_of_study = attrs.get('year_of_study')
                if year_of_study and (year_of_study < 1 or year_of_study > 10):
                    raise serializers.ValidationError("Year of study must be between 1 and 10")
        
        return attrs

    def update(self, instance, validated_data):
        """Update profile and calculate completion percentage."""
        profile = super().update(instance, validated_data)
        profile.calculate_profile_completion()
        return profile


class DepartmentSerializer(serializers.ModelSerializer):
    """
    Serializer for department information.
    """
    head_name = serializers.CharField(source='head_of_department.get_full_name', read_only=True)
    head_email = serializers.CharField(source='head_of_department.email', read_only=True)

    class Meta:
        model = Department
        fields = [
            'id', 'name', 'code', 'description', 'faculty',
            'head_of_department', 'head_name', 'head_email',
            'email', 'phone', 'office_location',
            'total_lecturers', 'total_students',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'total_lecturers', 'total_students', 'created_at', 'updated_at']


class CourseSerializer(serializers.ModelSerializer):
    """
    Serializer for course information.
    """
    department_name = serializers.CharField(source='department.name', read_only=True)
    lecturer_names = serializers.SerializerMethodField()
    total_enrolled = serializers.ReadOnlyField()
    total_lecturers = serializers.ReadOnlyField()

    class Meta:
        model = Course
        fields = [
            'id', 'code', 'name', 'description', 'department', 'department_name',
            'credits', 'semester', 'year_level', 'prerequisites',
            'lecturers', 'lecturer_names', 'enrolled_students',
            'total_enrolled', 'total_lecturers',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def get_lecturer_names(self, obj):
        """Get names of assigned lecturers."""
        return [lecturer.get_full_name() for lecturer in obj.lecturers.all()]


class ProfileHistorySerializer(serializers.ModelSerializer):
    """
    Serializer for profile history tracking.
    """
    changed_by_name = serializers.CharField(source='changed_by.get_full_name', read_only=True)
    changed_by_email = serializers.CharField(source='changed_by.email', read_only=True)

    class Meta:
        model = ProfileHistory
        fields = [
            'id', 'profile', 'changed_by', 'changed_by_name', 'changed_by_email',
            'field_name', 'old_value', 'new_value', 'change_reason',
            'created_at'
        ]
        read_only_fields = ['id', 'created_at']


class UserProfileUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for updating user profiles with change tracking.
    """
    change_reason = serializers.CharField(write_only=True, required=False)

    class Meta:
        model = UserProfile
        fields = [
            'employee_id', 'student_id', 'department', 'faculty',
            'year_of_study', 'specialization', 'address', 'emergency_contact',
            'emergency_contact_name', 'emergency_contact_relationship',
            'qualification', 'experience_years', 'research_interests',
            'enrollment_year', 'expected_graduation', 'gpa',
            'timezone', 'language', 'notification_preferences',
            'bio', 'website', 'social_links', 'change_reason'
        ]

    def update(self, instance, validated_data):
        """Update profile with change tracking."""
        change_reason = validated_data.pop('change_reason', '')
        changed_by = self.context['request'].user
        
        # Track changes
        for field, new_value in validated_data.items():
            old_value = getattr(instance, field)
            if old_value != new_value:
                ProfileHistory.objects.create(
                    profile=instance,
                    changed_by=changed_by,
                    field_name=field,
                    old_value=str(old_value) if old_value is not None else '',
                    new_value=str(new_value) if new_value is not None else '',
                    change_reason=change_reason
                )
        
        # Update profile
        profile = super().update(instance, validated_data)
        profile.calculate_profile_completion()
        return profile


class BulkProfileUpdateSerializer(serializers.Serializer):
    """
    Serializer for bulk profile updates.
    """
    user_ids = serializers.ListField(
        child=serializers.UUIDField(),
        min_length=1,
        max_length=1000
    )
    updates = serializers.DictField()
    change_reason = serializers.CharField(required=False)

    def validate_updates(self, value):
        """Validate update fields."""
        allowed_fields = [
            'department', 'faculty', 'timezone', 'language',
            'notification_preferences'
        ]
        
        for field in value.keys():
            if field not in allowed_fields:
                raise serializers.ValidationError(f"Field '{field}' is not allowed for bulk updates")
        
        return value
