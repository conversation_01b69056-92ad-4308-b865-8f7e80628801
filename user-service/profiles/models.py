"""
Extended user profile models for the user management service.
"""
from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from core.models import BaseModel
from users.models import User


class UserProfile(BaseModel):
    """
    Extended user profile information.
    """
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile')
    
    # Academic information
    employee_id = models.CharField(max_length=50, blank=True, unique=True, null=True)
    student_id = models.CharField(max_length=50, blank=True, unique=True, null=True)
    department = models.CharField(max_length=100, blank=True)
    faculty = models.CharField(max_length=100, blank=True)
    year_of_study = models.IntegerField(
        null=True, 
        blank=True, 
        validators=[MinValueValidator(1), MaxValueValidator(10)],
        help_text="For students"
    )
    specialization = models.CharField(max_length=100, blank=True)
    
    # Contact information
    address = models.TextField(blank=True)
    emergency_contact = models.CharField(max_length=20, blank=True)
    emergency_contact_name = models.CharField(max_length=100, blank=True)
    emergency_contact_relationship = models.CharField(max_length=50, blank=True)
    
    # Academic details for lecturers
    qualification = models.CharField(max_length=200, blank=True)
    experience_years = models.IntegerField(
        null=True, 
        blank=True,
        validators=[MinValueValidator(0), MaxValueValidator(50)]
    )
    research_interests = models.TextField(blank=True)
    
    # Academic details for students
    enrollment_year = models.IntegerField(
        null=True, 
        blank=True,
        validators=[MinValueValidator(2000), MaxValueValidator(2050)]
    )
    expected_graduation = models.IntegerField(
        null=True, 
        blank=True,
        validators=[MinValueValidator(2000), MaxValueValidator(2060)]
    )
    gpa = models.DecimalField(
        max_digits=3, 
        decimal_places=2, 
        null=True, 
        blank=True,
        validators=[MinValueValidator(0.0), MaxValueValidator(4.0)]
    )
    
    # Preferences
    timezone = models.CharField(max_length=50, default='UTC')
    language = models.CharField(max_length=10, default='en')
    notification_preferences = models.JSONField(
        default=dict,
        help_text="User's notification preferences"
    )
    
    # Additional metadata
    bio = models.TextField(blank=True, max_length=500)
    website = models.URLField(blank=True)
    social_links = models.JSONField(default=dict, blank=True)
    
    # Profile completion tracking
    profile_completion_percentage = models.IntegerField(
        default=0,
        validators=[MinValueValidator(0), MaxValueValidator(100)]
    )
    
    # Avatar/Profile picture
    avatar = models.ImageField(upload_to='avatars/', blank=True, null=True)

    class Meta:
        db_table = 'user_profiles'
        verbose_name = 'User Profile'
        verbose_name_plural = 'User Profiles'
        indexes = [
            models.Index(fields=['department']),
            models.Index(fields=['faculty']),
            models.Index(fields=['employee_id']),
            models.Index(fields=['student_id']),
            models.Index(fields=['year_of_study']),
            models.Index(fields=['enrollment_year']),
        ]

    def __str__(self):
        return f"Profile of {self.user.get_full_name()}"

    def calculate_profile_completion(self):
        """Calculate profile completion percentage."""
        total_fields = 0
        completed_fields = 0
        
        # Basic fields (always counted)
        basic_fields = [
            'department', 'faculty', 'address', 'emergency_contact',
            'bio', 'timezone', 'language'
        ]
        
        for field in basic_fields:
            total_fields += 1
            if getattr(self, field):
                completed_fields += 1
        
        # Role-specific fields
        if self.user.role:
            if self.user.role.name == 'lecturer':
                lecturer_fields = [
                    'employee_id', 'qualification', 'experience_years',
                    'research_interests'
                ]
                for field in lecturer_fields:
                    total_fields += 1
                    if getattr(self, field):
                        completed_fields += 1
            
            elif self.user.role.name == 'student':
                student_fields = [
                    'student_id', 'year_of_study', 'specialization',
                    'enrollment_year', 'expected_graduation'
                ]
                for field in student_fields:
                    total_fields += 1
                    if getattr(self, field):
                        completed_fields += 1
        
        # Calculate percentage
        if total_fields > 0:
            percentage = int((completed_fields / total_fields) * 100)
        else:
            percentage = 0
        
        # Update and save
        self.profile_completion_percentage = percentage
        self.save(update_fields=['profile_completion_percentage'])
        
        return percentage

    def get_academic_info(self):
        """Get role-specific academic information."""
        if not self.user.role:
            return {}
        
        if self.user.role.name == 'lecturer':
            return {
                'employee_id': self.employee_id,
                'qualification': self.qualification,
                'experience_years': self.experience_years,
                'research_interests': self.research_interests,
                'department': self.department,
                'faculty': self.faculty,
            }
        
        elif self.user.role.name == 'student':
            return {
                'student_id': self.student_id,
                'year_of_study': self.year_of_study,
                'specialization': self.specialization,
                'enrollment_year': self.enrollment_year,
                'expected_graduation': self.expected_graduation,
                'gpa': float(self.gpa) if self.gpa else None,
                'department': self.department,
                'faculty': self.faculty,
            }
        
        return {
            'department': self.department,
            'faculty': self.faculty,
        }


class ProfileHistory(BaseModel):
    """
    Track profile changes for audit purposes.
    """
    profile = models.ForeignKey(UserProfile, on_delete=models.CASCADE, related_name='history')
    changed_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='profile_changes')
    field_name = models.CharField(max_length=100)
    old_value = models.TextField(blank=True)
    new_value = models.TextField(blank=True)
    change_reason = models.CharField(max_length=200, blank=True)

    class Meta:
        db_table = 'profile_history'
        verbose_name = 'Profile History'
        verbose_name_plural = 'Profile Histories'
        indexes = [
            models.Index(fields=['profile', 'field_name']),
            models.Index(fields=['changed_by']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"{self.profile.user.email} - {self.field_name} changed"


class Department(BaseModel):
    """
    Department model for organizing users.
    """
    name = models.CharField(max_length=100, unique=True)
    code = models.CharField(max_length=10, unique=True)
    description = models.TextField(blank=True)
    faculty = models.CharField(max_length=100)
    head_of_department = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='headed_departments'
    )
    
    # Contact information
    email = models.EmailField(blank=True)
    phone = models.CharField(max_length=20, blank=True)
    office_location = models.CharField(max_length=200, blank=True)
    
    # Statistics
    total_lecturers = models.IntegerField(default=0)
    total_students = models.IntegerField(default=0)

    class Meta:
        db_table = 'departments'
        verbose_name = 'Department'
        verbose_name_plural = 'Departments'
        indexes = [
            models.Index(fields=['name']),
            models.Index(fields=['code']),
            models.Index(fields=['faculty']),
        ]

    def __str__(self):
        return f"{self.name} ({self.code})"

    def update_statistics(self):
        """Update department statistics."""
        self.total_lecturers = User.objects.filter(
            profile__department=self.name,
            role__name='lecturer',
            is_active=True
        ).count()
        
        self.total_students = User.objects.filter(
            profile__department=self.name,
            role__name='student',
            is_active=True
        ).count()
        
        self.save(update_fields=['total_lecturers', 'total_students'])


class Course(BaseModel):
    """
    Course model for academic courses.
    """
    code = models.CharField(max_length=20, unique=True)
    name = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    department = models.ForeignKey(Department, on_delete=models.CASCADE, related_name='courses')
    
    # Course details
    credits = models.IntegerField(validators=[MinValueValidator(1), MaxValueValidator(10)])
    semester = models.CharField(max_length=20)
    year_level = models.IntegerField(validators=[MinValueValidator(1), MaxValueValidator(10)])
    
    # Prerequisites
    prerequisites = models.ManyToManyField('self', blank=True, symmetrical=False)
    
    # Assigned lecturers
    lecturers = models.ManyToManyField(
        User, 
        blank=True, 
        related_name='assigned_courses',
        limit_choices_to={'role__name': 'lecturer'}
    )
    
    # Enrolled students
    enrolled_students = models.ManyToManyField(
        User,
        blank=True,
        related_name='enrolled_courses',
        limit_choices_to={'role__name': 'student'}
    )

    class Meta:
        db_table = 'courses'
        verbose_name = 'Course'
        verbose_name_plural = 'Courses'
        indexes = [
            models.Index(fields=['code']),
            models.Index(fields=['department']),
            models.Index(fields=['semester']),
            models.Index(fields=['year_level']),
        ]

    def __str__(self):
        return f"{self.code} - {self.name}"

    @property
    def total_enrolled(self):
        """Get total number of enrolled students."""
        return self.enrolled_students.count()

    @property
    def total_lecturers(self):
        """Get total number of assigned lecturers."""
        return self.lecturers.count()
