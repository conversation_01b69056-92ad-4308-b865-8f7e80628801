"""
Profile management views.
"""
from rest_framework import status, generics, permissions, filters
from rest_framework.decorators import api_view, permission_classes, action
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q, Count
from core.permissions import IsAdmin, IsOwnerOrReadOnly, CanManageUserRole
from profiles.models import UserProfile, Department, Course, ProfileHistory
from profiles.serializers import (
    UserProfileSerializer, DepartmentSerializer, CourseSerializer,
    ProfileHistorySerializer, UserProfileUpdateSerializer,
    BulkProfileUpdateSerializer
)
from users.models import User
import logging

logger = logging.getLogger(__name__)


class UserProfileViewSet(ModelViewSet):
    """
    ViewSet for user profile management.
    """
    queryset = UserProfile.objects.all().select_related('user', 'user__role')
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['department', 'faculty', 'year_of_study', 'user__role__name']
    search_fields = ['user__email', 'user__first_name', 'user__last_name', 'department', 'faculty']
    ordering_fields = ['user__email', 'department', 'faculty', 'created_at']
    ordering = ['-created_at']

    def get_serializer_class(self):
        """Return appropriate serializer based on action."""
        if self.action in ['update', 'partial_update']:
            return UserProfileUpdateSerializer
        return UserProfileSerializer

    def get_permissions(self):
        """Get permissions based on action."""
        if self.action in ['list', 'retrieve']:
            permission_classes = [permissions.IsAuthenticated, IsAdmin]
        elif self.action in ['update', 'partial_update']:
            permission_classes = [permissions.IsAuthenticated, IsOwnerOrReadOnly]
        else:
            permission_classes = [permissions.IsAuthenticated, IsAdmin]
        
        return [permission() for permission in permission_classes]

    def get_queryset(self):
        """Filter queryset based on user permissions."""
        queryset = super().get_queryset()
        
        if not self.request.user.is_authenticated:
            return queryset.none()

        user_role = getattr(self.request.user, 'role', None)
        if not user_role:
            return queryset.none()

        # Superadmin can see all profiles
        if user_role.name == 'superadmin':
            return queryset

        # Admin can see lecturer and student profiles
        elif user_role.name == 'admin':
            return queryset.filter(user__role__name__in=['lecturer', 'student'])

        # Users can only see their own profile
        else:
            return queryset.filter(user=self.request.user)

    @action(detail=True, methods=['get'])
    def history(self, request, pk=None):
        """Get profile change history."""
        profile = self.get_object()
        
        # Check permissions
        if (profile.user != request.user and 
            not IsAdmin().has_permission(request, None)):
            return Response({
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)
        
        history = ProfileHistory.objects.filter(profile=profile).order_by('-created_at')
        
        # Paginate
        page = self.paginate_queryset(history)
        if page is not None:
            serializer = ProfileHistorySerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = ProfileHistorySerializer(history, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['post'])
    def bulk_update(self, request):
        """Perform bulk updates on profiles."""
        if not IsAdmin().has_permission(request, None):
            return Response({
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)
        
        serializer = BulkProfileUpdateSerializer(data=request.data)
        if serializer.is_valid():
            user_ids = serializer.validated_data['user_ids']
            updates = serializer.validated_data['updates']
            change_reason = serializer.validated_data.get('change_reason', 'Bulk update')
            
            # Get profiles to update
            profiles = UserProfile.objects.filter(user_id__in=user_ids)
            
            updated_count = 0
            for profile in profiles:
                # Track changes and update
                for field, new_value in updates.items():
                    old_value = getattr(profile, field)
                    if old_value != new_value:
                        ProfileHistory.objects.create(
                            profile=profile,
                            changed_by=request.user,
                            field_name=field,
                            old_value=str(old_value) if old_value is not None else '',
                            new_value=str(new_value) if new_value is not None else '',
                            change_reason=change_reason
                        )
                        setattr(profile, field, new_value)
                
                profile.save()
                profile.calculate_profile_completion()
                updated_count += 1
            
            return Response({
                'message': f'Updated {updated_count} profiles',
                'updated_count': updated_count
            }, status=status.HTTP_200_OK)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """Get profile statistics."""
        if not IsAdmin().has_permission(request, None):
            return Response({
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)
        
        try:
            # Basic statistics
            total_profiles = UserProfile.objects.count()
            
            # Completion statistics
            completion_stats = UserProfile.objects.aggregate(
                avg_completion=models.Avg('profile_completion_percentage'),
                complete_profiles=Count('id', filter=Q(profile_completion_percentage=100)),
                incomplete_profiles=Count('id', filter=Q(profile_completion_percentage__lt=100))
            )
            
            # Department statistics
            dept_stats = UserProfile.objects.values('department').annotate(
                count=Count('id')
            ).order_by('-count')[:10]
            
            # Faculty statistics
            faculty_stats = UserProfile.objects.values('faculty').annotate(
                count=Count('id')
            ).order_by('-count')[:10]
            
            # Role-based statistics
            role_stats = UserProfile.objects.values(
                'user__role__name', 'user__role__display_name'
            ).annotate(count=Count('id'))
            
            return Response({
                'total_profiles': total_profiles,
                'completion_statistics': completion_stats,
                'department_statistics': list(dept_stats),
                'faculty_statistics': list(faculty_stats),
                'role_statistics': list(role_stats),
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Error generating profile statistics: {str(e)}")
            return Response({
                'error': 'Failed to generate statistics'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class DepartmentViewSet(ModelViewSet):
    """
    ViewSet for department management.
    """
    queryset = Department.objects.all().select_related('head_of_department')
    serializer_class = DepartmentSerializer
    permission_classes = [permissions.IsAuthenticated, IsAdmin]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['faculty']
    search_fields = ['name', 'code', 'description', 'faculty']
    ordering_fields = ['name', 'code', 'faculty', 'total_lecturers', 'total_students']
    ordering = ['name']

    @action(detail=True, methods=['post'])
    def update_statistics(self, request, pk=None):
        """Update department statistics."""
        department = self.get_object()
        department.update_statistics()
        
        return Response({
            'message': 'Statistics updated',
            'total_lecturers': department.total_lecturers,
            'total_students': department.total_students
        }, status=status.HTTP_200_OK)

    @action(detail=True, methods=['get'])
    def users(self, request, pk=None):
        """Get users in this department."""
        department = self.get_object()
        
        users = User.objects.filter(
            profile__department=department.name,
            is_active=True
        ).select_related('role')
        
        # Filter by role if specified
        role = request.GET.get('role')
        if role:
            users = users.filter(role__name=role)
        
        # Paginate
        page = self.paginate_queryset(users)
        if page is not None:
            from users.serializers import UserListSerializer
            serializer = UserListSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        from users.serializers import UserListSerializer
        serializer = UserListSerializer(users, many=True)
        return Response(serializer.data)


class CourseViewSet(ModelViewSet):
    """
    ViewSet for course management.
    """
    queryset = Course.objects.all().select_related('department')
    serializer_class = CourseSerializer
    permission_classes = [permissions.IsAuthenticated, IsAdmin]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['department', 'semester', 'year_level', 'credits']
    search_fields = ['code', 'name', 'description']
    ordering_fields = ['code', 'name', 'semester', 'year_level', 'credits']
    ordering = ['code']

    @action(detail=True, methods=['post'])
    def assign_lecturer(self, request, pk=None):
        """Assign a lecturer to the course."""
        course = self.get_object()
        lecturer_id = request.data.get('lecturer_id')
        
        try:
            lecturer = User.objects.get(
                id=lecturer_id,
                role__name='lecturer',
                is_active=True
            )
            course.lecturers.add(lecturer)
            
            return Response({
                'message': f'Lecturer {lecturer.get_full_name()} assigned to {course.code}'
            }, status=status.HTTP_200_OK)
            
        except User.DoesNotExist:
            return Response({
                'error': 'Lecturer not found'
            }, status=status.HTTP_404_NOT_FOUND)

    @action(detail=True, methods=['post'])
    def remove_lecturer(self, request, pk=None):
        """Remove a lecturer from the course."""
        course = self.get_object()
        lecturer_id = request.data.get('lecturer_id')
        
        try:
            lecturer = User.objects.get(id=lecturer_id)
            course.lecturers.remove(lecturer)
            
            return Response({
                'message': f'Lecturer {lecturer.get_full_name()} removed from {course.code}'
            }, status=status.HTTP_200_OK)
            
        except User.DoesNotExist:
            return Response({
                'error': 'Lecturer not found'
            }, status=status.HTTP_404_NOT_FOUND)

    @action(detail=True, methods=['post'])
    def enroll_student(self, request, pk=None):
        """Enroll a student in the course."""
        course = self.get_object()
        student_id = request.data.get('student_id')
        
        try:
            student = User.objects.get(
                id=student_id,
                role__name='student',
                is_active=True
            )
            course.enrolled_students.add(student)
            
            return Response({
                'message': f'Student {student.get_full_name()} enrolled in {course.code}'
            }, status=status.HTTP_200_OK)
            
        except User.DoesNotExist:
            return Response({
                'error': 'Student not found'
            }, status=status.HTTP_404_NOT_FOUND)

    @action(detail=True, methods=['post'])
    def unenroll_student(self, request, pk=None):
        """Unenroll a student from the course."""
        course = self.get_object()
        student_id = request.data.get('student_id')
        
        try:
            student = User.objects.get(id=student_id)
            course.enrolled_students.remove(student)
            
            return Response({
                'message': f'Student {student.get_full_name()} unenrolled from {course.code}'
            }, status=status.HTTP_200_OK)
            
        except User.DoesNotExist:
            return Response({
                'error': 'Student not found'
            }, status=status.HTTP_404_NOT_FOUND)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def my_profile(request):
    """
    Get current user's profile.
    """
    try:
        profile = UserProfile.objects.get(user=request.user)
        serializer = UserProfileSerializer(profile)
        return Response(serializer.data, status=status.HTTP_200_OK)
    except UserProfile.DoesNotExist:
        return Response({
            'error': 'Profile not found'
        }, status=status.HTTP_404_NOT_FOUND)
