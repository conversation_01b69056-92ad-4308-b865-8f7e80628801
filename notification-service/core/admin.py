"""
Admin configuration for core models.
"""
from django.contrib import admin
from .models import User, NotificationPreference, ServiceIntegration


@admin.register(User)
class UserAdmin(admin.ModelAdmin):
    list_display = ('email', 'username', 'role', 'is_active', 'email_notifications_enabled', 'date_joined')
    list_filter = ('role', 'is_active', 'email_notifications_enabled', 'sms_notifications_enabled', 'push_notifications_enabled')
    search_fields = ('email', 'username', 'first_name', 'last_name')
    ordering = ('-date_joined',)
    readonly_fields = ('date_joined', 'last_login')
    
    fieldsets = (
        (None, {
            'fields': ('email', 'username', 'password')
        }),
        ('Personal Info', {
            'fields': ('first_name', 'last_name', 'phone_number', 'department', 'faculty')
        }),
        ('Role & Permissions', {
            'fields': ('role', 'is_active', 'is_staff', 'is_superuser')
        }),
        ('Notification Preferences', {
            'fields': ('email_notifications_enabled', 'sms_notifications_enabled', 'push_notifications_enabled', 'in_app_notifications_enabled', 'email_frequency')
        }),
        ('Device Tokens', {
            'fields': ('fcm_token',),
            'classes': ('collapse',)
        }),
        ('Important Dates', {
            'fields': ('date_joined', 'last_login'),
            'classes': ('collapse',)
        }),
    )


@admin.register(NotificationPreference)
class NotificationPreferenceAdmin(admin.ModelAdmin):
    list_display = ('user', 'category', 'email_enabled', 'sms_enabled', 'push_enabled', 'in_app_enabled', 'frequency')
    list_filter = ('category', 'email_enabled', 'sms_enabled', 'push_enabled', 'in_app_enabled', 'frequency')
    search_fields = ('user__email', 'user__username')
    ordering = ('user', 'category')
    
    fieldsets = (
        (None, {
            'fields': ('user', 'category', 'frequency')
        }),
        ('Channel Preferences', {
            'fields': ('email_enabled', 'sms_enabled', 'push_enabled', 'in_app_enabled')
        }),
        ('Quiet Hours', {
            'fields': ('quiet_hours_start', 'quiet_hours_end'),
            'classes': ('collapse',)
        }),
    )


@admin.register(ServiceIntegration)
class ServiceIntegrationAdmin(admin.ModelAdmin):
    list_display = ('service_name', 'base_url', 'is_enabled', 'is_healthy', 'last_health_check', 'current_hour_requests')
    list_filter = ('service_name', 'is_enabled', 'is_healthy')
    search_fields = ('service_name', 'base_url')
    ordering = ('service_name',)
    readonly_fields = ('last_health_check', 'current_hour_requests', 'rate_limit_reset_time')
    
    fieldsets = (
        (None, {
            'fields': ('service_name', 'base_url', 'api_key', 'is_enabled')
        }),
        ('Health Check', {
            'fields': ('is_healthy', 'last_health_check', 'health_check_interval')
        }),
        ('Rate Limiting', {
            'fields': ('rate_limit_per_hour', 'current_hour_requests', 'rate_limit_reset_time'),
            'classes': ('collapse',)
        }),
    )
