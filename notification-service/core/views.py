"""
Core views for health checks and basic functionality.
"""
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status
from django.conf import settings
from django.db import connection
from django.utils import timezone
import redis
import logging

logger = logging.getLogger(__name__)


@api_view(['GET'])
@permission_classes([AllowAny])
def health_check(request):
    """
    Health check endpoint for monitoring service status.
    """
    health_status = {
        'status': 'healthy',
        'service': 'notification-service',
        'version': '1.0.0',
        'timestamp': timezone.now().isoformat(),
        'checks': {}
    }
    
    # Check database connection
    try:
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
        health_status['checks']['database'] = 'healthy'
    except Exception as e:
        health_status['checks']['database'] = f'unhealthy: {str(e)}'
        health_status['status'] = 'unhealthy'
        logger.error(f"Database health check failed: {e}")
    
    # Check Redis connection
    try:
        r = redis.from_url(settings.REDIS_URL)
        r.ping()
        health_status['checks']['redis'] = 'healthy'
    except Exception as e:
        health_status['checks']['redis'] = f'unhealthy: {str(e)}'
        health_status['status'] = 'unhealthy'
        logger.error(f"Redis health check failed: {e}")
    
    # Check Celery
    try:
        from celery import current_app
        inspect = current_app.control.inspect()
        stats = inspect.stats()
        if stats:
            health_status['checks']['celery'] = 'healthy'
        else:
            health_status['checks']['celery'] = 'no workers available'
    except Exception as e:
        health_status['checks']['celery'] = f'unhealthy: {str(e)}'
        logger.error(f"Celery health check failed: {e}")
    
    # Check email backend
    try:
        from django.core.mail import get_connection
        connection = get_connection()
        connection.open()
        connection.close()
        health_status['checks']['email'] = 'healthy'
    except Exception as e:
        health_status['checks']['email'] = f'unhealthy: {str(e)}'
        logger.error(f"Email backend health check failed: {e}")
    
    response_status = status.HTTP_200_OK if health_status['status'] == 'healthy' else status.HTTP_503_SERVICE_UNAVAILABLE
    return Response(health_status, status=response_status)


@api_view(['GET'])
@permission_classes([AllowAny])
def service_info(request):
    """
    Service information endpoint.
    """
    return Response({
        'service': 'Notification Service',
        'version': '1.0.0',
        'description': 'Microservice for managing and delivering notifications via multiple channels',
        'features': [
            'Email notifications with templates',
            'SMS notifications via Twilio',
            'Push notifications via FCM',
            'In-app notifications via WebSockets',
            'Notification preferences management',
            'Rate limiting and delivery tracking',
            'Integration with other microservices',
            'Notification history and analytics'
        ],
        'channels': {
            'email': {
                'enabled': True,
                'backend': settings.EMAIL_BACKEND,
                'sendgrid': settings.USE_SENDGRID
            },
            'sms': {
                'enabled': settings.USE_SMS,
                'provider': 'Twilio' if settings.USE_SMS else None
            },
            'push': {
                'enabled': settings.USE_PUSH_NOTIFICATIONS,
                'provider': 'FCM' if settings.USE_PUSH_NOTIFICATIONS else None
            },
            'websocket': {
                'enabled': True,
                'heartbeat_interval': settings.WEBSOCKET_HEARTBEAT_INTERVAL
            }
        },
        'endpoints': {
            'health': '/api/health/',
            'docs': '/api/docs/',
            'notifications': '/api/notifications/',
            'email': '/api/email/',
            'websocket': '/ws/notifications/'
        },
        'timestamp': timezone.now().isoformat()
    })


@api_view(['GET'])
def service_stats(request):
    """
    Service statistics endpoint.
    """
    from notifications.models import Notification, NotificationDelivery
    from datetime import timedelta
    
    # Calculate statistics
    now = timezone.now()
    last_24h = now - timedelta(hours=24)
    last_7d = now - timedelta(days=7)
    
    stats = {
        'notifications': {
            'total': Notification.objects.count(),
            'last_24h': Notification.objects.filter(created_at__gte=last_24h).count(),
            'last_7d': Notification.objects.filter(created_at__gte=last_7d).count(),
            'by_type': {}
        },
        'deliveries': {
            'total': NotificationDelivery.objects.count(),
            'successful': NotificationDelivery.objects.filter(status='delivered').count(),
            'failed': NotificationDelivery.objects.filter(status='failed').count(),
            'pending': NotificationDelivery.objects.filter(status='pending').count(),
            'last_24h': NotificationDelivery.objects.filter(created_at__gte=last_24h).count(),
            'by_channel': {}
        },
        'users': {
            'total': User.objects.count(),
            'email_enabled': User.objects.filter(email_notifications_enabled=True).count(),
            'sms_enabled': User.objects.filter(sms_notifications_enabled=True).count(),
            'push_enabled': User.objects.filter(push_notifications_enabled=True).count(),
        },
        'timestamp': now.isoformat()
    }
    
    # Get notification types breakdown
    from notifications.models import NotificationType
    for notification_type in NotificationType.objects.all():
        count = Notification.objects.filter(notification_type=notification_type).count()
        stats['notifications']['by_type'][notification_type.name] = count
    
    # Get delivery channel breakdown
    for channel in ['email', 'sms', 'push', 'websocket']:
        count = NotificationDelivery.objects.filter(channel=channel).count()
        stats['deliveries']['by_channel'][channel] = count
    
    return Response(stats)
