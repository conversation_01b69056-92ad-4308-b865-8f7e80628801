"""
Serializers for core models.
"""
from rest_framework import serializers
from .models import User, NotificationPreference, ServiceIntegration


class UserSerializer(serializers.ModelSerializer):
    full_name = serializers.ReadOnlyField()
    
    class Meta:
        model = User
        fields = [
            'id', 'email', 'username', 'first_name', 'last_name', 'full_name',
            'role', 'phone_number', 'department', 'faculty', 'is_active',
            'email_notifications_enabled', 'sms_notifications_enabled',
            'push_notifications_enabled', 'in_app_notifications_enabled',
            'email_frequency', 'date_joined', 'last_login'
        ]
        read_only_fields = ['id', 'date_joined', 'last_login']


class NotificationPreferenceSerializer(serializers.ModelSerializer):
    category_display = serializers.CharField(source='get_category_display', read_only=True)
    frequency_display = serializers.CharField(source='get_frequency_display', read_only=True)
    
    class Meta:
        model = NotificationPreference
        fields = [
            'id', 'user', 'category', 'category_display', 'email_enabled',
            'sms_enabled', 'push_enabled', 'in_app_enabled', 'frequency',
            'frequency_display', 'quiet_hours_start', 'quiet_hours_end',
            'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class ServiceIntegrationSerializer(serializers.ModelSerializer):
    service_name_display = serializers.CharField(source='get_service_name_display', read_only=True)
    
    class Meta:
        model = ServiceIntegration
        fields = [
            'id', 'service_name', 'service_name_display', 'base_url',
            'is_enabled', 'last_health_check', 'is_healthy',
            'health_check_interval', 'rate_limit_per_hour',
            'current_hour_requests', 'rate_limit_reset_time',
            'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'last_health_check', 'current_hour_requests',
            'rate_limit_reset_time', 'created_at', 'updated_at'
        ]
        extra_kwargs = {
            'api_key': {'write_only': True}
        }
