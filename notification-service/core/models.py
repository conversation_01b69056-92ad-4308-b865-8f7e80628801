"""
Core models for the notification service.
"""
from django.db import models
from django.contrib.auth.models import AbstractBaseUser, PermissionsMixin
from django.core.validators import EmailValidator
from django.utils import timezone
import uuid


class BaseModel(models.Model):
    """
    Abstract base model with common fields for all models.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        abstract = True


class User(AbstractBaseUser, PermissionsMixin, BaseModel):
    """
    User model proxy for notification service (synced from user management service).
    """
    email = models.EmailField(
        unique=True,
        validators=[EmailValidator()],
        help_text="User's email address"
    )
    username = models.Char<PERSON>ield(
        max_length=150,
        unique=True,
        help_text="Unique username for the user"
    )
    first_name = models.CharField(max_length=150, blank=True)
    last_name = models.CharField(max_length=150, blank=True)
    is_staff = models.BooleanField(default=False)
    is_superuser = models.BooleanField(default=False)
    date_joined = models.DateTimeField(default=timezone.now)
    last_login = models.DateTimeField(blank=True, null=True)
    
    # Role information
    role = models.CharField(
        max_length=50,
        choices=[
            ('superadmin', 'Super Admin'),
            ('admin', 'Admin'),
            ('lecturer', 'Lecturer'),
            ('student', 'Student'),
        ],
        default='student'
    )
    
    # Contact information
    phone_number = models.CharField(max_length=20, blank=True)
    department = models.CharField(max_length=100, blank=True)
    faculty = models.CharField(max_length=100, blank=True)
    
    # Notification preferences
    email_notifications_enabled = models.BooleanField(default=True)
    sms_notifications_enabled = models.BooleanField(default=False)
    push_notifications_enabled = models.BooleanField(default=True)
    in_app_notifications_enabled = models.BooleanField(default=True)
    
    # Notification frequency preferences
    FREQUENCY_CHOICES = [
        ('immediate', 'Immediate'),
        ('hourly', 'Hourly Digest'),
        ('daily', 'Daily Digest'),
        ('weekly', 'Weekly Digest'),
        ('disabled', 'Disabled'),
    ]
    
    email_frequency = models.CharField(
        max_length=20,
        choices=FREQUENCY_CHOICES,
        default='immediate'
    )
    
    # Device tokens for push notifications
    fcm_token = models.TextField(blank=True, help_text="Firebase Cloud Messaging token")
    
    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username']

    class Meta:
        db_table = 'core_user'
        verbose_name = 'User'
        verbose_name_plural = 'Users'

    def __str__(self):
        return f"{self.email} ({self.role})"

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}".strip()

    def can_receive_email(self):
        """Check if user can receive email notifications."""
        return self.email_notifications_enabled and self.email

    def can_receive_sms(self):
        """Check if user can receive SMS notifications."""
        return self.sms_notifications_enabled and self.phone_number

    def can_receive_push(self):
        """Check if user can receive push notifications."""
        return self.push_notifications_enabled and self.fcm_token

    def can_receive_in_app(self):
        """Check if user can receive in-app notifications."""
        return self.in_app_notifications_enabled


class NotificationPreference(BaseModel):
    """
    User-specific notification preferences for different types of notifications.
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='notification_preferences')
    
    # Notification categories
    CATEGORY_CHOICES = [
        ('timetable_updates', 'Timetable Updates'),
        ('preference_reminders', 'Preference Reminders'),
        ('system_alerts', 'System Alerts'),
        ('deadline_reminders', 'Deadline Reminders'),
        ('conflict_notifications', 'Conflict Notifications'),
        ('generation_status', 'Generation Status'),
        ('export_ready', 'Export Ready'),
        ('account_security', 'Account Security'),
    ]
    
    category = models.CharField(max_length=50, choices=CATEGORY_CHOICES)
    
    # Channel preferences for this category
    email_enabled = models.BooleanField(default=True)
    sms_enabled = models.BooleanField(default=False)
    push_enabled = models.BooleanField(default=True)
    in_app_enabled = models.BooleanField(default=True)
    
    # Frequency for this category
    frequency = models.CharField(
        max_length=20,
        choices=User.FREQUENCY_CHOICES,
        default='immediate'
    )
    
    # Quiet hours
    quiet_hours_start = models.TimeField(null=True, blank=True, help_text="Start of quiet hours (no notifications)")
    quiet_hours_end = models.TimeField(null=True, blank=True, help_text="End of quiet hours")
    
    class Meta:
        db_table = 'core_notification_preference'
        unique_together = ['user', 'category']
        ordering = ['category']

    def __str__(self):
        return f"{self.user.email} - {self.get_category_display()}"

    def is_in_quiet_hours(self):
        """Check if current time is within user's quiet hours."""
        if not self.quiet_hours_start or not self.quiet_hours_end:
            return False
        
        now = timezone.now().time()
        
        if self.quiet_hours_start <= self.quiet_hours_end:
            # Same day quiet hours
            return self.quiet_hours_start <= now <= self.quiet_hours_end
        else:
            # Overnight quiet hours
            return now >= self.quiet_hours_start or now <= self.quiet_hours_end


class ServiceIntegration(BaseModel):
    """
    Model for tracking integration with other microservices.
    """
    SERVICE_CHOICES = [
        ('user_service', 'User Management Service'),
        ('auth_service', 'Authentication Service'),
        ('preference_service', 'Preference Collection Service'),
        ('timetable_service', 'Timetable Generation Service'),
        ('analytics_service', 'Data Analytics Service'),
    ]
    
    service_name = models.CharField(max_length=50, choices=SERVICE_CHOICES, unique=True)
    base_url = models.URLField()
    api_key = models.CharField(max_length=255, blank=True)
    is_enabled = models.BooleanField(default=True)
    
    # Health check
    last_health_check = models.DateTimeField(null=True, blank=True)
    is_healthy = models.BooleanField(default=True)
    health_check_interval = models.PositiveIntegerField(default=300, help_text="Health check interval in seconds")
    
    # Rate limiting
    rate_limit_per_hour = models.PositiveIntegerField(default=1000)
    current_hour_requests = models.PositiveIntegerField(default=0)
    rate_limit_reset_time = models.DateTimeField(default=timezone.now)
    
    class Meta:
        db_table = 'core_service_integration'
        ordering = ['service_name']

    def __str__(self):
        return f"{self.get_service_name_display()} - {'Healthy' if self.is_healthy else 'Unhealthy'}"

    def can_make_request(self):
        """Check if service can make a request within rate limits."""
        now = timezone.now()
        
        # Reset rate limit if hour has passed
        if now >= self.rate_limit_reset_time:
            self.current_hour_requests = 0
            self.rate_limit_reset_time = now.replace(minute=0, second=0, microsecond=0) + timezone.timedelta(hours=1)
            self.save()
        
        return self.current_hour_requests < self.rate_limit_per_hour

    def increment_request_count(self):
        """Increment the request count for rate limiting."""
        self.current_hour_requests += 1
        self.save(update_fields=['current_hour_requests'])
