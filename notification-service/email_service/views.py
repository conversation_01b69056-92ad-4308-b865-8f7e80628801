"""
Views for email service.
"""
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from .services import EmailService
import logging

logger = logging.getLogger(__name__)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def send_email(request):
    """
    Send a single email.
    """
    data = request.data
    
    required_fields = ['to_email', 'subject', 'message']
    for field in required_fields:
        if field not in data:
            return Response(
                {'error': f'{field} is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
    
    email_service = EmailService()
    
    # Prepare content
    html_content = data.get('html_content', f"<p>{data['message']}</p>")
    text_content = data.get('text_content', data['message'])
    
    # Send email
    if email_service.use_sendgrid:
        result = email_service._send_via_sendgrid(
            to_email=data['to_email'],
            subject=data['subject'],
            html_content=html_content,
            text_content=text_content
        )
    else:
        result = email_service._send_via_django(
            to_email=data['to_email'],
            subject=data['subject'],
            html_content=html_content,
            text_content=text_content
        )
    
    if result['success']:
        return Response(result, status=status.HTTP_200_OK)
    else:
        return Response(result, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def send_bulk_email(request):
    """
    Send bulk email to multiple recipients.
    """
    data = request.data
    
    required_fields = ['recipients', 'subject', 'message']
    for field in required_fields:
        if field not in data:
            return Response(
                {'error': f'{field} is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
    
    if not isinstance(data['recipients'], list):
        return Response(
            {'error': 'recipients must be a list of email addresses'},
            status=status.HTTP_400_BAD_REQUEST
        )
    
    email_service = EmailService()
    
    # Prepare content
    html_content = data.get('html_content', f"<p>{data['message']}</p>")
    text_content = data.get('text_content', data['message'])
    
    # Send bulk email
    result = email_service.send_bulk_email(
        recipients=data['recipients'],
        subject=data['subject'],
        html_content=html_content,
        text_content=text_content
    )
    
    return Response(result, status=status.HTTP_200_OK)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def send_template_email(request):
    """
    Send email using a template.
    """
    data = request.data
    
    required_fields = ['to_email', 'template_name', 'context']
    for field in required_fields:
        if field not in data:
            return Response(
                {'error': f'{field} is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
    
    email_service = EmailService()
    
    result = email_service.send_template_email(
        to_email=data['to_email'],
        template_name=data['template_name'],
        context=data['context'],
        subject=data.get('subject')
    )
    
    if result['success']:
        return Response(result, status=status.HTTP_200_OK)
    else:
        return Response(result, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def test_email(request):
    """
    Send a test email to verify email service is working.
    """
    data = request.data
    to_email = data.get('to_email', request.user.email)
    
    email_service = EmailService()
    
    test_content = """
    <h2>Email Service Test</h2>
    <p>This is a test email from the Notification Service.</p>
    <p>If you received this email, the email service is working correctly.</p>
    <p>Timestamp: {{ timestamp }}</p>
    """
    
    from django.utils import timezone
    context = {
        'user': request.user,
        'timestamp': timezone.now().isoformat(),
        'site_name': 'Timetable Management System'
    }
    
    from jinja2 import Template
    template = Template(test_content)
    html_content = template.render(context)
    
    if email_service.use_sendgrid:
        result = email_service._send_via_sendgrid(
            to_email=to_email,
            subject="Email Service Test",
            html_content=html_content,
            text_content="This is a test email from the Notification Service."
        )
    else:
        result = email_service._send_via_django(
            to_email=to_email,
            subject="Email Service Test",
            html_content=html_content,
            text_content="This is a test email from the Notification Service."
        )
    
    return Response(result, status=status.HTTP_200_OK)
