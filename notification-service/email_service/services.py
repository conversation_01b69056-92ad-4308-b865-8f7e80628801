"""
Email service for sending notifications.
"""
from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string
from django.conf import settings
from django.utils.html import strip_tags
import logging

logger = logging.getLogger(__name__)


class EmailService:
    """
    Email service for sending notification emails.
    """
    
    def __init__(self):
        self.use_sendgrid = settings.USE_SENDGRID
        if self.use_sendgrid:
            try:
                import sendgrid
                from sendgrid.helpers.mail import Mail
                self.sg = sendgrid.SendGridAPIClient(api_key=settings.SENDGRID_API_KEY)
                self.Mail = Mail
            except ImportError:
                logger.error("SendGrid library not installed")
                self.use_sendgrid = False
            except Exception as e:
                logger.error(f"Failed to initialize SendGrid client: {e}")
                self.use_sendgrid = False
    
    def send_notification_email(self, notification):
        """
        Send email notification.
        """
        try:
            # Get email template
            template = self._get_email_template(notification)
            
            # Prepare context
            context = {
                'user': notification.recipient,
                'notification': notification,
                'site_name': 'Timetable Management System',
                'site_url': getattr(settings, 'SITE_URL', 'http://localhost:3000'),
                **notification.data
            }
            
            # Render email content
            if template:
                rendered = template.render(context)
                subject = rendered['subject'] or notification.title
                html_content = rendered['body']
            else:
                subject = notification.title
                html_content = self._render_default_template(notification, context)
            
            # Create plain text version
            text_content = strip_tags(html_content)
            
            # Send email
            if self.use_sendgrid:
                return self._send_via_sendgrid(
                    to_email=notification.recipient.email,
                    subject=subject,
                    html_content=html_content,
                    text_content=text_content
                )
            else:
                return self._send_via_django(
                    to_email=notification.recipient.email,
                    subject=subject,
                    html_content=html_content,
                    text_content=text_content
                )
                
        except Exception as e:
            logger.error(f"Failed to send email notification: {e}")
            return {'success': False, 'error': str(e)}
    
    def _send_via_sendgrid(self, to_email, subject, html_content, text_content):
        """
        Send email via SendGrid.
        """
        try:
            message = self.Mail(
                from_email=settings.DEFAULT_FROM_EMAIL,
                to_emails=to_email,
                subject=subject,
                html_content=html_content,
                plain_text_content=text_content
            )
            
            response = self.sg.send(message)
            
            return {
                'success': True,
                'message_id': response.headers.get('X-Message-Id'),
                'status_code': response.status_code,
                'provider': 'sendgrid'
            }
            
        except Exception as e:
            logger.error(f"SendGrid email failed: {e}")
            return {'success': False, 'error': str(e), 'provider': 'sendgrid'}
    
    def _send_via_django(self, to_email, subject, html_content, text_content):
        """
        Send email via Django's email backend.
        """
        try:
            email = EmailMultiAlternatives(
                subject=subject,
                body=text_content,
                from_email=settings.DEFAULT_FROM_EMAIL,
                to=[to_email]
            )
            
            email.attach_alternative(html_content, "text/html")
            result = email.send()
            
            return {
                'success': result > 0,
                'message_id': None,
                'provider': 'django',
                'sent_count': result
            }
            
        except Exception as e:
            logger.error(f"Django email failed: {e}")
            return {'success': False, 'error': str(e), 'provider': 'django'}
    
    def _get_email_template(self, notification):
        """
        Get email template for notification type.
        """
        from notifications.models import NotificationTemplate
        
        template_name = notification.notification_type.email_template
        
        if template_name:
            try:
                return NotificationTemplate.objects.get(
                    name=template_name,
                    template_type='email_html'
                )
            except NotificationTemplate.DoesNotExist:
                pass
        
        # Return default email template
        return NotificationTemplate.objects.filter(
            template_type='email_html',
            is_default=True
        ).first()
    
    def _render_default_template(self, notification, context):
        """
        Render default email template.
        """
        template_content = """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{{ notification.title }}</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background-color: #007bff; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; background-color: #f8f9fa; }
                .footer { padding: 20px; text-align: center; font-size: 12px; color: #666; }
                .button { display: inline-block; padding: 10px 20px; background-color: #007bff; color: white; text-decoration: none; border-radius: 5px; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>{{ site_name }}</h1>
                </div>
                <div class="content">
                    <h2>{{ notification.title }}</h2>
                    <p>Hello {{ user.first_name or user.username }},</p>
                    <p>{{ notification.message }}</p>
                    {% if notification.data.click_action %}
                    <p>
                        <a href="{{ site_url }}{{ notification.data.click_action }}" class="button">
                            View Details
                        </a>
                    </p>
                    {% endif %}
                </div>
                <div class="footer">
                    <p>This is an automated message from {{ site_name }}.</p>
                    <p>If you no longer wish to receive these emails, you can update your notification preferences.</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        from jinja2 import Template
        template = Template(template_content)
        return template.render(context)
    
    def send_bulk_email(self, recipients, subject, html_content, text_content=None):
        """
        Send bulk email to multiple recipients.
        """
        if not text_content:
            text_content = strip_tags(html_content)
        
        results = []
        
        for recipient_email in recipients:
            if self.use_sendgrid:
                result = self._send_via_sendgrid(recipient_email, subject, html_content, text_content)
            else:
                result = self._send_via_django(recipient_email, subject, html_content, text_content)
            
            results.append({
                'email': recipient_email,
                'success': result['success'],
                'error': result.get('error')
            })
        
        return {
            'total_sent': len([r for r in results if r['success']]),
            'total_failed': len([r for r in results if not r['success']]),
            'results': results
        }
    
    def send_template_email(self, to_email, template_name, context, subject=None):
        """
        Send email using a specific template.
        """
        try:
            from notifications.models import NotificationTemplate
            
            template = NotificationTemplate.objects.get(
                name=template_name,
                template_type='email_html'
            )
            
            rendered = template.render(context)
            email_subject = subject or rendered['subject']
            html_content = rendered['body']
            text_content = strip_tags(html_content)
            
            if self.use_sendgrid:
                return self._send_via_sendgrid(to_email, email_subject, html_content, text_content)
            else:
                return self._send_via_django(to_email, email_subject, html_content, text_content)
                
        except Exception as e:
            logger.error(f"Failed to send template email: {e}")
            return {'success': False, 'error': str(e)}
