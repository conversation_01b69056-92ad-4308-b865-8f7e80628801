"""
Celery configuration for notification_service project.
"""
import os
from celery import Celery
from django.conf import settings

# Set the default Django settings module for the 'celery' program.
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'notification_service.settings')

app = Celery('notification_service')

# Using a string here means the worker doesn't have to serialize
# the configuration object to child processes.
app.config_from_object('django.conf:settings', namespace='CELERY')

# Load task modules from all registered Django apps.
app.autodiscover_tasks()

# Celery beat schedule for periodic tasks
app.conf.beat_schedule = {
    'cleanup-old-notifications': {
        'task': 'notifications.tasks.cleanup_old_notifications',
        'schedule': 86400.0,  # Run daily
    },
    'send-pending-notifications': {
        'task': 'notifications.tasks.process_pending_notifications',
        'schedule': 300.0,  # Run every 5 minutes
    },
    'cleanup-failed-notifications': {
        'task': 'notifications.tasks.cleanup_failed_notifications',
        'schedule': 3600.0,  # Run every hour
    },
    'send-digest-notifications': {
        'task': 'notifications.tasks.send_digest_notifications',
        'schedule': 86400.0,  # Run daily
    },
}

app.conf.timezone = settings.TIME_ZONE

# Task routing
app.conf.task_routes = {
    'notifications.tasks.send_email_notification': {'queue': 'email'},
    'notifications.tasks.send_sms_notification': {'queue': 'sms'},
    'notifications.tasks.send_push_notification': {'queue': 'push'},
    'notifications.tasks.send_websocket_notification': {'queue': 'websocket'},
}

@app.task(bind=True)
def debug_task(self):
    print(f'Request: {self.request!r}')
