"""
WebSocket service for sending real-time notifications.
"""
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
import logging

logger = logging.getLogger(__name__)


class WebSocketService:
    """
    Service for sending WebSocket notifications.
    """
    
    def __init__(self):
        self.channel_layer = get_channel_layer()
    
    def send_notification(self, notification):
        """
        Send notification via WebSocket to a specific user.
        """
        try:
            # Prepare notification data
            notification_data = {
                'id': str(notification.id),
                'title': notification.title,
                'message': notification.message,
                'type': notification.notification_type.name,
                'priority': notification.notification_type.priority,
                'data': notification.data,
                'created_at': notification.created_at.isoformat(),
                'is_read': False
            }
            
            # Send to user's notification group
            user_group = f'notifications_{notification.recipient.id}'
            
            async_to_sync(self.channel_layer.group_send)(
                user_group,
                {
                    'type': 'notification_message',
                    'notification': notification_data
                }
            )
            
            logger.info(f"WebSocket notification sent to user {notification.recipient.id}")
            
            return {
                'success': True,
                'user_id': str(notification.recipient.id),
                'notification_id': str(notification.id)
            }
            
        except Exception as e:
            logger.error(f"Failed to send WebSocket notification: {e}")
            return {'success': False, 'error': str(e)}
    
    def send_notification_update(self, notification_id, user_id, updates):
        """
        Send notification update via WebSocket.
        """
        try:
            user_group = f'notifications_{user_id}'
            
            async_to_sync(self.channel_layer.group_send)(
                user_group,
                {
                    'type': 'notification_update',
                    'notification_id': str(notification_id),
                    'updates': updates
                }
            )
            
            return {'success': True}
            
        except Exception as e:
            logger.error(f"Failed to send notification update: {e}")
            return {'success': False, 'error': str(e)}
    
    def send_unread_count_update(self, user_id, count):
        """
        Send unread count update via WebSocket.
        """
        try:
            user_group = f'notifications_{user_id}'
            
            async_to_sync(self.channel_layer.group_send)(
                user_group,
                {
                    'type': 'unread_count_update',
                    'count': count
                }
            )
            
            return {'success': True}
            
        except Exception as e:
            logger.error(f"Failed to send unread count update: {e}")
            return {'success': False, 'error': str(e)}
    
    def send_system_broadcast(self, message, priority='normal', data=None):
        """
        Send system-wide broadcast message.
        """
        try:
            async_to_sync(self.channel_layer.group_send)(
                'system_broadcasts',
                {
                    'type': 'system_broadcast',
                    'message': message,
                    'priority': priority,
                    'data': data or {}
                }
            )
            
            logger.info(f"System broadcast sent: {message}")
            
            return {'success': True}
            
        except Exception as e:
            logger.error(f"Failed to send system broadcast: {e}")
            return {'success': False, 'error': str(e)}
    
    def send_bulk_notifications(self, user_ids, notification_data):
        """
        Send notifications to multiple users via WebSocket.
        """
        results = []
        
        for user_id in user_ids:
            try:
                user_group = f'notifications_{user_id}'
                
                async_to_sync(self.channel_layer.group_send)(
                    user_group,
                    {
                        'type': 'notification_message',
                        'notification': notification_data
                    }
                )
                
                results.append({'user_id': str(user_id), 'success': True})
                
            except Exception as e:
                logger.error(f"Failed to send WebSocket notification to user {user_id}: {e}")
                results.append({'user_id': str(user_id), 'success': False, 'error': str(e)})
        
        success_count = len([r for r in results if r['success']])
        
        return {
            'total_sent': success_count,
            'total_failed': len(results) - success_count,
            'results': results
        }
