"""
WebSocket consumers for real-time notifications.
"""
import json
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.contrib.auth.models import AnonymousUser
from django.conf import settings
import logging

logger = logging.getLogger(__name__)


class NotificationConsumer(AsyncWebsocketConsumer):
    """
    WebSocket consumer for real-time notifications.
    """
    
    async def connect(self):
        # Get user from scope
        self.user = self.scope["user"]
        
        # Check if user is authenticated
        if self.user == AnonymousUser():
            await self.close()
            return
        
        # Set up user-specific group
        self.user_id = str(self.user.id)
        self.room_group_name = f'notifications_{self.user_id}'
        
        # Join room group
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )
        
        await self.accept()
        
        # Send connection confirmation
        await self.send(text_data=json.dumps({
            'type': 'connection_established',
            'message': 'Connected to notification service',
            'user_id': self.user_id
        }))
        
        # Send unread notifications count
        unread_count = await self.get_unread_notifications_count()
        await self.send(text_data=json.dumps({
            'type': 'unread_count',
            'count': unread_count
        }))
        
        logger.info(f"User {self.user_id} connected to notifications WebSocket")
    
    async def disconnect(self, close_code):
        # Leave room group
        if hasattr(self, 'room_group_name'):
            await self.channel_layer.group_discard(
                self.room_group_name,
                self.channel_name
            )
        
        logger.info(f"User {getattr(self, 'user_id', 'unknown')} disconnected from notifications WebSocket")
    
    async def receive(self, text_data):
        """Handle incoming WebSocket messages."""
        try:
            text_data_json = json.loads(text_data)
            message_type = text_data_json.get('type')
            
            if message_type == 'mark_as_read':
                await self.mark_notification_as_read(text_data_json.get('notification_id'))
            elif message_type == 'mark_all_as_read':
                await self.mark_all_notifications_as_read()
            elif message_type == 'get_notifications':
                await self.send_recent_notifications(text_data_json.get('limit', 10))
            elif message_type == 'ping':
                await self.send(text_data=json.dumps({
                    'type': 'pong',
                    'timestamp': text_data_json.get('timestamp')
                }))
            else:
                await self.send(text_data=json.dumps({
                    'type': 'error',
                    'message': f'Unknown message type: {message_type}'
                }))
                
        except json.JSONDecodeError:
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': 'Invalid JSON format'
            }))
        except Exception as e:
            logger.error(f"Error handling WebSocket message: {e}")
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': 'Internal server error'
            }))
    
    async def mark_notification_as_read(self, notification_id):
        """Mark a specific notification as read."""
        if not notification_id:
            return
        
        try:
            success = await self.mark_notification_read(notification_id)
            if success:
                # Send updated unread count
                unread_count = await self.get_unread_notifications_count()
                await self.send(text_data=json.dumps({
                    'type': 'notification_marked_read',
                    'notification_id': notification_id,
                    'unread_count': unread_count
                }))
        except Exception as e:
            logger.error(f"Error marking notification as read: {e}")
    
    async def mark_all_notifications_as_read(self):
        """Mark all notifications as read for the user."""
        try:
            count = await self.mark_all_notifications_read()
            await self.send(text_data=json.dumps({
                'type': 'all_notifications_marked_read',
                'marked_count': count,
                'unread_count': 0
            }))
        except Exception as e:
            logger.error(f"Error marking all notifications as read: {e}")
    
    async def send_recent_notifications(self, limit=10):
        """Send recent notifications to the client."""
        try:
            notifications = await self.get_recent_notifications(limit)
            await self.send(text_data=json.dumps({
                'type': 'recent_notifications',
                'notifications': notifications
            }))
        except Exception as e:
            logger.error(f"Error sending recent notifications: {e}")
    
    # Group message handlers
    async def notification_message(self, event):
        """Handle notification messages sent to the group."""
        await self.send(text_data=json.dumps({
            'type': 'new_notification',
            'notification': event['notification']
        }))
    
    async def notification_update(self, event):
        """Handle notification update messages."""
        await self.send(text_data=json.dumps({
            'type': 'notification_updated',
            'notification_id': event['notification_id'],
            'updates': event['updates']
        }))
    
    async def unread_count_update(self, event):
        """Handle unread count updates."""
        await self.send(text_data=json.dumps({
            'type': 'unread_count_update',
            'count': event['count']
        }))
    
    # Database operations
    @database_sync_to_async
    def get_unread_notifications_count(self):
        """Get count of unread notifications for the user."""
        from notifications.models import Notification
        return Notification.objects.filter(
            recipient=self.user,
            read_at__isnull=True,
            status='sent'
        ).count()
    
    @database_sync_to_async
    def mark_notification_read(self, notification_id):
        """Mark a notification as read."""
        try:
            from notifications.models import Notification
            notification = Notification.objects.get(
                id=notification_id,
                recipient=self.user
            )
            notification.mark_as_read()
            return True
        except Notification.DoesNotExist:
            return False
    
    @database_sync_to_async
    def mark_all_notifications_read(self):
        """Mark all notifications as read for the user."""
        from notifications.models import Notification
        from django.utils import timezone
        
        count = Notification.objects.filter(
            recipient=self.user,
            read_at__isnull=True
        ).update(read_at=timezone.now())
        
        return count
    
    @database_sync_to_async
    def get_recent_notifications(self, limit=10):
        """Get recent notifications for the user."""
        from notifications.models import Notification
        
        notifications = Notification.objects.filter(
            recipient=self.user,
            status='sent'
        ).order_by('-created_at')[:limit]
        
        return [
            {
                'id': str(notification.id),
                'title': notification.title,
                'message': notification.message,
                'type': notification.notification_type.name,
                'priority': notification.notification_type.priority,
                'data': notification.data,
                'is_read': notification.is_read,
                'created_at': notification.created_at.isoformat(),
                'read_at': notification.read_at.isoformat() if notification.read_at else None
            }
            for notification in notifications
        ]


class BroadcastConsumer(AsyncWebsocketConsumer):
    """
    WebSocket consumer for system-wide broadcasts.
    """
    
    async def connect(self):
        # Check if user is authenticated
        if self.scope["user"] == AnonymousUser():
            await self.close()
            return
        
        # Join broadcast group
        self.room_group_name = 'system_broadcasts'
        
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )
        
        await self.accept()
        
        logger.info(f"User {self.scope['user'].id} connected to broadcast WebSocket")
    
    async def disconnect(self, close_code):
        # Leave broadcast group
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )
        
        logger.info(f"User disconnected from broadcast WebSocket")
    
    async def receive(self, text_data):
        """Handle incoming WebSocket messages."""
        try:
            text_data_json = json.loads(text_data)
            message_type = text_data_json.get('type')
            
            if message_type == 'ping':
                await self.send(text_data=json.dumps({
                    'type': 'pong',
                    'timestamp': text_data_json.get('timestamp')
                }))
                
        except json.JSONDecodeError:
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': 'Invalid JSON format'
            }))
    
    # Group message handlers
    async def system_broadcast(self, event):
        """Handle system broadcast messages."""
        await self.send(text_data=json.dumps({
            'type': 'system_broadcast',
            'message': event['message'],
            'priority': event.get('priority', 'normal'),
            'data': event.get('data', {})
        }))
