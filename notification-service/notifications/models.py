"""
Notification models for the notification service.
"""
from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from core.models import BaseModel, User
import json


class NotificationType(BaseModel):
    """
    Model for different types of notifications.
    """
    PRIORITY_CHOICES = [
        ('low', 'Low'),
        ('normal', 'Normal'),
        ('high', 'High'),
        ('urgent', 'Urgent'),
    ]
    
    name = models.CharField(max_length=100, unique=True)
    display_name = models.CharField(max_length=200)
    description = models.TextField()
    priority = models.Char<PERSON>ield(max_length=10, choices=PRIORITY_CHOICES, default='normal')
    
    # Channel settings
    email_enabled = models.BooleanField(default=True)
    sms_enabled = models.BooleanField(default=False)
    push_enabled = models.BooleanField(default=True)
    websocket_enabled = models.BooleanField(default=True)
    
    # Template settings
    email_template = models.Char<PERSON><PERSON>(max_length=200, blank=True)
    sms_template = models.CharField(max_length=200, blank=True)
    push_template = models.CharField(max_length=200, blank=True)
    websocket_template = models.CharField(max_length=200, blank=True)
    
    # Delivery settings
    retry_count = models.PositiveIntegerField(default=3)
    retry_delay_minutes = models.PositiveIntegerField(default=5)
    expiry_hours = models.PositiveIntegerField(default=24, help_text="Hours after which notification expires")
    
    class Meta:
        db_table = 'notifications_type'
        ordering = ['priority', 'name']

    def __str__(self):
        return f"{self.display_name} ({self.priority})"


class Notification(BaseModel):
    """
    Main notification model.
    """
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('sent', 'Sent'),
        ('failed', 'Failed'),
        ('expired', 'Expired'),
    ]
    
    notification_type = models.ForeignKey(NotificationType, on_delete=models.CASCADE)
    recipient = models.ForeignKey(User, on_delete=models.CASCADE, related_name='received_notifications')
    sender = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='sent_notifications')
    
    # Content
    title = models.CharField(max_length=255)
    message = models.TextField()
    data = models.JSONField(default=dict, blank=True, help_text="Additional data for the notification")
    
    # Status and timing
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    scheduled_at = models.DateTimeField(default=timezone.now, help_text="When to send the notification")
    sent_at = models.DateTimeField(null=True, blank=True)
    expires_at = models.DateTimeField(null=True, blank=True)
    
    # Delivery tracking
    delivery_attempts = models.PositiveIntegerField(default=0)
    last_attempt_at = models.DateTimeField(null=True, blank=True)
    error_message = models.TextField(blank=True)
    
    # Metadata
    source_service = models.CharField(max_length=50, blank=True, help_text="Service that created this notification")
    reference_id = models.CharField(max_length=255, blank=True, help_text="Reference ID from source service")
    
    # User interaction
    read_at = models.DateTimeField(null=True, blank=True)
    clicked_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        db_table = 'notifications_notification'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['recipient', 'status']),
            models.Index(fields=['scheduled_at']),
            models.Index(fields=['notification_type']),
        ]

    def __str__(self):
        return f"{self.title} -> {self.recipient.email}"

    def save(self, *args, **kwargs):
        # Set expiry time if not set
        if not self.expires_at and self.notification_type:
            self.expires_at = self.scheduled_at + timezone.timedelta(hours=self.notification_type.expiry_hours)
        super().save(*args, **kwargs)

    @property
    def is_expired(self):
        """Check if notification has expired."""
        return self.expires_at and timezone.now() > self.expires_at

    @property
    def is_read(self):
        """Check if notification has been read."""
        return self.read_at is not None

    def mark_as_read(self):
        """Mark notification as read."""
        if not self.read_at:
            self.read_at = timezone.now()
            self.save(update_fields=['read_at'])

    def mark_as_clicked(self):
        """Mark notification as clicked."""
        if not self.clicked_at:
            self.clicked_at = timezone.now()
            self.save(update_fields=['clicked_at'])
        
        # Also mark as read if not already
        self.mark_as_read()

    def can_retry(self):
        """Check if notification can be retried."""
        return (
            self.status in ['pending', 'failed'] and
            self.delivery_attempts < self.notification_type.retry_count and
            not self.is_expired
        )


class NotificationDelivery(BaseModel):
    """
    Model for tracking delivery attempts across different channels.
    """
    CHANNEL_CHOICES = [
        ('email', 'Email'),
        ('sms', 'SMS'),
        ('push', 'Push Notification'),
        ('websocket', 'WebSocket'),
    ]
    
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('delivered', 'Delivered'),
        ('failed', 'Failed'),
        ('bounced', 'Bounced'),
        ('rejected', 'Rejected'),
    ]
    
    notification = models.ForeignKey(Notification, on_delete=models.CASCADE, related_name='deliveries')
    channel = models.CharField(max_length=20, choices=CHANNEL_CHOICES)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    
    # Delivery details
    recipient_address = models.CharField(max_length=255, help_text="Email, phone number, or device token")
    sent_at = models.DateTimeField(null=True, blank=True)
    delivered_at = models.DateTimeField(null=True, blank=True)
    
    # Provider details
    provider_id = models.CharField(max_length=255, blank=True, help_text="ID from email/SMS provider")
    provider_response = models.JSONField(default=dict, blank=True)
    
    # Error tracking
    error_code = models.CharField(max_length=50, blank=True)
    error_message = models.TextField(blank=True)
    retry_count = models.PositiveIntegerField(default=0)
    
    class Meta:
        db_table = 'notifications_delivery'
        ordering = ['-created_at']
        unique_together = ['notification', 'channel']

    def __str__(self):
        return f"{self.notification.title} via {self.channel} -> {self.recipient_address}"

    def mark_as_delivered(self, provider_id=None, provider_response=None):
        """Mark delivery as successful."""
        self.status = 'delivered'
        self.delivered_at = timezone.now()
        if provider_id:
            self.provider_id = provider_id
        if provider_response:
            self.provider_response = provider_response
        self.save()

    def mark_as_failed(self, error_code=None, error_message=None, provider_response=None):
        """Mark delivery as failed."""
        self.status = 'failed'
        self.retry_count += 1
        if error_code:
            self.error_code = error_code
        if error_message:
            self.error_message = error_message
        if provider_response:
            self.provider_response = provider_response
        self.save()


class NotificationTemplate(BaseModel):
    """
    Model for notification templates.
    """
    TEMPLATE_TYPE_CHOICES = [
        ('email_html', 'Email HTML'),
        ('email_text', 'Email Text'),
        ('sms', 'SMS'),
        ('push', 'Push Notification'),
        ('websocket', 'WebSocket'),
    ]
    
    name = models.CharField(max_length=100, unique=True)
    display_name = models.CharField(max_length=200)
    template_type = models.CharField(max_length=20, choices=TEMPLATE_TYPE_CHOICES)
    
    # Template content
    subject_template = models.CharField(max_length=255, blank=True, help_text="For email notifications")
    body_template = models.TextField(help_text="Template content with variables")
    
    # Template settings
    is_default = models.BooleanField(default=False)
    language = models.CharField(max_length=10, default='en')
    
    # Variables documentation
    available_variables = models.JSONField(
        default=list,
        blank=True,
        help_text="List of available variables for this template"
    )
    
    class Meta:
        db_table = 'notifications_template'
        ordering = ['template_type', 'name']
        unique_together = ['name', 'template_type', 'language']

    def __str__(self):
        return f"{self.display_name} ({self.template_type})"

    def render(self, context):
        """Render template with given context."""
        from jinja2 import Template
        
        try:
            # Render subject if applicable
            subject = ""
            if self.subject_template:
                subject_tmpl = Template(self.subject_template)
                subject = subject_tmpl.render(context)
            
            # Render body
            body_tmpl = Template(self.body_template)
            body = body_tmpl.render(context)
            
            return {
                'subject': subject,
                'body': body
            }
        except Exception as e:
            raise ValueError(f"Template rendering failed: {str(e)}")


class NotificationBatch(BaseModel):
    """
    Model for batch notifications.
    """
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ]
    
    name = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    notification_type = models.ForeignKey(NotificationType, on_delete=models.CASCADE)
    
    # Batch settings
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    scheduled_at = models.DateTimeField(default=timezone.now)
    
    # Recipients
    recipient_filter = models.JSONField(
        default=dict,
        help_text="Filter criteria for selecting recipients"
    )
    total_recipients = models.PositiveIntegerField(default=0)
    
    # Progress tracking
    notifications_created = models.PositiveIntegerField(default=0)
    notifications_sent = models.PositiveIntegerField(default=0)
    notifications_failed = models.PositiveIntegerField(default=0)
    
    # Timing
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    
    # Template and content
    template = models.ForeignKey(NotificationTemplate, on_delete=models.SET_NULL, null=True, blank=True)
    title_template = models.CharField(max_length=255)
    message_template = models.TextField()
    data_template = models.JSONField(default=dict, blank=True)
    
    class Meta:
        db_table = 'notifications_batch'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} ({self.status})"

    @property
    def progress_percentage(self):
        """Calculate progress percentage."""
        if self.total_recipients == 0:
            return 0
        return (self.notifications_sent + self.notifications_failed) / self.total_recipients * 100

    def start_processing(self):
        """Mark batch as started."""
        self.status = 'processing'
        self.started_at = timezone.now()
        self.save()

    def complete(self):
        """Mark batch as completed."""
        self.status = 'completed'
        self.completed_at = timezone.now()
        self.save()

    def fail(self, error_message=None):
        """Mark batch as failed."""
        self.status = 'failed'
        self.completed_at = timezone.now()
        self.save()
