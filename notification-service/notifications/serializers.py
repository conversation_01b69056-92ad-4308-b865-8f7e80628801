"""
Serializers for notification models.
"""
from rest_framework import serializers
from .models import (
    NotificationType, Notification, NotificationDelivery, 
    NotificationTemplate, NotificationBatch
)
from core.models import User


class NotificationTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = NotificationType
        fields = [
            'id', 'name', 'display_name', 'description', 'priority',
            'email_enabled', 'sms_enabled', 'push_enabled', 'websocket_enabled',
            'email_template', 'sms_template', 'push_template', 'websocket_template',
            'retry_count', 'retry_delay_minutes', 'expiry_hours',
            'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class NotificationSerializer(serializers.ModelSerializer):
    notification_type_name = serializers.CharField(source='notification_type.name', read_only=True)
    notification_type_display = serializers.CharField(source='notification_type.display_name', read_only=True)
    recipient_email = serializers.Char<PERSON><PERSON>(source='recipient.email', read_only=True)
    recipient_name = serializers.CharField(source='recipient.full_name', read_only=True)
    sender_name = serializers.CharField(source='sender.full_name', read_only=True)
    is_read = serializers.ReadOnlyField()
    is_expired = serializers.ReadOnlyField()
    
    class Meta:
        model = Notification
        fields = [
            'id', 'notification_type', 'notification_type_name', 'notification_type_display',
            'recipient', 'recipient_email', 'recipient_name', 'sender', 'sender_name',
            'title', 'message', 'data', 'status', 'scheduled_at', 'sent_at', 'expires_at',
            'delivery_attempts', 'last_attempt_at', 'error_message', 'source_service',
            'reference_id', 'read_at', 'clicked_at', 'is_read', 'is_expired',
            'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'sent_at', 'delivery_attempts', 'last_attempt_at', 'error_message',
            'read_at', 'clicked_at', 'created_at', 'updated_at'
        ]


class NotificationCreateSerializer(serializers.ModelSerializer):
    """Simplified serializer for creating notifications."""
    
    notification_type_name = serializers.CharField(write_only=True)
    recipient_id = serializers.UUIDField(write_only=True)
    sender_id = serializers.UUIDField(write_only=True, required=False)
    
    class Meta:
        model = Notification
        fields = [
            'notification_type_name', 'recipient_id', 'sender_id',
            'title', 'message', 'data', 'scheduled_at', 'source_service', 'reference_id'
        ]
    
    def create(self, validated_data):
        # Get notification type
        notification_type_name = validated_data.pop('notification_type_name')
        notification_type = NotificationType.objects.get(name=notification_type_name)
        
        # Get recipient
        recipient_id = validated_data.pop('recipient_id')
        recipient = User.objects.get(id=recipient_id)
        
        # Get sender if provided
        sender = None
        sender_id = validated_data.pop('sender_id', None)
        if sender_id:
            sender = User.objects.get(id=sender_id)
        
        # Create notification
        notification = Notification.objects.create(
            notification_type=notification_type,
            recipient=recipient,
            sender=sender,
            **validated_data
        )
        
        return notification


class NotificationDeliverySerializer(serializers.ModelSerializer):
    notification_title = serializers.CharField(source='notification.title', read_only=True)
    
    class Meta:
        model = NotificationDelivery
        fields = [
            'id', 'notification', 'notification_title', 'channel', 'status',
            'recipient_address', 'sent_at', 'delivered_at', 'provider_id',
            'provider_response', 'error_code', 'error_message', 'retry_count',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'sent_at', 'delivered_at', 'provider_id', 'provider_response',
            'error_code', 'error_message', 'retry_count', 'created_at', 'updated_at'
        ]


class NotificationTemplateSerializer(serializers.ModelSerializer):
    class Meta:
        model = NotificationTemplate
        fields = [
            'id', 'name', 'display_name', 'template_type', 'subject_template',
            'body_template', 'is_default', 'language', 'available_variables',
            'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class NotificationBatchSerializer(serializers.ModelSerializer):
    notification_type_name = serializers.CharField(source='notification_type.name', read_only=True)
    progress_percentage = serializers.ReadOnlyField()
    
    class Meta:
        model = NotificationBatch
        fields = [
            'id', 'name', 'description', 'notification_type', 'notification_type_name',
            'status', 'scheduled_at', 'recipient_filter', 'total_recipients',
            'notifications_created', 'notifications_sent', 'notifications_failed',
            'started_at', 'completed_at', 'template', 'title_template',
            'message_template', 'data_template', 'progress_percentage',
            'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'total_recipients', 'notifications_created', 'notifications_sent',
            'notifications_failed', 'started_at', 'completed_at', 'created_at', 'updated_at'
        ]


class NotificationBatchCreateSerializer(serializers.ModelSerializer):
    """Simplified serializer for creating notification batches."""
    
    notification_type_name = serializers.CharField(write_only=True)
    
    class Meta:
        model = NotificationBatch
        fields = [
            'notification_type_name', 'name', 'description', 'scheduled_at',
            'recipient_filter', 'title_template', 'message_template', 'data_template'
        ]
    
    def create(self, validated_data):
        # Get notification type
        notification_type_name = validated_data.pop('notification_type_name')
        notification_type = NotificationType.objects.get(name=notification_type_name)
        
        # Create batch
        batch = NotificationBatch.objects.create(
            notification_type=notification_type,
            **validated_data
        )
        
        return batch


class NotificationStatsSerializer(serializers.Serializer):
    """Serializer for notification statistics."""
    
    total_notifications = serializers.IntegerField()
    unread_notifications = serializers.IntegerField()
    notifications_today = serializers.IntegerField()
    notifications_this_week = serializers.IntegerField()
    
    by_type = serializers.DictField()
    by_status = serializers.DictField()
    by_channel = serializers.DictField()
    
    delivery_rate = serializers.FloatField()
    read_rate = serializers.FloatField()


class BulkNotificationSerializer(serializers.Serializer):
    """Serializer for bulk notification creation."""
    
    notification_type_name = serializers.CharField()
    recipient_ids = serializers.ListField(child=serializers.UUIDField())
    title = serializers.CharField(max_length=255)
    message = serializers.CharField()
    data = serializers.DictField(required=False, default=dict)
    scheduled_at = serializers.DateTimeField(required=False)
    source_service = serializers.CharField(required=False)
    reference_id = serializers.CharField(required=False)
