"""
Notification services for different delivery channels.
"""
from django.conf import settings
import logging

logger = logging.getLogger(__name__)


class SMSService:
    """
    SMS notification service using Twilio.
    """
    
    def __init__(self):
        self.enabled = settings.USE_SMS
        if self.enabled:
            try:
                from twilio.rest import Client
                self.client = Client(
                    settings.TWILIO_ACCOUNT_SID,
                    settings.TWILIO_AUTH_TOKEN
                )
                self.from_number = settings.TWILIO_PHONE_NUMBER
            except ImportError:
                logger.error("Twilio library not installed")
                self.enabled = False
            except Exception as e:
                logger.error(f"Failed to initialize Twilio client: {e}")
                self.enabled = False
    
    def send_notification_sms(self, notification):
        """
        Send SMS notification.
        """
        if not self.enabled:
            return {'success': False, 'error': 'SMS service not enabled'}
        
        try:
            # Get SMS template
            template = self._get_sms_template(notification)
            
            # Render message
            context = {
                'user': notification.recipient,
                'notification': notification,
                **notification.data
            }
            
            message_body = template.render(context)['body']
            
            # Truncate if too long (SMS limit is usually 160 characters)
            if len(message_body) > 160:
                message_body = message_body[:157] + "..."
            
            # Send SMS
            message = self.client.messages.create(
                body=message_body,
                from_=self.from_number,
                to=notification.recipient.phone_number
            )
            
            return {
                'success': True,
                'message_sid': message.sid,
                'status': message.status,
                'body': message_body
            }
            
        except Exception as e:
            logger.error(f"Failed to send SMS notification: {e}")
            return {'success': False, 'error': str(e)}
    
    def _get_sms_template(self, notification):
        """
        Get SMS template for notification type.
        """
        from .models import NotificationTemplate
        
        template_name = notification.notification_type.sms_template
        
        if template_name:
            try:
                return NotificationTemplate.objects.get(
                    name=template_name,
                    template_type='sms'
                )
            except NotificationTemplate.DoesNotExist:
                pass
        
        # Return default SMS template
        return NotificationTemplate.objects.filter(
            template_type='sms',
            is_default=True
        ).first()


class PushNotificationService:
    """
    Push notification service using Firebase Cloud Messaging.
    """
    
    def __init__(self):
        self.enabled = settings.USE_PUSH_NOTIFICATIONS
        if self.enabled:
            try:
                from pyfcm import FCMNotification
                self.fcm = FCMNotification(api_key=settings.FCM_SERVER_KEY)
            except ImportError:
                logger.error("PyFCM library not installed")
                self.enabled = False
            except Exception as e:
                logger.error(f"Failed to initialize FCM client: {e}")
                self.enabled = False
    
    def send_notification_push(self, notification):
        """
        Send push notification.
        """
        if not self.enabled:
            return {'success': False, 'error': 'Push notification service not enabled'}
        
        try:
            # Get push template
            template = self._get_push_template(notification)
            
            # Render message
            context = {
                'user': notification.recipient,
                'notification': notification,
                **notification.data
            }
            
            rendered = template.render(context)
            title = rendered['subject'] or notification.title
            body = rendered['body']
            
            # Prepare data payload
            data_payload = {
                'notification_id': str(notification.id),
                'type': notification.notification_type.name,
                'click_action': notification.data.get('click_action', ''),
                **notification.data
            }
            
            # Send push notification
            result = self.fcm.notify_single_device(
                registration_id=notification.recipient.fcm_token,
                message_title=title,
                message_body=body,
                data_message=data_payload,
                sound='default',
                badge=1
            )
            
            if result['success']:
                return {
                    'success': True,
                    'message_id': result.get('message_id'),
                    'multicast_id': result.get('multicast_id'),
                    'title': title,
                    'body': body
                }
            else:
                return {
                    'success': False,
                    'error': result.get('failure', 'Unknown error'),
                    'results': result.get('results', [])
                }
            
        except Exception as e:
            logger.error(f"Failed to send push notification: {e}")
            return {'success': False, 'error': str(e)}
    
    def _get_push_template(self, notification):
        """
        Get push template for notification type.
        """
        from .models import NotificationTemplate
        
        template_name = notification.notification_type.push_template
        
        if template_name:
            try:
                return NotificationTemplate.objects.get(
                    name=template_name,
                    template_type='push'
                )
            except NotificationTemplate.DoesNotExist:
                pass
        
        # Return default push template
        return NotificationTemplate.objects.filter(
            template_type='push',
            is_default=True
        ).first()


class NotificationService:
    """
    Main notification service for creating and sending notifications.
    """
    
    @staticmethod
    def create_notification(
        notification_type_name,
        recipient_id,
        title,
        message,
        data=None,
        sender_id=None,
        scheduled_at=None,
        source_service=None,
        reference_id=None
    ):
        """
        Create a new notification.
        """
        from .models import Notification, NotificationType
        from core.models import User
        
        try:
            # Get notification type
            notification_type = NotificationType.objects.get(name=notification_type_name)
            
            # Get recipient
            recipient = User.objects.get(id=recipient_id)
            
            # Get sender if provided
            sender = None
            if sender_id:
                try:
                    sender = User.objects.get(id=sender_id)
                except User.DoesNotExist:
                    pass
            
            # Create notification
            notification = Notification.objects.create(
                notification_type=notification_type,
                recipient=recipient,
                sender=sender,
                title=title,
                message=message,
                data=data or {},
                scheduled_at=scheduled_at,
                source_service=source_service,
                reference_id=reference_id
            )
            
            # Queue for sending if not scheduled for later
            from django.utils import timezone
            if not scheduled_at or scheduled_at <= timezone.now():
                from .tasks import send_notification
                send_notification.delay(str(notification.id))
            
            return {
                'success': True,
                'notification_id': str(notification.id),
                'status': 'created'
            }
            
        except Exception as e:
            logger.error(f"Failed to create notification: {e}")
            return {'success': False, 'error': str(e)}
    
    @staticmethod
    def create_batch_notification(
        notification_type_name,
        recipient_filter,
        title_template,
        message_template,
        data_template=None,
        scheduled_at=None,
        name=None,
        description=None
    ):
        """
        Create a batch notification.
        """
        from .models import NotificationBatch, NotificationType
        
        try:
            # Get notification type
            notification_type = NotificationType.objects.get(name=notification_type_name)
            
            # Create batch
            batch = NotificationBatch.objects.create(
                name=name or f"Batch {notification_type_name}",
                description=description or "",
                notification_type=notification_type,
                recipient_filter=recipient_filter,
                title_template=title_template,
                message_template=message_template,
                data_template=data_template or {},
                scheduled_at=scheduled_at
            )
            
            # Queue for processing if not scheduled for later
            from django.utils import timezone
            if not scheduled_at or scheduled_at <= timezone.now():
                from .tasks import process_notification_batch
                process_notification_batch.delay(str(batch.id))
            
            return {
                'success': True,
                'batch_id': str(batch.id),
                'status': 'created'
            }
            
        except Exception as e:
            logger.error(f"Failed to create batch notification: {e}")
            return {'success': False, 'error': str(e)}
    
    @staticmethod
    def send_timetable_update_notification(timetable_id, user_ids, update_type='generated'):
        """
        Send timetable update notifications.
        """
        notification_types = {
            'generated': 'timetable_generated',
            'published': 'timetable_published',
            'updated': 'timetable_updated',
            'conflict': 'timetable_conflict'
        }
        
        notification_type_name = notification_types.get(update_type, 'timetable_updated')
        
        results = []
        for user_id in user_ids:
            result = NotificationService.create_notification(
                notification_type_name=notification_type_name,
                recipient_id=user_id,
                title=f"Timetable {update_type.title()}",
                message=f"Your timetable has been {update_type}. Please check the latest version.",
                data={
                    'timetable_id': timetable_id,
                    'update_type': update_type,
                    'click_action': f'/timetables/{timetable_id}'
                },
                source_service='timetable_service',
                reference_id=f"timetable_{timetable_id}_{update_type}"
            )
            results.append(result)
        
        return results
    
    @staticmethod
    def send_preference_reminder(user_ids, deadline_date):
        """
        Send preference submission reminders.
        """
        results = []
        for user_id in user_ids:
            result = NotificationService.create_notification(
                notification_type_name='preference_reminder',
                recipient_id=user_id,
                title="Preference Submission Reminder",
                message=f"Please submit your preferences before {deadline_date.strftime('%B %d, %Y')}.",
                data={
                    'deadline_date': deadline_date.isoformat(),
                    'click_action': '/preferences'
                },
                source_service='preference_service',
                reference_id=f"preference_reminder_{deadline_date.strftime('%Y%m%d')}"
            )
            results.append(result)
        
        return results
