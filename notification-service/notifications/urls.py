"""
URLs configuration for notifications app.
"""
from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON><PERSON><PERSON>
from . import views

app_name = 'notifications'

# Create router for viewsets
router = DefaultRouter()
router.register(r'types', views.NotificationTypeViewSet, basename='notification-type')
router.register(r'deliveries', views.NotificationDeliveryViewSet, basename='notification-delivery')
router.register(r'templates', views.NotificationTemplateViewSet, basename='notification-template')
router.register(r'batches', views.NotificationBatchViewSet, basename='notification-batch')
router.register(r'', views.NotificationViewSet, basename='notification')

urlpatterns = [
    # ViewSet URLs
    path('', include(router.urls)),
    
    # Additional endpoints
    path('create/', views.create_notification, name='create-notification'),
    path('create-bulk/', views.create_bulk_notifications, name='create-bulk-notifications'),
]
