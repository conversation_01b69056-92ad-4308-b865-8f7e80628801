"""
Celery tasks for notification processing.
"""
from celery import shared_task
from django.utils import timezone
from django.conf import settings
from .models import Notification, NotificationDelivery, NotificationBatch, NotificationType
from core.models import User
from datetime import timedelta
import logging

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3)
def send_notification(self, notification_id):
    """
    Send a notification via all enabled channels.
    """
    try:
        notification = Notification.objects.get(id=notification_id)
        
        if notification.is_expired:
            notification.status = 'expired'
            notification.save()
            return {'status': 'expired', 'notification_id': str(notification_id)}
        
        notification.status = 'processing'
        notification.delivery_attempts += 1
        notification.last_attempt_at = timezone.now()
        notification.save()
        
        results = {}
        success_count = 0
        
        # Send via email if enabled
        if notification.notification_type.email_enabled and notification.recipient.can_receive_email():
            try:
                result = send_email_notification.delay(notification_id)
                results['email'] = 'queued'
                success_count += 1
            except Exception as e:
                results['email'] = f'failed: {str(e)}'
                logger.error(f"Failed to queue email notification {notification_id}: {e}")
        
        # Send via SMS if enabled
        if notification.notification_type.sms_enabled and notification.recipient.can_receive_sms():
            try:
                result = send_sms_notification.delay(notification_id)
                results['sms'] = 'queued'
                success_count += 1
            except Exception as e:
                results['sms'] = f'failed: {str(e)}'
                logger.error(f"Failed to queue SMS notification {notification_id}: {e}")
        
        # Send via push if enabled
        if notification.notification_type.push_enabled and notification.recipient.can_receive_push():
            try:
                result = send_push_notification.delay(notification_id)
                results['push'] = 'queued'
                success_count += 1
            except Exception as e:
                results['push'] = f'failed: {str(e)}'
                logger.error(f"Failed to queue push notification {notification_id}: {e}")
        
        # Send via WebSocket if enabled
        if notification.notification_type.websocket_enabled and notification.recipient.can_receive_in_app():
            try:
                result = send_websocket_notification.delay(notification_id)
                results['websocket'] = 'queued'
                success_count += 1
            except Exception as e:
                results['websocket'] = f'failed: {str(e)}'
                logger.error(f"Failed to queue WebSocket notification {notification_id}: {e}")
        
        # Update notification status
        if success_count > 0:
            notification.status = 'sent'
            notification.sent_at = timezone.now()
        else:
            notification.status = 'failed'
            notification.error_message = 'No delivery channels available or all failed'
        
        notification.save()
        
        return {
            'status': notification.status,
            'notification_id': str(notification_id),
            'channels': results,
            'success_count': success_count
        }
        
    except Notification.DoesNotExist:
        logger.error(f"Notification {notification_id} not found")
        return {'status': 'error', 'message': 'Notification not found'}
    
    except Exception as e:
        logger.error(f"Failed to send notification {notification_id}: {e}")
        
        # Retry if possible
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60 * (self.request.retries + 1))
        
        # Mark as failed if max retries reached
        try:
            notification = Notification.objects.get(id=notification_id)
            notification.status = 'failed'
            notification.error_message = str(e)
            notification.save()
        except:
            pass
        
        return {'status': 'failed', 'error': str(e)}


@shared_task(bind=True, max_retries=3)
def send_email_notification(self, notification_id):
    """
    Send email notification.
    """
    try:
        notification = Notification.objects.get(id=notification_id)
        
        # Get or create delivery record
        delivery, created = NotificationDelivery.objects.get_or_create(
            notification=notification,
            channel='email',
            defaults={
                'recipient_address': notification.recipient.email,
                'status': 'processing'
            }
        )
        
        if not created:
            delivery.status = 'processing'
            delivery.retry_count += 1
            delivery.save()
        
        # Import email service
        from email_service.services import EmailService
        
        email_service = EmailService()
        result = email_service.send_notification_email(notification)
        
        if result['success']:
            delivery.mark_as_delivered(
                provider_id=result.get('message_id'),
                provider_response=result
            )
            logger.info(f"Email notification {notification_id} sent successfully")
        else:
            delivery.mark_as_failed(
                error_message=result.get('error'),
                provider_response=result
            )
            logger.error(f"Email notification {notification_id} failed: {result.get('error')}")
        
        return result
        
    except Exception as e:
        logger.error(f"Failed to send email notification {notification_id}: {e}")
        
        # Update delivery record
        try:
            delivery = NotificationDelivery.objects.get(
                notification_id=notification_id,
                channel='email'
            )
            delivery.mark_as_failed(error_message=str(e))
        except:
            pass
        
        # Retry if possible
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60 * (self.request.retries + 1))
        
        return {'success': False, 'error': str(e)}


@shared_task(bind=True, max_retries=3)
def send_sms_notification(self, notification_id):
    """
    Send SMS notification.
    """
    try:
        notification = Notification.objects.get(id=notification_id)
        
        # Get or create delivery record
        delivery, created = NotificationDelivery.objects.get_or_create(
            notification=notification,
            channel='sms',
            defaults={
                'recipient_address': notification.recipient.phone_number,
                'status': 'processing'
            }
        )
        
        if not created:
            delivery.status = 'processing'
            delivery.retry_count += 1
            delivery.save()
        
        # Import SMS service
        from .services import SMSService
        
        sms_service = SMSService()
        result = sms_service.send_notification_sms(notification)
        
        if result['success']:
            delivery.mark_as_delivered(
                provider_id=result.get('message_sid'),
                provider_response=result
            )
            logger.info(f"SMS notification {notification_id} sent successfully")
        else:
            delivery.mark_as_failed(
                error_message=result.get('error'),
                provider_response=result
            )
            logger.error(f"SMS notification {notification_id} failed: {result.get('error')}")
        
        return result
        
    except Exception as e:
        logger.error(f"Failed to send SMS notification {notification_id}: {e}")
        
        # Update delivery record
        try:
            delivery = NotificationDelivery.objects.get(
                notification_id=notification_id,
                channel='sms'
            )
            delivery.mark_as_failed(error_message=str(e))
        except:
            pass
        
        # Retry if possible
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60 * (self.request.retries + 1))
        
        return {'success': False, 'error': str(e)}


@shared_task(bind=True, max_retries=3)
def send_push_notification(self, notification_id):
    """
    Send push notification.
    """
    try:
        notification = Notification.objects.get(id=notification_id)
        
        # Get or create delivery record
        delivery, created = NotificationDelivery.objects.get_or_create(
            notification=notification,
            channel='push',
            defaults={
                'recipient_address': notification.recipient.fcm_token,
                'status': 'processing'
            }
        )
        
        if not created:
            delivery.status = 'processing'
            delivery.retry_count += 1
            delivery.save()
        
        # Import push service
        from .services import PushNotificationService
        
        push_service = PushNotificationService()
        result = push_service.send_notification_push(notification)
        
        if result['success']:
            delivery.mark_as_delivered(
                provider_id=result.get('message_id'),
                provider_response=result
            )
            logger.info(f"Push notification {notification_id} sent successfully")
        else:
            delivery.mark_as_failed(
                error_message=result.get('error'),
                provider_response=result
            )
            logger.error(f"Push notification {notification_id} failed: {result.get('error')}")
        
        return result
        
    except Exception as e:
        logger.error(f"Failed to send push notification {notification_id}: {e}")
        
        # Update delivery record
        try:
            delivery = NotificationDelivery.objects.get(
                notification_id=notification_id,
                channel='push'
            )
            delivery.mark_as_failed(error_message=str(e))
        except:
            pass
        
        # Retry if possible
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60 * (self.request.retries + 1))
        
        return {'success': False, 'error': str(e)}


@shared_task
def send_websocket_notification(notification_id):
    """
    Send WebSocket notification.
    """
    try:
        notification = Notification.objects.get(id=notification_id)
        
        # Get or create delivery record
        delivery, created = NotificationDelivery.objects.get_or_create(
            notification=notification,
            channel='websocket',
            defaults={
                'recipient_address': f"user_{notification.recipient.id}",
                'status': 'processing'
            }
        )
        
        if not created:
            delivery.status = 'processing'
            delivery.retry_count += 1
            delivery.save()
        
        # Import WebSocket service
        from websockets.services import WebSocketService
        
        websocket_service = WebSocketService()
        result = websocket_service.send_notification(notification)
        
        if result['success']:
            delivery.mark_as_delivered(provider_response=result)
            logger.info(f"WebSocket notification {notification_id} sent successfully")
        else:
            delivery.mark_as_failed(
                error_message=result.get('error'),
                provider_response=result
            )
            logger.error(f"WebSocket notification {notification_id} failed: {result.get('error')}")
        
        return result
        
    except Exception as e:
        logger.error(f"Failed to send WebSocket notification {notification_id}: {e}")
        
        # Update delivery record
        try:
            delivery = NotificationDelivery.objects.get(
                notification_id=notification_id,
                channel='websocket'
            )
            delivery.mark_as_failed(error_message=str(e))
        except:
            pass
        
        return {'success': False, 'error': str(e)}


@shared_task
def process_pending_notifications():
    """
    Process pending notifications that are scheduled to be sent.
    """
    now = timezone.now()
    
    # Get pending notifications that are due
    pending_notifications = Notification.objects.filter(
        status='pending',
        scheduled_at__lte=now
    ).exclude(
        expires_at__lt=now
    )[:100]  # Process in batches
    
    processed_count = 0
    
    for notification in pending_notifications:
        try:
            send_notification.delay(str(notification.id))
            processed_count += 1
        except Exception as e:
            logger.error(f"Failed to queue notification {notification.id}: {e}")
    
    logger.info(f"Queued {processed_count} pending notifications")
    return {'processed': processed_count}


@shared_task
def cleanup_old_notifications():
    """
    Clean up old notifications based on retention policy.
    """
    cutoff_date = timezone.now() - timedelta(days=settings.NOTIFICATION_RETENTION_DAYS)
    
    # Delete old notifications
    deleted_notifications = Notification.objects.filter(
        created_at__lt=cutoff_date,
        status__in=['sent', 'failed', 'expired']
    ).delete()
    
    # Delete old deliveries
    deleted_deliveries = NotificationDelivery.objects.filter(
        created_at__lt=cutoff_date
    ).delete()
    
    logger.info(f"Cleaned up {deleted_notifications[0]} notifications and {deleted_deliveries[0]} deliveries")
    
    return {
        'deleted_notifications': deleted_notifications[0],
        'deleted_deliveries': deleted_deliveries[0]
    }


@shared_task
def cleanup_failed_notifications():
    """
    Clean up failed notifications that can't be retried.
    """
    # Mark expired notifications
    expired_count = Notification.objects.filter(
        status__in=['pending', 'processing'],
        expires_at__lt=timezone.now()
    ).update(status='expired')
    
    # Clean up old failed notifications
    old_failed = Notification.objects.filter(
        status='failed',
        created_at__lt=timezone.now() - timedelta(days=7)
    ).delete()
    
    logger.info(f"Marked {expired_count} notifications as expired, deleted {old_failed[0]} old failed notifications")
    
    return {
        'expired_count': expired_count,
        'deleted_failed': old_failed[0]
    }


@shared_task
def process_notification_batch(batch_id):
    """
    Process a batch notification.
    """
    try:
        batch = NotificationBatch.objects.get(id=batch_id)
        batch.start_processing()
        
        # Get recipients based on filter
        recipients = User.objects.filter(**batch.recipient_filter)
        batch.total_recipients = recipients.count()
        batch.save()
        
        # Create notifications for each recipient
        for recipient in recipients:
            try:
                # Render templates with recipient context
                context = {
                    'user': recipient,
                    'recipient': recipient,
                    **batch.data_template
                }
                
                from jinja2 import Template
                title = Template(batch.title_template).render(context)
                message = Template(batch.message_template).render(context)
                
                notification = Notification.objects.create(
                    notification_type=batch.notification_type,
                    recipient=recipient,
                    title=title,
                    message=message,
                    data=context,
                    source_service='notification_service',
                    reference_id=f"batch_{batch.id}"
                )
                
                # Queue for sending
                send_notification.delay(str(notification.id))
                batch.notifications_created += 1
                
            except Exception as e:
                logger.error(f"Failed to create notification for user {recipient.id} in batch {batch_id}: {e}")
                batch.notifications_failed += 1
        
        batch.complete()
        
        logger.info(f"Batch {batch_id} processed: {batch.notifications_created} notifications created")
        
        return {
            'batch_id': str(batch_id),
            'total_recipients': batch.total_recipients,
            'notifications_created': batch.notifications_created,
            'notifications_failed': batch.notifications_failed
        }
        
    except Exception as e:
        logger.error(f"Failed to process batch {batch_id}: {e}")
        
        try:
            batch = NotificationBatch.objects.get(id=batch_id)
            batch.fail()
        except:
            pass
        
        return {'error': str(e)}


@shared_task
def send_digest_notifications():
    """
    Send digest notifications for users who prefer them.
    """
    # This would implement digest functionality
    # For now, just a placeholder
    logger.info("Digest notifications task executed")
    return {'status': 'completed'}
