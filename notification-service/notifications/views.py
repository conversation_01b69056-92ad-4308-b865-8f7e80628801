"""
Views for notification management.
"""
from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter
from django.shortcuts import get_object_or_404
from django.utils import timezone
from datetime import timed<PERSON><PERSON>
from .models import (
    NotificationType, Notification, NotificationDelivery,
    NotificationTemplate, NotificationBatch
)
from .serializers import (
    NotificationTypeSerializer, NotificationSerializer, NotificationCreateSerializer,
    NotificationDeliverySerializer, NotificationTemplateSerializer,
    NotificationBatchSerializer, NotificationBatchCreateSerializer,
    NotificationStatsSerializer, BulkNotificationSerializer
)
from .services import NotificationService
from core.models import User
import logging

logger = logging.getLogger(__name__)


class NotificationTypeViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing notification types.
    """
    queryset = NotificationType.objects.all()
    serializer_class = NotificationTypeSerializer
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['priority', 'email_enabled', 'sms_enabled', 'push_enabled', 'websocket_enabled']
    search_fields = ['name', 'display_name', 'description']
    ordering_fields = ['name', 'priority', 'created_at']
    ordering = ['priority', 'name']


class NotificationViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing notifications.
    """
    serializer_class = NotificationSerializer
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['status', 'notification_type', 'recipient', 'sender', 'source_service']
    search_fields = ['title', 'message']
    ordering_fields = ['created_at', 'scheduled_at', 'sent_at']
    ordering = ['-created_at']
    
    def get_queryset(self):
        """Filter notifications based on user permissions."""
        user = self.request.user
        
        if user.is_superuser or user.role in ['admin', 'superadmin']:
            return Notification.objects.all()
        else:
            # Users can only see their own notifications
            return Notification.objects.filter(recipient=user)
    
    def get_serializer_class(self):
        if self.action == 'create':
            return NotificationCreateSerializer
        return NotificationSerializer
    
    @action(detail=True, methods=['post'])
    def mark_as_read(self, request, pk=None):
        """Mark notification as read."""
        notification = self.get_object()
        
        # Check if user can mark this notification as read
        if notification.recipient != request.user and not request.user.is_staff:
            return Response(
                {'error': 'You can only mark your own notifications as read'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        notification.mark_as_read()
        
        return Response({
            'message': 'Notification marked as read',
            'read_at': notification.read_at
        })
    
    @action(detail=True, methods=['post'])
    def mark_as_clicked(self, request, pk=None):
        """Mark notification as clicked."""
        notification = self.get_object()
        
        # Check if user can mark this notification as clicked
        if notification.recipient != request.user and not request.user.is_staff:
            return Response(
                {'error': 'You can only mark your own notifications as clicked'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        notification.mark_as_clicked()
        
        return Response({
            'message': 'Notification marked as clicked',
            'clicked_at': notification.clicked_at,
            'read_at': notification.read_at
        })
    
    @action(detail=False, methods=['post'])
    def mark_all_as_read(self, request):
        """Mark all user's notifications as read."""
        count = Notification.objects.filter(
            recipient=request.user,
            read_at__isnull=True
        ).update(read_at=timezone.now())
        
        return Response({
            'message': f'{count} notifications marked as read',
            'marked_count': count
        })
    
    @action(detail=False, methods=['get'])
    def unread(self, request):
        """Get unread notifications for the current user."""
        notifications = Notification.objects.filter(
            recipient=request.user,
            read_at__isnull=True,
            status='sent'
        ).order_by('-created_at')
        
        page = self.paginate_queryset(notifications)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(notifications, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get notification statistics for the current user."""
        user = request.user
        now = timezone.now()
        today = now.date()
        week_ago = now - timedelta(days=7)
        
        # Calculate statistics
        total_notifications = Notification.objects.filter(recipient=user).count()
        unread_notifications = Notification.objects.filter(
            recipient=user,
            read_at__isnull=True,
            status='sent'
        ).count()
        notifications_today = Notification.objects.filter(
            recipient=user,
            created_at__date=today
        ).count()
        notifications_this_week = Notification.objects.filter(
            recipient=user,
            created_at__gte=week_ago
        ).count()
        
        # Group by type
        by_type = {}
        for notification_type in NotificationType.objects.all():
            count = Notification.objects.filter(
                recipient=user,
                notification_type=notification_type
            ).count()
            if count > 0:
                by_type[notification_type.name] = count
        
        # Group by status
        by_status = {}
        for status_choice in Notification.STATUS_CHOICES:
            status_value = status_choice[0]
            count = Notification.objects.filter(
                recipient=user,
                status=status_value
            ).count()
            if count > 0:
                by_status[status_value] = count
        
        # Group by delivery channel
        by_channel = {}
        for channel_choice in NotificationDelivery.CHANNEL_CHOICES:
            channel_value = channel_choice[0]
            count = NotificationDelivery.objects.filter(
                notification__recipient=user,
                channel=channel_value
            ).count()
            if count > 0:
                by_channel[channel_value] = count
        
        # Calculate rates
        sent_notifications = Notification.objects.filter(recipient=user, status='sent').count()
        read_notifications = Notification.objects.filter(
            recipient=user,
            read_at__isnull=False
        ).count()
        
        delivery_rate = (sent_notifications / total_notifications * 100) if total_notifications > 0 else 0
        read_rate = (read_notifications / sent_notifications * 100) if sent_notifications > 0 else 0
        
        stats_data = {
            'total_notifications': total_notifications,
            'unread_notifications': unread_notifications,
            'notifications_today': notifications_today,
            'notifications_this_week': notifications_this_week,
            'by_type': by_type,
            'by_status': by_status,
            'by_channel': by_channel,
            'delivery_rate': round(delivery_rate, 2),
            'read_rate': round(read_rate, 2)
        }
        
        serializer = NotificationStatsSerializer(stats_data)
        return Response(serializer.data)


class NotificationDeliveryViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for viewing notification deliveries.
    """
    serializer_class = NotificationDeliverySerializer
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['channel', 'status', 'notification']
    search_fields = ['recipient_address', 'error_message']
    ordering_fields = ['created_at', 'sent_at', 'delivered_at']
    ordering = ['-created_at']
    
    def get_queryset(self):
        """Filter deliveries based on user permissions."""
        user = self.request.user
        
        if user.is_superuser or user.role in ['admin', 'superadmin']:
            return NotificationDelivery.objects.all()
        else:
            # Users can only see deliveries for their own notifications
            return NotificationDelivery.objects.filter(notification__recipient=user)


class NotificationTemplateViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing notification templates.
    """
    queryset = NotificationTemplate.objects.all()
    serializer_class = NotificationTemplateSerializer
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['template_type', 'is_default', 'language']
    search_fields = ['name', 'display_name']
    ordering_fields = ['name', 'template_type', 'created_at']
    ordering = ['template_type', 'name']
    
    @action(detail=True, methods=['post'])
    def test_render(self, request, pk=None):
        """Test template rendering with sample data."""
        template = self.get_object()
        
        # Sample context data
        sample_context = {
            'user': {
                'first_name': 'John',
                'last_name': 'Doe',
                'email': '<EMAIL>'
            },
            'notification': {
                'title': 'Test Notification',
                'message': 'This is a test message'
            },
            'site_name': 'Timetable Management System',
            'site_url': 'http://localhost:3000'
        }
        
        # Override with provided context
        context = {**sample_context, **request.data.get('context', {})}
        
        try:
            rendered = template.render(context)
            return Response({
                'success': True,
                'rendered': rendered,
                'context_used': context
            })
        except Exception as e:
            return Response({
                'success': False,
                'error': str(e),
                'context_used': context
            }, status=status.HTTP_400_BAD_REQUEST)


class NotificationBatchViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing notification batches.
    """
    queryset = NotificationBatch.objects.all()
    serializer_class = NotificationBatchSerializer
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['status', 'notification_type']
    search_fields = ['name', 'description']
    ordering_fields = ['created_at', 'scheduled_at', 'completed_at']
    ordering = ['-created_at']
    
    def get_serializer_class(self):
        if self.action == 'create':
            return NotificationBatchCreateSerializer
        return NotificationBatchSerializer
    
    @action(detail=True, methods=['post'])
    def process(self, request, pk=None):
        """Process a notification batch."""
        batch = self.get_object()
        
        if batch.status != 'pending':
            return Response(
                {'error': 'Only pending batches can be processed'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Queue batch for processing
        from .tasks import process_notification_batch
        task = process_notification_batch.delay(str(batch.id))
        
        return Response({
            'message': 'Batch processing started',
            'task_id': task.id,
            'batch_id': str(batch.id)
        }, status=status.HTTP_202_ACCEPTED)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def create_notification(request):
    """
    Create a single notification.
    """
    serializer = NotificationCreateSerializer(data=request.data)
    if serializer.is_valid():
        try:
            notification = serializer.save()
            
            # Queue for sending
            from .tasks import send_notification
            send_notification.delay(str(notification.id))
            
            response_serializer = NotificationSerializer(notification)
            return Response(response_serializer.data, status=status.HTTP_201_CREATED)
            
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
    
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def create_bulk_notifications(request):
    """
    Create multiple notifications at once.
    """
    serializer = BulkNotificationSerializer(data=request.data)
    if serializer.is_valid():
        try:
            data = serializer.validated_data
            
            # Create notifications for each recipient
            notifications = []
            for recipient_id in data['recipient_ids']:
                result = NotificationService.create_notification(
                    notification_type_name=data['notification_type_name'],
                    recipient_id=recipient_id,
                    title=data['title'],
                    message=data['message'],
                    data=data.get('data', {}),
                    scheduled_at=data.get('scheduled_at'),
                    source_service=data.get('source_service'),
                    reference_id=data.get('reference_id')
                )
                
                if result['success']:
                    notifications.append(result['notification_id'])
            
            return Response({
                'message': f'{len(notifications)} notifications created',
                'notification_ids': notifications,
                'total_recipients': len(data['recipient_ids'])
            }, status=status.HTTP_201_CREATED)
            
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
    
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
