"""
Admin configuration for notification models.
"""
from django.contrib import admin
from .models import (
    NotificationType, Notification, NotificationDelivery,
    NotificationTemplate, NotificationBatch
)


@admin.register(NotificationType)
class NotificationTypeAdmin(admin.ModelAdmin):
    list_display = ('name', 'display_name', 'priority', 'email_enabled', 'sms_enabled', 'push_enabled', 'websocket_enabled')
    list_filter = ('priority', 'email_enabled', 'sms_enabled', 'push_enabled', 'websocket_enabled')
    search_fields = ('name', 'display_name', 'description')
    ordering = ('priority', 'name')
    
    fieldsets = (
        (None, {
            'fields': ('name', 'display_name', 'description', 'priority')
        }),
        ('Channel Settings', {
            'fields': ('email_enabled', 'sms_enabled', 'push_enabled', 'websocket_enabled')
        }),
        ('Templates', {
            'fields': ('email_template', 'sms_template', 'push_template', 'websocket_template'),
            'classes': ('collapse',)
        }),
        ('Delivery Settings', {
            'fields': ('retry_count', 'retry_delay_minutes', 'expiry_hours'),
            'classes': ('collapse',)
        }),
    )


@admin.register(Notification)
class NotificationAdmin(admin.ModelAdmin):
    list_display = ('title', 'recipient', 'notification_type', 'status', 'scheduled_at', 'sent_at', 'is_read')
    list_filter = ('status', 'notification_type', 'source_service', 'created_at')
    search_fields = ('title', 'message', 'recipient__email', 'recipient__username')
    ordering = ('-created_at',)
    readonly_fields = ('delivery_attempts', 'last_attempt_at', 'sent_at', 'read_at', 'clicked_at')
    
    fieldsets = (
        (None, {
            'fields': ('notification_type', 'recipient', 'sender', 'title', 'message')
        }),
        ('Data', {
            'fields': ('data',),
            'classes': ('collapse',)
        }),
        ('Scheduling', {
            'fields': ('status', 'scheduled_at', 'expires_at')
        }),
        ('Delivery Tracking', {
            'fields': ('delivery_attempts', 'last_attempt_at', 'sent_at', 'error_message'),
            'classes': ('collapse',)
        }),
        ('User Interaction', {
            'fields': ('read_at', 'clicked_at'),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('source_service', 'reference_id'),
            'classes': ('collapse',)
        }),
    )
    
    def is_read(self, obj):
        return obj.is_read
    is_read.boolean = True
    is_read.short_description = 'Read'


@admin.register(NotificationDelivery)
class NotificationDeliveryAdmin(admin.ModelAdmin):
    list_display = ('notification', 'channel', 'status', 'recipient_address', 'sent_at', 'delivered_at', 'retry_count')
    list_filter = ('channel', 'status', 'created_at')
    search_fields = ('notification__title', 'recipient_address', 'error_message')
    ordering = ('-created_at',)
    readonly_fields = ('sent_at', 'delivered_at', 'provider_id', 'provider_response', 'retry_count')
    
    fieldsets = (
        (None, {
            'fields': ('notification', 'channel', 'status', 'recipient_address')
        }),
        ('Delivery Details', {
            'fields': ('sent_at', 'delivered_at', 'provider_id', 'provider_response'),
            'classes': ('collapse',)
        }),
        ('Error Tracking', {
            'fields': ('error_code', 'error_message', 'retry_count'),
            'classes': ('collapse',)
        }),
    )


@admin.register(NotificationTemplate)
class NotificationTemplateAdmin(admin.ModelAdmin):
    list_display = ('name', 'display_name', 'template_type', 'is_default', 'language')
    list_filter = ('template_type', 'is_default', 'language')
    search_fields = ('name', 'display_name')
    ordering = ('template_type', 'name')
    
    fieldsets = (
        (None, {
            'fields': ('name', 'display_name', 'template_type', 'language')
        }),
        ('Settings', {
            'fields': ('is_default', 'available_variables')
        }),
        ('Template Content', {
            'fields': ('subject_template', 'body_template')
        }),
    )


@admin.register(NotificationBatch)
class NotificationBatchAdmin(admin.ModelAdmin):
    list_display = ('name', 'notification_type', 'status', 'total_recipients', 'notifications_created', 'progress_percentage', 'scheduled_at')
    list_filter = ('status', 'notification_type', 'created_at')
    search_fields = ('name', 'description')
    ordering = ('-created_at',)
    readonly_fields = ('total_recipients', 'notifications_created', 'notifications_sent', 'notifications_failed', 'started_at', 'completed_at', 'progress_percentage')
    
    fieldsets = (
        (None, {
            'fields': ('name', 'description', 'notification_type', 'status', 'scheduled_at')
        }),
        ('Recipients', {
            'fields': ('recipient_filter', 'total_recipients')
        }),
        ('Progress', {
            'fields': ('notifications_created', 'notifications_sent', 'notifications_failed', 'progress_percentage'),
            'classes': ('collapse',)
        }),
        ('Timing', {
            'fields': ('started_at', 'completed_at'),
            'classes': ('collapse',)
        }),
        ('Templates', {
            'fields': ('template', 'title_template', 'message_template', 'data_template'),
            'classes': ('collapse',)
        }),
    )
    
    def progress_percentage(self, obj):
        return f"{obj.progress_percentage:.1f}%"
    progress_percentage.short_description = 'Progress'
