"""
Management command to set up default notification types and templates.
"""
from django.core.management.base import BaseCommand
from notifications.models import NotificationType, NotificationTemplate


class Command(BaseCommand):
    help = 'Set up default notification types and templates'

    def handle(self, *args, **options):
        self.stdout.write('Setting up default notification types and templates...')
        
        # Create notification types
        notification_types = [
            {
                'name': 'timetable_generated',
                'display_name': 'Timetable Generated',
                'description': 'Notification sent when a timetable is generated',
                'priority': 'normal',
                'email_enabled': True,
                'push_enabled': True,
                'websocket_enabled': True,
                'email_template': 'timetable_generated_email',
                'push_template': 'timetable_generated_push',
                'websocket_template': 'timetable_generated_websocket'
            },
            {
                'name': 'timetable_published',
                'display_name': 'Timetable Published',
                'description': 'Notification sent when a timetable is published',
                'priority': 'high',
                'email_enabled': True,
                'push_enabled': True,
                'websocket_enabled': True,
                'email_template': 'timetable_published_email',
                'push_template': 'timetable_published_push',
                'websocket_template': 'timetable_published_websocket'
            },
            {
                'name': 'timetable_updated',
                'display_name': 'Timetable Updated',
                'description': 'Notification sent when a timetable is updated',
                'priority': 'normal',
                'email_enabled': True,
                'push_enabled': True,
                'websocket_enabled': True,
                'email_template': 'timetable_updated_email',
                'push_template': 'timetable_updated_push',
                'websocket_template': 'timetable_updated_websocket'
            },
            {
                'name': 'preference_reminder',
                'display_name': 'Preference Reminder',
                'description': 'Reminder to submit preferences',
                'priority': 'normal',
                'email_enabled': True,
                'push_enabled': True,
                'websocket_enabled': True,
                'email_template': 'preference_reminder_email',
                'push_template': 'preference_reminder_push',
                'websocket_template': 'preference_reminder_websocket'
            },
            {
                'name': 'timetable_conflict',
                'display_name': 'Timetable Conflict',
                'description': 'Notification about timetable conflicts',
                'priority': 'high',
                'email_enabled': True,
                'push_enabled': True,
                'websocket_enabled': True,
                'email_template': 'timetable_conflict_email',
                'push_template': 'timetable_conflict_push',
                'websocket_template': 'timetable_conflict_websocket'
            },
            {
                'name': 'system_alert',
                'display_name': 'System Alert',
                'description': 'System-wide alerts and announcements',
                'priority': 'urgent',
                'email_enabled': True,
                'sms_enabled': True,
                'push_enabled': True,
                'websocket_enabled': True,
                'email_template': 'system_alert_email',
                'sms_template': 'system_alert_sms',
                'push_template': 'system_alert_push',
                'websocket_template': 'system_alert_websocket'
            }
        ]
        
        for nt_data in notification_types:
            notification_type, created = NotificationType.objects.get_or_create(
                name=nt_data['name'],
                defaults=nt_data
            )
            if created:
                self.stdout.write(f'Created notification type: {notification_type.display_name}')
            else:
                self.stdout.write(f'Notification type already exists: {notification_type.display_name}')
        
        # Create email templates
        email_templates = [
            {
                'name': 'default_email',
                'display_name': 'Default Email Template',
                'template_type': 'email_html',
                'is_default': True,
                'subject_template': '{{ notification.title }}',
                'body_template': '''
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ notification.title }}</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #007bff; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { padding: 30px; background-color: #f8f9fa; border-radius: 0 0 8px 8px; }
        .footer { padding: 20px; text-align: center; font-size: 12px; color: #666; }
        .button { display: inline-block; padding: 12px 24px; background-color: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 10px 0; }
        .button:hover { background-color: #0056b3; }
        .alert { padding: 15px; margin: 15px 0; border-radius: 5px; }
        .alert-info { background-color: #d1ecf1; border-left: 4px solid #bee5eb; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{{ site_name or "Timetable Management System" }}</h1>
        </div>
        <div class="content">
            <h2>{{ notification.title }}</h2>
            <p>Hello {{ user.first_name or user.username }},</p>
            <div class="alert alert-info">
                <p>{{ notification.message }}</p>
            </div>
            {% if notification.data.click_action %}
            <p>
                <a href="{{ site_url }}{{ notification.data.click_action }}" class="button">
                    View Details
                </a>
            </p>
            {% endif %}
            <p>Best regards,<br>The Timetable Management Team</p>
        </div>
        <div class="footer">
            <p>This is an automated message from {{ site_name or "Timetable Management System" }}.</p>
            <p>If you no longer wish to receive these emails, you can update your notification preferences in your account settings.</p>
        </div>
    </div>
</body>
</html>
                ''',
                'available_variables': [
                    'user.first_name', 'user.last_name', 'user.email',
                    'notification.title', 'notification.message', 'notification.data',
                    'site_name', 'site_url'
                ]
            },
            {
                'name': 'timetable_generated_email',
                'display_name': 'Timetable Generated Email',
                'template_type': 'email_html',
                'subject_template': 'Your Timetable Has Been Generated',
                'body_template': '''
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Timetable Generated</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #28a745; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background-color: #f8f9fa; }
        .button { display: inline-block; padding: 10px 20px; background-color: #28a745; color: white; text-decoration: none; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎉 Timetable Generated Successfully!</h1>
        </div>
        <div class="content">
            <p>Hello {{ user.first_name or user.username }},</p>
            <p>Great news! Your timetable has been successfully generated and is now ready for review.</p>
            <p><strong>Timetable Details:</strong></p>
            <ul>
                <li>Academic Year: {{ notification.data.academic_year or "Current" }}</li>
                <li>Semester: {{ notification.data.semester or "Current" }}</li>
                <li>Generated: {{ notification.created_at.strftime('%B %d, %Y at %I:%M %p') }}</li>
            </ul>
            <p>
                <a href="{{ site_url }}/timetables/{{ notification.data.timetable_id }}" class="button">
                    View Your Timetable
                </a>
            </p>
            <p>Best regards,<br>The Timetable Management Team</p>
        </div>
    </div>
</body>
</html>
                '''
            }
        ]
        
        for template_data in email_templates:
            template, created = NotificationTemplate.objects.get_or_create(
                name=template_data['name'],
                template_type=template_data['template_type'],
                defaults=template_data
            )
            if created:
                self.stdout.write(f'Created email template: {template.display_name}')
            else:
                self.stdout.write(f'Email template already exists: {template.display_name}')
        
        # Create SMS templates
        sms_templates = [
            {
                'name': 'default_sms',
                'display_name': 'Default SMS Template',
                'template_type': 'sms',
                'is_default': True,
                'body_template': '{{ notification.title }}: {{ notification.message }}',
                'available_variables': ['notification.title', 'notification.message', 'user.first_name']
            },
            {
                'name': 'system_alert_sms',
                'display_name': 'System Alert SMS',
                'template_type': 'sms',
                'body_template': 'ALERT: {{ notification.message }} - Timetable System',
                'available_variables': ['notification.message']
            }
        ]
        
        for template_data in sms_templates:
            template, created = NotificationTemplate.objects.get_or_create(
                name=template_data['name'],
                template_type=template_data['template_type'],
                defaults=template_data
            )
            if created:
                self.stdout.write(f'Created SMS template: {template.display_name}')
            else:
                self.stdout.write(f'SMS template already exists: {template.display_name}')
        
        # Create push notification templates
        push_templates = [
            {
                'name': 'default_push',
                'display_name': 'Default Push Template',
                'template_type': 'push',
                'is_default': True,
                'subject_template': '{{ notification.title }}',
                'body_template': '{{ notification.message }}',
                'available_variables': ['notification.title', 'notification.message']
            }
        ]
        
        for template_data in push_templates:
            template, created = NotificationTemplate.objects.get_or_create(
                name=template_data['name'],
                template_type=template_data['template_type'],
                defaults=template_data
            )
            if created:
                self.stdout.write(f'Created push template: {template.display_name}')
            else:
                self.stdout.write(f'Push template already exists: {template.display_name}')
        
        self.stdout.write(self.style.SUCCESS('Successfully set up notification types and templates!'))
