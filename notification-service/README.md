# Notification Service

A comprehensive microservice for managing and delivering notifications across multiple channels including email, SMS, push notifications, and real-time WebSocket connections.

## Overview

This service handles all notification requirements for the timetable management system, providing:

- **Multi-Channel Delivery** (Email, SMS, Push, WebSocket)
- **Template Management** with Jinja2 templating
- **Async Processing** with Celery for scalable delivery
- **Real-time Notifications** via WebSocket connections
- **Delivery Tracking** and analytics
- **Rate Limiting** and retry mechanisms
- **Integration** with external services (SendGrid, Twilio, FCM)

## Technology Stack

- **Framework**: Django REST Framework
- **Database**: MongoDB (shared with other services)
- **Task Queue**: Celery with Redis
- **Real-time**: Django Channels with Redis
- **Authentication**: JWT tokens from Authentication Service
- **Email Providers**: Django SMTP / SendGrid
- **SMS Provider**: Twilio
- **Push Notifications**: Firebase Cloud Messaging (FCM)
- **Templates**: Jinja2 templating engine
- **API Documentation**: Swagger/OpenAPI

## Key Features

### 📧 Email Notifications
- HTML and text email support
- SendGrid integration for reliable delivery
- Template-based email composition
- Delivery tracking and bounce handling
- Rate limiting and retry mechanisms

### 📱 SMS Notifications
- Twilio integration for SMS delivery
- Template-based SMS composition
- International SMS support
- Delivery status tracking
- Cost-effective rate limiting

### 🔔 Push Notifications
- Firebase Cloud Messaging (FCM) integration
- Cross-platform push notifications (iOS, Android, Web)
- Rich notification support with actions
- Device token management
- Delivery analytics

### 🌐 Real-time WebSocket Notifications
- Live in-app notifications
- User-specific notification channels
- Real-time unread count updates
- Connection management and heartbeat
- System-wide broadcast capabilities

### 📋 Template Management
- Jinja2-based template system
- Multi-language template support
- Template testing and preview
- Variable documentation
- Default template fallbacks

### 📊 Analytics & Tracking
- Delivery success/failure tracking
- User engagement metrics (read rates, click rates)
- Channel performance analytics
- Notification history and logs
- Rate limiting statistics

## API Endpoints

### Notifications
- `GET /api/notifications/` - List notifications
- `POST /api/notifications/create/` - Create notification
- `POST /api/notifications/create-bulk/` - Create bulk notifications
- `GET /api/notifications/unread/` - Get unread notifications
- `POST /api/notifications/{id}/mark_as_read/` - Mark as read
- `GET /api/notifications/stats/` - Get user statistics

### Notification Types
- `GET /api/notifications/types/` - List notification types
- `POST /api/notifications/types/` - Create notification type
- `PUT /api/notifications/types/{id}/` - Update notification type

### Templates
- `GET /api/notifications/templates/` - List templates
- `POST /api/notifications/templates/` - Create template
- `POST /api/notifications/templates/{id}/test_render/` - Test template

### Email Service
- `POST /api/email/send/` - Send single email
- `POST /api/email/send-bulk/` - Send bulk email
- `POST /api/email/send-template/` - Send template email
- `POST /api/email/test/` - Send test email

### Batches
- `GET /api/notifications/batches/` - List notification batches
- `POST /api/notifications/batches/` - Create batch
- `POST /api/notifications/batches/{id}/process/` - Process batch

## WebSocket Endpoints

### User Notifications
```
ws://localhost:8005/ws/notifications/{user_id}/
```

**Messages:**
- `new_notification` - New notification received
- `notification_updated` - Notification status changed
- `unread_count_update` - Unread count changed
- `mark_as_read` - Mark notification as read
- `get_notifications` - Get recent notifications

### System Broadcasts
```
ws://localhost:8005/ws/broadcasts/
```

**Messages:**
- `system_broadcast` - System-wide announcements
- `maintenance_alert` - Maintenance notifications

## Environment Variables

```bash
# Core Settings
DEBUG=False
SECRET_KEY=your-secret-key
ALLOWED_HOSTS=localhost,notification-service

# Database
MONGODB_URI=*********************************************************
MONGODB_DB_NAME=timetable_system

# Redis & Celery
REDIS_URL=redis://localhost:6379/0
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# Service URLs
USER_SERVICE_URL=http://user-service:8002
TIMETABLE_SERVICE_URL=http://timetable-service:8004

# Email Configuration
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
DEFAULT_FROM_EMAIL=<EMAIL>

# SendGrid (Optional)
USE_SENDGRID=False
SENDGRID_API_KEY=your-sendgrid-api-key

# SMS (Twilio)
USE_SMS=False
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=+**********

# Push Notifications (FCM)
USE_PUSH_NOTIFICATIONS=False
FCM_SERVER_KEY=your-fcm-server-key

# Notification Settings
NOTIFICATION_RETENTION_DAYS=90
MAX_NOTIFICATIONS_PER_USER=1000
EMAIL_RATE_LIMIT_PER_HOUR=100
SMS_RATE_LIMIT_PER_HOUR=20

# WebSocket Settings
WEBSOCKET_HEARTBEAT_INTERVAL=30
WEBSOCKET_MAX_CONNECTIONS_PER_USER=5

# Site Configuration
SITE_URL=https://timetable.example.com
```

## Docker Deployment

### Build Image
```bash
docker build -t timetable/notification-service:latest .
```

### Run Service
```bash
docker run -d \
  --name notification-service \
  -p 8005:8005 \
  --env-file .env \
  timetable/notification-service:latest
```

### Run Celery Worker
```bash
docker run -d \
  --name notification-celery-worker \
  --env-file .env \
  timetable/notification-service:latest \
  celery -A notification_service worker --loglevel=info
```

## Kubernetes Deployment

```bash
# Deploy service
kubectl apply -f k8s/notification-service/

# Check status
kubectl get pods -n timetable-system -l app=notification-service
```

## Development Setup

### Prerequisites
- Python 3.11+
- MongoDB
- Redis
- Node.js (for frontend integration)

### Installation
```bash
# Clone repository
git clone <repository-url>
cd notification-service

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Set up environment
cp .env.example .env
# Edit .env with your configuration

# Run migrations
python manage.py migrate

# Set up default notification types and templates
python manage.py setup_notifications

# Create superuser
python manage.py createsuperuser

# Start development server
python manage.py runserver 0.0.0.0:8005

# In another terminal, start Celery worker
celery -A notification_service worker --loglevel=info

# In another terminal, start Celery beat
celery -A notification_service beat --loglevel=info
```

## Usage Examples

### Creating a Notification
```python
from notifications.services import NotificationService

# Create single notification
result = NotificationService.create_notification(
    notification_type_name='timetable_generated',
    recipient_id='user-uuid',
    title='Your Timetable is Ready',
    message='Your timetable has been generated successfully.',
    data={
        'timetable_id': 'timetable-uuid',
        'click_action': '/timetables/timetable-uuid'
    }
)
```

### Sending Timetable Updates
```python
# Send timetable update notifications
NotificationService.send_timetable_update_notification(
    timetable_id='timetable-uuid',
    user_ids=['user1-uuid', 'user2-uuid'],
    update_type='published'
)
```

### Creating Batch Notifications
```python
# Create batch notification
result = NotificationService.create_batch_notification(
    notification_type_name='preference_reminder',
    recipient_filter={'role': 'lecturer'},
    title_template='Preference Submission Reminder',
    message_template='Please submit your preferences by {{ deadline_date }}.',
    data_template={'deadline_date': '2024-01-15'}
)
```

## Testing

### Run Tests
```bash
# Run all tests
python manage.py test

# Run specific app tests
python manage.py test notifications
python manage.py test email_service
python manage.py test websockets

# Run with coverage
pytest --cov=. --cov-report=html
```

### Test Email Service
```bash
# Send test email
curl -X POST http://localhost:8005/api/email/test/ \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"to_email": "<EMAIL>"}'
```

### Test WebSocket Connection
```javascript
// Connect to WebSocket
const ws = new WebSocket('ws://localhost:8005/ws/notifications/');

ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    console.log('Notification:', data);
};

// Mark notification as read
ws.send(JSON.stringify({
    type: 'mark_as_read',
    notification_id: 'notification-uuid'
}));
```

## Integration with Other Services

### Timetable Service Integration
```python
# Called when timetable is generated
from notifications.services import NotificationService

NotificationService.send_timetable_update_notification(
    timetable_id=timetable.id,
    user_ids=[lecturer.id for lecturer in affected_lecturers],
    update_type='generated'
)
```

### Preference Service Integration
```python
# Send preference reminders
from datetime import datetime, timedelta

deadline = datetime.now() + timedelta(days=7)
NotificationService.send_preference_reminder(
    user_ids=lecturer_ids,
    deadline_date=deadline
)
```

## Performance Considerations

### Celery Configuration
- **Worker Concurrency**: 2-4 per worker for I/O bound tasks
- **Queue Separation**: Separate queues for email, SMS, push, WebSocket
- **Rate Limiting**: Respect provider rate limits
- **Retry Logic**: Exponential backoff for failed deliveries

### WebSocket Optimization
- **Connection Pooling**: Limit connections per user
- **Heartbeat**: Regular ping/pong for connection health
- **Message Batching**: Batch notifications for efficiency

### Database Optimization
- **Indexes**: On frequently queried fields (recipient, status, created_at)
- **Cleanup**: Regular cleanup of old notifications
- **Archiving**: Archive old notifications to separate collection

## Monitoring and Logging

### Health Checks
```bash
curl http://localhost:8005/api/health/
```

### Metrics
- Notification delivery rates by channel
- Template rendering performance
- WebSocket connection counts
- Queue processing times

### Logs
- Application logs: `/app/logs/notification_service.log`
- Celery logs: Separate worker logs
- Delivery tracking: Success/failure logs

## Security Considerations

- **Authentication**: JWT token validation
- **Rate Limiting**: Per-user and per-service limits
- **Input Validation**: Template and data validation
- **Privacy**: User preference respect
- **Encryption**: Sensitive data encryption

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
